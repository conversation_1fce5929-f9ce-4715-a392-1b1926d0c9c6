using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.Requests;
using CMS.WebApi.Models.Responses;
using CMS.WebApi.Services.Interfaces;
using CMS.WebApi.Security;

namespace CMS.WebApi.Controllers;

[ApiController]
[Route("api/collection-components")]
[Authorize(Policy = AuthorizationPolicies.JwtOrApiToken)]
[Tags("Collection Component Management")]
public class CollectionComponentController : ControllerBase
{
    private readonly ICollectionComponentService _collectionComponentService;
    private readonly ILogger<CollectionComponentController> _logger;

    public CollectionComponentController(
        ICollectionComponentService collectionComponentService,
        ILogger<CollectionComponentController> logger)
    {
        _collectionComponentService = collectionComponentService;
        _logger = logger;
    }

    /// <summary>
    /// Get all collection components
    /// </summary>
    /// <returns>List of all collection components</returns>
    [HttpGet("getAll")]
    [ProducesResponseType(typeof(IEnumerable<CollectionComponent>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    public async Task<ActionResult<IEnumerable<CollectionComponent>>> GetAllCollectionComponents()
    {
        try
        {
            _logger.LogInformation("All collection components requested");

            var components = await _collectionComponentService.GetAllCollectionComponentsAsync();

            if (!components.Any())
            {
                return NoContent();
            }

            return Ok(components);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all collection components");
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving collection components",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get collection component by ID
    /// </summary>
    /// <param name="id">Collection component ID</param>
    /// <returns>Collection component</returns>
    [HttpGet("getById/{id}")]
    [ProducesResponseType(typeof(CollectionComponent), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<CollectionComponent>> GetCollectionComponentById(int id)
    {
        try
        {
            var component = await _collectionComponentService.GetCollectionComponentByIdAsync(id);

            if (component == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Collection component with ID {id} not found",
                    Path = Request.Path
                });
            }

            return Ok(component);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get collection component by ID: {Id}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving the collection component",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get collection components by collection ID
    /// </summary>
    /// <param name="collectionId">Collection ID</param>
    /// <returns>List of collection components</returns>
    [HttpGet("getByCollectionId/{collectionId}")]
    [ProducesResponseType(typeof(IEnumerable<CollectionComponent>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    public async Task<ActionResult<IEnumerable<CollectionComponent>>> GetCollectionComponentsByCollectionId(int collectionId)
    {
        try
        {
            _logger.LogInformation("Collection components requested for collection ID: {CollectionId}", collectionId);

            var components = await _collectionComponentService.GetCollectionComponentsByCollectionIdAsync(collectionId);

            if (!components.Any())
            {
                _logger.LogInformation("No collection components found for collection ID: {CollectionId}", collectionId);
                return NoContent();
            }

            _logger.LogInformation("Found {Count} collection components for collection ID: {CollectionId}",
                components.Count(), collectionId);
            return Ok(components);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid collection ID: {CollectionId}", collectionId);
            return BadRequest(new ErrorResponse
            {
                Status = 400,
                Error = "Bad Request",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving collection components for collection ID: {CollectionId}", collectionId);
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving collection components",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Create a new collection component
    /// </summary>
    /// <param name="request">Collection component details</param>
    /// <returns>Created collection component</returns>
    [HttpPost("create")]
    [ProducesResponseType(typeof(CollectionComponent), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<CollectionComponent>> CreateCollectionComponent([FromBody] CollectionComponentCreateRequest request)
    {
        try
        {
            _logger.LogInformation("Collection component creation requested");

            // Map request to entity
            var collectionComponent = new CollectionComponent
            {
                CollectionId = request.CollectionId > 0 ? request.CollectionId : request.Collection?.Id ?? 0,
                ComponentId = request.ComponentId > 0 ? request.ComponentId : request.Component?.Id ?? 0,
                DisplayPreference = request.DisplayPreference,
                IsRepeatable = request.IsRepeatable,
                MinRepeatOccurrences = request.MinRepeatOccurrences,
                MaxRepeatOccurrences = request.MaxRepeatOccurrences,
                IsActive = request.IsActive,
                Name = request.Name,
                DisplayName = request.DisplayName,
                AdditionalInfo = request.AdditionalInfo,
                AdditionalInfoImage = request.AdditionalInfoImage
            };

            // Validate that we have required IDs
            if (collectionComponent.CollectionId <= 0)
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = "Collection ID is required",
                    Path = Request.Path
                });
            }

            if (collectionComponent.ComponentId <= 0)
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = "Component ID is required",
                    Path = Request.Path
                });
            }

            var createdComponent = await _collectionComponentService.CreateCollectionComponentAsync(collectionComponent);

            _logger.LogInformation("Collection component created successfully: {Id}", createdComponent.Id);

            return CreatedAtAction(
                nameof(GetCollectionComponentById),
                new { id = createdComponent.Id },
                createdComponent);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid collection component data");
            return BadRequest(new ErrorResponse
            {
                Status = 400,
                Error = "Bad Request",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating collection component");
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while creating the collection component",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Update an existing collection component
    /// </summary>
    /// <param name="id">Collection component ID</param>
    /// <param name="component">Updated collection component details</param>
    /// <returns>Updated collection component</returns>
    [HttpPut("update/{id}")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status501NotImplemented)]
    public async Task<ActionResult<object>> UpdateCollectionComponent(int id, [FromBody] object component)
    {
        try
        {
            _logger.LogInformation("Collection component update requested for ID: {ComponentId}", id);
            
            return StatusCode(StatusCodes.Status501NotImplemented, new ErrorResponse
            {
                Status = 501,
                Error = "Not Implemented",
                Message = "Collection component update is not yet implemented",
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating collection component: {ComponentId}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while updating the collection component",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Update display preference for collection component
    /// </summary>
    /// <param name="id">Collection component ID</param>
    /// <param name="request">Display preference update request</param>
    /// <returns>Updated collection component</returns>
    [HttpPut("updateDisplayPreference/{id}")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<object>> UpdateDisplayPreference(int id, [FromBody] DisplayPreferenceRequest request)
    {
        try
        {
            _logger.LogInformation("Display preference update requested for collection component ID: {ComponentId} with preference: {DisplayPreference}",
                id, request.DisplayPreference);

            var updatedComponent = await _collectionComponentService.UpdateDisplayPreferenceAsync(id, request.DisplayPreference);

            _logger.LogInformation("Successfully updated display preference for collection component {ComponentId}", id);

            return Ok(new
            {
                success = true,
                message = "Display preference updated successfully",
                data = new
                {
                    id = updatedComponent.Id,
                    displayPreference = updatedComponent.DisplayPreference,
                    modifiedAt = updatedComponent.ModifiedAt
                }
            });
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Collection component not found: {ComponentId}", id);
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating display preference for collection component: {ComponentId}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while updating display preference",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Delete a collection component
    /// </summary>
    /// <param name="id">Collection component ID</param>
    /// <returns>No content</returns>
    [HttpDelete("deleteById/{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status501NotImplemented)]
    public async Task<ActionResult> DeleteCollectionComponent(int id)
    {
        try
        {
            _logger.LogInformation("Collection component deletion requested for ID: {ComponentId}", id);
            
            return StatusCode(StatusCodes.Status501NotImplemented, new ErrorResponse
            {
                Status = 501,
                Error = "Not Implemented",
                Message = "Collection component deletion is not yet implemented",
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting collection component: {ComponentId}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while deleting the collection component",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get next available ID for collection component
    /// </summary>
    /// <returns>Next available ID</returns>
    [HttpGet("getNextId")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    public async Task<ActionResult<object>> GetNextId()
    {
        try
        {
            // Return a simple incremental ID for now
            var nextId = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            return Ok(new { nextId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting next available ID for collection component");
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while getting next available ID",
                Path = Request.Path
            });
        }
    }
}

public class DisplayPreferenceRequest
{
    public int DisplayPreference { get; set; }
}
