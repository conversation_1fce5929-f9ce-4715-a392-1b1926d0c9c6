using Microsoft.EntityFrameworkCore;
using CMS.WebApi.Data;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.Requests;
using CMS.WebApi.Services.Interfaces;

namespace CMS.WebApi.Services.Implementations;

public class ComponentFieldService : IComponentFieldService
{
    private readonly CmsDbContext _context;
    private readonly ILogger<ComponentFieldService> _logger;
    private readonly IComponentFieldConfigService _componentFieldConfigService;

    public ComponentFieldService(
        CmsDbContext context,
        ILogger<ComponentFieldService> logger,
        IComponentFieldConfigService componentFieldConfigService)
    {
        _context = context;
        _logger = logger;
        _componentFieldConfigService = componentFieldConfigService;
    }

    public async Task<ComponentField?> GetComponentFieldByIdAsync(int id)
    {
        return await _context.ComponentFields
            .Include(cf => cf.Component)
            .Include(cf => cf.FieldType)
            .Include(cf => cf.Configs)
            .ThenInclude(c => c.FieldConfig)
            .FirstOrDefaultAsync(cf => cf.Id == id);
    }

    public async Task<IEnumerable<ComponentField>> GetAllComponentFieldsAsync()
    {
        return await _context.ComponentFields
            .Include(cf => cf.Component)
            .Include(cf => cf.FieldType)
            .Include(cf => cf.Configs)
            .ThenInclude(c => c.FieldConfig)
            .OrderBy(cf => cf.ComponentId)
            .ThenBy(cf => cf.DisplayPreference)
            .ToListAsync();
    }

    public async Task<IEnumerable<ComponentField>> GetComponentFieldsByComponentIdAsync(int componentId)
    {
        // Verify component exists
        var componentExists = await _context.ComponentListings
            .AnyAsync(c => c.Id == componentId);

        if (!componentExists)
        {
            throw new ArgumentException($"Component not found with id: {componentId}");
        }

        return await _context.ComponentFields
            .Include(cf => cf.FieldType)
            .Include(cf => cf.Configs)
            .ThenInclude(c => c.FieldConfig)
            .Where(cf => cf.ComponentId == componentId)
            .OrderBy(cf => cf.DisplayPreference ?? int.MaxValue)
            .ToListAsync();
    }

    public async Task<ComponentField> CreateComponentFieldAsync(ComponentField componentField)
    {
        // Validate field type
        if (componentField.FieldTypeId <= 0)
        {
            throw new ArgumentException("Field type is required");
        }

        // Verify field type exists
        var fieldType = await _context.FieldTypes
            .FirstOrDefaultAsync(ft => ft.Id == componentField.FieldTypeId);

        if (fieldType == null)
        {
            throw new ArgumentException($"Field type with ID {componentField.FieldTypeId} not found");
        }

        // Verify component exists
        if (componentField.ComponentId <= 0)
        {
            throw new ArgumentException("Component is required");
        }

        var component = await _context.ComponentListings
            .FirstOrDefaultAsync(c => c.Id == componentField.ComponentId);

        if (component == null)
        {
            throw new ArgumentException($"Component with ID {componentField.ComponentId} not found");
        }

        // Set display preference if not provided
        if (!componentField.DisplayPreference.HasValue)
        {
            var maxDisplayPreference = await _context.ComponentFields
                .Where(cf => cf.ComponentId == componentField.ComponentId)
                .MaxAsync(cf => (int?)cf.DisplayPreference);

            componentField.DisplayPreference = (maxDisplayPreference ?? 0) + 10;
        }

        _logger.LogDebug("Setting display preference to {DisplayPreference} for component {ComponentId}",
            componentField.DisplayPreference, componentField.ComponentId);

        // Set audit fields
        componentField.CreatedAt = DateTime.UtcNow;

        _context.ComponentFields.Add(componentField);
        await _context.SaveChangesAsync();

        _logger.LogInformation("Component field created successfully: ID {Id}, Component {ComponentId}, FieldType {FieldTypeId}",
            componentField.Id, componentField.ComponentId, componentField.FieldTypeId);

        return componentField;
    }

    public async Task<ComponentField> UpdateComponentFieldAsync(int id, ComponentField componentField)
    {
        var existingField = await _context.ComponentFields
            .FirstOrDefaultAsync(cf => cf.Id == id);

        if (existingField == null)
        {
            throw new ArgumentException($"Component field with ID {id} not found");
        }

        // Update properties
        existingField.FieldTypeId = componentField.FieldTypeId;
        existingField.DisplayPreference = componentField.DisplayPreference;
        existingField.DependentOnId = componentField.DependentOnId;
        existingField.AdditionalInformation = componentField.AdditionalInformation;
        existingField.ModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        _logger.LogInformation("Component field updated successfully: ID {Id}", id);

        return existingField;
    }

    public async Task<ComponentField> UpdateComponentFieldWithConfigsAsync(int id, UpdateComponentFieldRequest request)
    {
        _logger.LogInformation("Updating component field with configurations: ID {FieldId}", id);

        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            // 1. Update the component field
            var existingField = await _context.ComponentFields
                .Include(cf => cf.Configs)
                .FirstOrDefaultAsync(cf => cf.Id == id);

            if (existingField == null)
            {
                throw new ArgumentException($"Component field with ID {id} not found");
            }

            // Update basic field properties
            existingField.FieldTypeId = request.FieldTypeId;
            existingField.DisplayPreference = request.DisplayPreference;
            existingField.DependentOnId = request.DependentOnId;
            existingField.AdditionalInformation = request.AdditionalInformation;
            existingField.ModifiedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            // 2. Update configurations if provided
            if (request.Configs.Any())
            {
                _logger.LogInformation("Updating {ConfigCount} configurations for field ID: {FieldId}",
                    request.Configs.Count, id);

                // Get existing configurations
                var existingConfigs = existingField.Configs.ToList();
                _logger.LogInformation("Found {ExistingCount} existing configurations", existingConfigs.Count);

                // Remove all existing configurations first
                if (existingConfigs.Any())
                {
                    _logger.LogInformation("Removing {Count} existing configurations", existingConfigs.Count);
                    _context.ComponentFieldConfigs.RemoveRange(existingConfigs);
                    await _context.SaveChangesAsync(); // Save the removal first
                }

                // Add new configurations
                var newConfigs = request.Configs.Select(config => new ComponentFieldConfig
                {
                    ComponentFieldId = id,
                    FieldConfigId = config.FieldConfigId,
                    ConfigValue = config.ConfigValue,
                    IsActive = config.IsActive,
                    CreatedAt = DateTime.UtcNow
                }).ToList();

                foreach (var config in newConfigs)
                {
                    _logger.LogInformation("Adding config: ComponentFieldId={ComponentFieldId}, FieldConfigId={FieldConfigId}, ConfigValue={ConfigValue}",
                        config.ComponentFieldId, config.FieldConfigId, config.ConfigValue);
                }

                await _context.ComponentFieldConfigs.AddRangeAsync(newConfigs);
                await _context.SaveChangesAsync(); // Save the additions

                _logger.LogInformation("Successfully updated {ConfigCount} field configurations", newConfigs.Count);
            }
            else
            {
                _logger.LogInformation("No configurations provided for field ID: {FieldId}", id);
            }

            await transaction.CommitAsync();

            _logger.LogInformation("Component field with {ConfigCount} configurations updated successfully: ID {Id}",
                request.Configs.Count, id);

            // Return the complete field with configurations
            return await GetComponentFieldByIdAsync(id) ?? existingField;
        }
        catch (Exception)
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    public async Task DeleteComponentFieldAsync(int id)
    {
        var field = await _context.ComponentFields
            .FirstOrDefaultAsync(cf => cf.Id == id);

        if (field == null)
        {
            throw new ArgumentException($"Component field with ID {id} not found");
        }

        _context.ComponentFields.Remove(field);
        await _context.SaveChangesAsync();

        _logger.LogInformation("Component field deleted successfully: ID {Id}", id);
    }

    public async Task<int> GetNextAvailableIdAsync()
    {
        // Get the maximum ID currently in use
        var maxId = await _context.ComponentFields
            .MaxAsync(cf => (int?)cf.Id);

        // If no records exist, start with ID 1, otherwise use max + 1
        return maxId.HasValue ? maxId.Value + 1 : 1;
    }

    public async Task<IEnumerable<ComponentField>> ReorderComponentFieldsAsync(int componentId, List<int> fieldIds)
    {
        // Verify component exists
        var componentExists = await _context.ComponentListings
            .AnyAsync(c => c.Id == componentId);

        if (!componentExists)
        {
            throw new ArgumentException($"Component not found with id: {componentId}");
        }

        // Get all fields for this component
        var fields = await _context.ComponentFields
            .Where(cf => cf.ComponentId == componentId && fieldIds.Contains(cf.Id))
            .ToListAsync();

        // Update display preferences based on the order in fieldIds
        for (int i = 0; i < fieldIds.Count; i++)
        {
            var field = fields.FirstOrDefault(f => f.Id == fieldIds[i]);
            if (field != null)
            {
                field.DisplayPreference = (i + 1) * 10; // 10, 20, 30, etc.
                field.ModifiedAt = DateTime.UtcNow;
            }
        }

        await _context.SaveChangesAsync();

        _logger.LogInformation("Reordered {Count} component fields for component {ComponentId}", 
            fieldIds.Count, componentId);

        // Return the reordered fields
        return await GetComponentFieldsByComponentIdAsync(componentId);
    }

    public async Task<ComponentField> CreateComponentFieldWithConfigsAsync(CreateComponentFieldRequest request)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();

        try
        {
            // 1. Create the main component field
            var componentField = new ComponentField
            {
                ComponentId = request.ComponentId,
                FieldTypeId = request.FieldTypeId,
                DisplayPreference = request.DisplayPreference,
                DependentOnId = request.DependentOnId,
                AdditionalInformation = request.AdditionalInformation,
                CreatedAt = DateTime.UtcNow
            };

            // If no display preference provided, calculate next available
            if (!componentField.DisplayPreference.HasValue)
            {
                var maxDisplayPreference = await _context.ComponentFields
                    .Where(cf => cf.ComponentId == request.ComponentId)
                    .MaxAsync(cf => (int?)cf.DisplayPreference);

                componentField.DisplayPreference = (maxDisplayPreference ?? 0) + 10;
            }

            var createdField = await CreateComponentFieldAsync(componentField);

            // 2. Create field configurations if provided
            if (request.Configurations.Any())
            {
                _logger.LogInformation("Creating {ConfigCount} field configurations for field ID: {FieldId}",
                    request.Configurations.Count, createdField.Id);

                var fieldConfigs = request.Configurations.Select(config => new ComponentFieldConfig
                {
                    ComponentFieldId = createdField.Id,
                    FieldConfigId = config.FieldConfigId,
                    ConfigValue = config.ConfigValue,
                    IsActive = config.IsActive,
                    CreatedAt = DateTime.UtcNow
                }).ToList();

                foreach (var config in fieldConfigs)
                {
                    _logger.LogInformation("Creating config: ComponentFieldId={ComponentFieldId}, FieldConfigId={FieldConfigId}, ConfigValue={ConfigValue}",
                        config.ComponentFieldId, config.FieldConfigId, config.ConfigValue);
                }

                var createdConfigs = await _componentFieldConfigService.CreateComponentFieldConfigsAsync(fieldConfigs);
                _logger.LogInformation("Successfully created {CreatedCount} field configurations", createdConfigs.Count());
            }
            else
            {
                _logger.LogInformation("No configurations provided for field ID: {FieldId}", createdField.Id);
            }

            await transaction.CommitAsync();

            _logger.LogInformation("Component field with {ConfigCount} configurations created successfully: ID {Id}",
                request.Configurations.Count, createdField.Id);

            // Return the complete field with configurations
            return await GetComponentFieldByIdAsync(createdField.Id) ?? createdField;
        }
        catch (Exception)
        {
            await transaction.RollbackAsync();
            throw;
        }
    }
}
