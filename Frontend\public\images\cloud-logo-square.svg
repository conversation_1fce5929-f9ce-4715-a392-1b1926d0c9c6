<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 500 500">
  <rect width="500" height="500" fill="#4F46E5" rx="60" ry="60"/>
  
  <defs>
    <linearGradient id="blue_gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#0066FF"/>
      <stop offset="100%" stop-color="#0099FF"/>
    </linearGradient>
    <linearGradient id="red_gradient" x1="100%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#CC0066"/>
      <stop offset="100%" stop-color="#990033"/>
    </linearGradient>
  </defs>
  
  <!-- Left blue C shape -->
  <path d="M100,250 C100,190 145,150 200,150 C255,150 300,190 300,250 C300,310 255,350 200,350 C145,350 100,310 100,250 Z" 
        fill="url(#blue_gradient)" stroke="none"/>
  
  <!-- Right red P shape -->
  <path d="M200,250 C200,190 245,150 300,150 C355,150 400,190 400,250 C400,310 355,350 300,350 C245,350 200,310 200,250 Z" 
        fill="url(#red_gradient)" stroke="none"/>
  
  <!-- White circle in blue C -->
  <circle cx="150" cy="250" r="15" fill="white"/>
  
  <!-- White circle in red P -->
  <circle cx="350" cy="250" r="15" fill="white"/>
  
  <!-- Center connecting curve -->
  <path d="M200,180 C240,150 260,180 250,250 C240,320 260,350 300,320" 
        stroke="#0099FF" stroke-width="15" fill="none"/>
</svg>
