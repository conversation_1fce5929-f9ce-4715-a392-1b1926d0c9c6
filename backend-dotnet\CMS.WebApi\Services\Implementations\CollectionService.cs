using Microsoft.EntityFrameworkCore;
using CMS.WebApi.Data;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Services.Interfaces;

namespace CMS.WebApi.Services.Implementations;

public class CollectionService : ICollectionService
{
    private readonly CmsDbContext _context;
    private readonly ILogger<CollectionService> _logger;

    public CollectionService(CmsDbContext context, ILogger<CollectionService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<IEnumerable<CollectionListing>> GetAllCollectionsAsync()
    {
        return await _context.CollectionListings
            .Include(c => c.Category)
            .Include(c => c.Fields)
            .ThenInclude(f => f.FieldType)
            .ToListAsync();
    }

    public async Task<CollectionListing?> GetCollectionByIdAsync(int id)
    {
        return await _context.CollectionListings
            .Include(c => c.Category)
            .Include(c => c.Fields)
            .ThenInclude(f => f.FieldType)
            .FirstOrDefaultAsync(c => c.Id == id);
    }

    public async Task<CollectionListing?> GetCollectionByApiIdAsync(string apiId)
    {
        return await _context.CollectionListings
            .Include(c => c.Category)
            .Include(c => c.Fields)
            .ThenInclude(f => f.FieldType)
            .FirstOrDefaultAsync(c => c.CollectionApiId == apiId);
    }

    public async Task<IEnumerable<CollectionListing>> GetAllCollectionsWithDetailsAsync()
    {
        return await _context.CollectionListings
            .Include(c => c.Category)
            .Include(c => c.Components)
                .ThenInclude(cc => cc.Component)
                    .ThenInclude(comp => comp.Fields)
                        .ThenInclude(f => f.FieldType)
            .Include(c => c.Components)
                .ThenInclude(cc => cc.Component)
                    .ThenInclude(comp => comp.Fields)
                        .ThenInclude(f => f.Configs)
                            .ThenInclude(config => config.FieldConfig)
                                .ThenInclude(fc => fc.ConfigType)
            .Include(c => c.Components)
                .ThenInclude(cc => cc.Component)
                    .ThenInclude(comp => comp.ChildComponents)
                        .ThenInclude(child => child.ChildComponent)
            .Include(c => c.Fields)
                .ThenInclude(f => f.FieldType)
            .Include(c => c.Fields)
                .ThenInclude(f => f.Configs)
                    .ThenInclude(config => config.FieldConfig)
                        .ThenInclude(fc => fc.ConfigType)
            .ToListAsync();
    }

    public async Task<CollectionListing?> GetCollectionByIdWithDetailsAsync(int id)
    {
        return await _context.CollectionListings
            .Include(c => c.Category)
            .Include(c => c.Components)
                .ThenInclude(cc => cc.Component)
                    .ThenInclude(comp => comp.Fields)
                        .ThenInclude(f => f.FieldType)
            .Include(c => c.Components)
                .ThenInclude(cc => cc.Component)
                    .ThenInclude(comp => comp.Fields)
                        .ThenInclude(f => f.Configs)
                            .ThenInclude(config => config.FieldConfig)
                                .ThenInclude(fc => fc.ConfigType)
            .Include(c => c.Components)
                .ThenInclude(cc => cc.Component)
                    .ThenInclude(comp => comp.ChildComponents)
                        .ThenInclude(child => child.ChildComponent)
            .Include(c => c.Fields)
                .ThenInclude(f => f.FieldType)
            .Include(c => c.Fields)
                .ThenInclude(f => f.Configs)
                    .ThenInclude(config => config.FieldConfig)
                        .ThenInclude(fc => fc.ConfigType)
            .FirstOrDefaultAsync(c => c.Id == id);
    }

    public async Task<CollectionListing?> GetCollectionByApiIdWithDetailsAsync(string apiId)
    {
        return await _context.CollectionListings
            .Include(c => c.Category)
            .Include(c => c.Components)
                .ThenInclude(cc => cc.Component)
                    .ThenInclude(comp => comp.Fields)
                        .ThenInclude(f => f.FieldType)
            .Include(c => c.Components)
                .ThenInclude(cc => cc.Component)
                    .ThenInclude(comp => comp.Fields)
                        .ThenInclude(f => f.Configs)
                            .ThenInclude(config => config.FieldConfig)
                                .ThenInclude(fc => fc.ConfigType)
            .Include(c => c.Components)
                .ThenInclude(cc => cc.Component)
                    .ThenInclude(comp => comp.ChildComponents)
                        .ThenInclude(child => child.ChildComponent)
            .Include(c => c.Fields)
                .ThenInclude(f => f.FieldType)
            .Include(c => c.Fields)
                .ThenInclude(f => f.Configs)
                    .ThenInclude(config => config.FieldConfig)
                        .ThenInclude(fc => fc.ConfigType)
            .FirstOrDefaultAsync(c => c.CollectionApiId == apiId);
    }

    public async Task<IEnumerable<CollectionListing>> GetCollectionsByCategoryAsync(int categoryId)
    {
        return await _context.CollectionListings
            .Include(c => c.Category)
            .Include(c => c.Fields)
            .ThenInclude(f => f.FieldType)
            .Where(c => c.CategoryId == categoryId)
            .ToListAsync();
    }

    public async Task<CollectionListing> CreateCollectionAsync(CollectionListing collection)
    {
        collection.CreatedAt = DateTime.UtcNow;
        _context.CollectionListings.Add(collection);
        await _context.SaveChangesAsync();
        
        _logger.LogInformation("Collection created successfully: {CollectionName}", collection.CollectionName);
        return collection;
    }

    public async Task<CollectionListing> UpdateCollectionAsync(int id, CollectionListing collection)
    {
        var existingCollection = await _context.CollectionListings.FindAsync(id);
        if (existingCollection == null)
            throw new ArgumentException($"Collection with ID {id} not found");

        existingCollection.CollectionName = collection.CollectionName;
        existingCollection.CollectionDesc = collection.CollectionDesc;
        existingCollection.AdditionalInformation = collection.AdditionalInformation;
        existingCollection.DisclaimerText = collection.DisclaimerText;
        existingCollection.CollectionApiId = collection.CollectionApiId;
        existingCollection.CategoryId = collection.CategoryId;
        existingCollection.ModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        
        _logger.LogInformation("Collection updated successfully: {CollectionId}", id);
        return existingCollection;
    }

    public async Task DeleteCollectionAsync(int id)
    {
        var collection = await _context.CollectionListings.FindAsync(id);
        if (collection != null)
        {
            _context.CollectionListings.Remove(collection);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Collection deleted successfully: {CollectionId}", id);
        }
    }

    public async Task<bool> CollectionExistsAsync(string apiId)
    {
        return await _context.CollectionListings.AnyAsync(c => c.CollectionApiId == apiId);
    }

    public async Task<IEnumerable<CollectionField>> GetCollectionFieldsAsync(int collectionId)
    {
        return await _context.CollectionFields
            .Include(f => f.FieldType)
            .Include(f => f.Configs)
            .ThenInclude(c => c.FieldConfig)
            .Where(f => f.CollectionId == collectionId)
            .OrderBy(f => f.DisplayPreference)
            .ToListAsync();
    }

    public async Task<CollectionField> AddFieldToCollectionAsync(int collectionId, CollectionField field)
    {
        field.CollectionId = collectionId;
        field.CreatedAt = DateTime.UtcNow;
        
        _context.CollectionFields.Add(field);
        await _context.SaveChangesAsync();
        
        _logger.LogInformation("Field added to collection: {CollectionId}, FieldType: {FieldTypeId}", 
            collectionId, field.FieldTypeId);
        
        return field;
    }

    public async Task RemoveFieldFromCollectionAsync(int collectionId, int fieldId)
    {
        var field = await _context.CollectionFields
            .FirstOrDefaultAsync(f => f.Id == fieldId && f.CollectionId == collectionId);
        
        if (field != null)
        {
            _context.CollectionFields.Remove(field);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Field removed from collection: {CollectionId}, FieldId: {FieldId}", 
                collectionId, fieldId);
        }
    }
}
