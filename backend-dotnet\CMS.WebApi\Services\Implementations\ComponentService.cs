using Microsoft.EntityFrameworkCore;
using CMS.WebApi.Data;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Services.Interfaces;

namespace CMS.WebApi.Services.Implementations;

public class ComponentService : IComponentService
{
    private readonly CmsDbContext _context;
    private readonly ILogger<ComponentService> _logger;

    public ComponentService(CmsDbContext context, ILogger<ComponentService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<IEnumerable<ComponentListing>> GetAllComponentsAsync()
    {
        return await _context.ComponentListings
            .Include(c => c.Fields)
            .ThenInclude(f => f.FieldType)
            .ToListAsync();
    }

    public async Task<IEnumerable<ComponentListing>> GetActiveComponentsAsync()
    {
        return await _context.ComponentListings
            .Include(c => c.Fields)
            .ThenInclude(f => f.FieldType)
            .Where(c => c.IsActive == true)
            .ToListAsync();
    }

    public async Task<ComponentListing?> GetComponentByIdAsync(int id)
    {
        return await _context.ComponentListings
            .Include(c => c.Fields)
            .ThenInclude(f => f.FieldType)
            .FirstOrDefaultAsync(c => c.Id == id);
    }

    public async Task<ComponentListing?> GetComponentByNameAsync(string name)
    {
        return await _context.ComponentListings
            .Include(c => c.Fields)
            .ThenInclude(f => f.FieldType)
            .FirstOrDefaultAsync(c => c.ComponentName == name);
    }

    public async Task<ComponentListing?> GetComponentByApiIdAsync(string apiId)
    {
        return await _context.ComponentListings
            .Include(c => c.Fields)
            .ThenInclude(f => f.FieldType)
            .FirstOrDefaultAsync(c => c.ComponentApiId == apiId);
    }

    public async Task<ComponentListing> CreateComponentAsync(ComponentListing component)
    {
        component.CreatedAt = DateTime.UtcNow;
        _context.ComponentListings.Add(component);
        await _context.SaveChangesAsync();
        
        _logger.LogInformation("Component created successfully: {ComponentName}", component.ComponentName);
        return component;
    }

    public async Task<ComponentListing> UpdateComponentAsync(int id, ComponentListing component)
    {
        var existingComponent = await _context.ComponentListings.FindAsync(id);
        if (existingComponent == null)
            throw new ArgumentException($"Component with ID {id} not found");

        existingComponent.ComponentName = component.ComponentName;
        existingComponent.ComponentDisplayName = component.ComponentDisplayName;
        existingComponent.ComponentApiId = component.ComponentApiId;
        existingComponent.IsActive = component.IsActive;
        existingComponent.GetUrl = component.GetUrl;
        existingComponent.PostUrl = component.PostUrl;
        existingComponent.UpdateUrl = component.UpdateUrl;
        existingComponent.ModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        
        _logger.LogInformation("Component updated successfully: {ComponentId}", id);
        return existingComponent;
    }

    public async Task DeleteComponentAsync(int id)
    {
        var component = await _context.ComponentListings.FindAsync(id);
        if (component != null)
        {
            _context.ComponentListings.Remove(component);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Component deleted successfully: {ComponentId}", id);
        }
    }

    public async Task<bool> ComponentExistsAsync(string name)
    {
        return await _context.ComponentListings.AnyAsync(c => c.ComponentName == name);
    }

    public async Task<IEnumerable<ComponentField>> GetComponentFieldsAsync(int componentId)
    {
        return await _context.ComponentFields
            .Include(f => f.FieldType)
            .Include(f => f.Configs)
            .ThenInclude(c => c.FieldConfig)
            .Where(f => f.ComponentId == componentId)
            .OrderBy(f => f.DisplayPreference)
            .ToListAsync();
    }

    public async Task<ComponentField> AddFieldToComponentAsync(int componentId, ComponentField field)
    {
        field.ComponentId = componentId;
        field.CreatedAt = DateTime.UtcNow;
        
        _context.ComponentFields.Add(field);
        await _context.SaveChangesAsync();
        
        _logger.LogInformation("Field added to component: {ComponentId}, FieldType: {FieldTypeId}", 
            componentId, field.FieldTypeId);
        
        return field;
    }

    public async Task RemoveFieldFromComponentAsync(int componentId, int fieldId)
    {
        var field = await _context.ComponentFields
            .FirstOrDefaultAsync(f => f.Id == fieldId && f.ComponentId == componentId);
        
        if (field != null)
        {
            _context.ComponentFields.Remove(field);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Field removed from component: {ComponentId}, FieldId: {FieldId}", 
                componentId, fieldId);
        }
    }
}
