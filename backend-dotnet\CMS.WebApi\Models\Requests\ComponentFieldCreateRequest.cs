using System.ComponentModel.DataAnnotations;

namespace CMS.WebApi.Models.Requests;

public class ComponentFieldCreateRequest
{
    /// <summary>
    /// Component ID
    /// </summary>
    [Required]
    public int ComponentId { get; set; }

    /// <summary>
    /// Field type ID
    /// </summary>
    [Required]
    public int FieldTypeId { get; set; }

    /// <summary>
    /// Display preference for ordering
    /// </summary>
    public int? DisplayPreference { get; set; }

    /// <summary>
    /// ID of the field this depends on
    /// </summary>
    public int? DependentOnId { get; set; }

    /// <summary>
    /// Additional information about the field
    /// </summary>
    public string? AdditionalInformation { get; set; }

    /// <summary>
    /// Component reference (alternative to ComponentId)
    /// </summary>
    public ComponentReference? Component { get; set; }

    /// <summary>
    /// Field type reference (alternative to FieldTypeId)
    /// </summary>
    public FieldTypeReference? FieldType { get; set; }
}
