using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.Responses;
using CMS.WebApi.Services.Interfaces;
using CMS.WebApi.Security;

namespace CMS.WebApi.Controllers;

[ApiController]
[Route("api/media")]
[Authorize(Policy = AuthorizationPolicies.JwtOrApiToken)]
[Tags("Media Management")]
public class MediaController : ControllerBase
{
    private readonly IMediaService _mediaService;
    private readonly ILogger<MediaController> _logger;
    private readonly IConfiguration _configuration;

    public MediaController(IMediaService mediaService, ILogger<MediaController> logger, IConfiguration configuration)
    {
        _mediaService = mediaService;
        _logger = logger;
        _configuration = configuration;
    }

    /// <summary>
    /// Upload a file to the media library
    /// </summary>
    /// <param name="file">The file to upload</param>
    /// <param name="folderId">Optional folder ID to store the file in</param>
    /// <param name="description">Optional description of the file</param>
    /// <param name="altText">Optional alt text for the file</param>
    /// <param name="isPublic">Whether the file is publicly accessible</param>
    /// <returns>Uploaded media details</returns>
    [HttpPost("upload")]
    [ProducesResponseType(typeof(Media), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<Media>> UploadFile(
        IFormFile file,
        [FromForm] int? folderId = null,
        [FromForm] string? description = null,
        [FromForm] string? altText = null,
        [FromForm] bool isPublic = false)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = "File is required",
                    Path = Request.Path
                });
            }

            var media = await _mediaService.UploadFileAsync(file, folderId);
            
            // Update additional properties if provided
            if (!string.IsNullOrEmpty(description) || !string.IsNullOrEmpty(altText) || isPublic)
            {
                media.Description = description;
                media.AltText = altText;
                media.IsPublic = isPublic;
                media = await _mediaService.UpdateMediaAsync(media.Id, media);
            }

            _logger.LogInformation("File uploaded successfully: {FileName}", media.FileName);
            return Ok(media);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to upload file");
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while uploading the file",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get all media with pagination
    /// </summary>
    /// <param name="page">Page number (0-based)</param>
    /// <param name="size">Page size</param>
    /// <param name="sort">Sort field</param>
    /// <param name="direction">Sort direction (ASC/DESC)</param>
    /// <returns>Paginated list of media</returns>
    [HttpGet("assets")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    public async Task<ActionResult<object>> GetAllMedia(
        [FromQuery] int page = 0,
        [FromQuery] int size = 20,
        [FromQuery] string sort = "CreatedAt",
        [FromQuery] string direction = "DESC")
    {
        try
        {
            var media = await _mediaService.GetAllMediaAsync();
            
            // Simple pagination implementation
            var totalCount = media.Count();
            var pagedMedia = media
                .Skip(page * size)
                .Take(size)
                .ToList();

            var result = new
            {
                Content = pagedMedia,
                TotalElements = totalCount,
                TotalPages = (int)Math.Ceiling((double)totalCount / size),
                Size = size,
                Number = page,
                First = page == 0,
                Last = page >= (int)Math.Ceiling((double)totalCount / size) - 1,
                NumberOfElements = pagedMedia.Count
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get media");
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving media",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get media by folder
    /// </summary>
    /// <param name="folderId">Folder ID (null for root folder assets)</param>
    /// <returns>List of media in the folder</returns>
    [HttpGet("assets/folder/{folderId?}")]
    [ProducesResponseType(typeof(IEnumerable<Media>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<Media>>> GetMediaByFolder(int? folderId = null)
    {
        try
        {
            var media = await _mediaService.GetMediaByFolderAsync(folderId);
            _logger.LogInformation("Retrieved {Count} media items for folder {FolderId}", media.Count(), folderId);
            return Ok(media);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve media for folder: {FolderId}", folderId);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving media",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get media by ID
    /// </summary>
    /// <param name="id">Media ID</param>
    /// <returns>Media details</returns>
    [HttpGet("assets/{id}")]
    [ProducesResponseType(typeof(Media), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<Media>> GetMediaById(int id)
    {
        var media = await _mediaService.GetMediaByIdAsync(id);
        if (media == null)
        {
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = $"Media with ID {id} not found",
                Path = Request.Path
            });
        }
        return Ok(media);
    }

    /// <summary>
    /// Update media details
    /// </summary>
    /// <param name="id">Media ID</param>
    /// <param name="media">Updated media details</param>
    /// <returns>Updated media</returns>
    [HttpPut("assets/{id}")]
    [ProducesResponseType(typeof(Media), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<Media>> UpdateMedia(int id, [FromBody] Media media)
    {
        try
        {
            var updatedMedia = await _mediaService.UpdateMediaAsync(id, media);
            _logger.LogInformation("Media updated successfully: {MediaId}", id);
            return Ok(updatedMedia);
        }
        catch (ArgumentException ex)
        {
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update media: {MediaId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while updating the media",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Delete media
    /// </summary>
    /// <param name="id">Media ID</param>
    /// <returns>No content</returns>
    [HttpDelete("assets/{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult> DeleteMedia(int id)
    {
        try
        {
            var media = await _mediaService.GetMediaByIdAsync(id);
            if (media == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Media with ID {id} not found",
                    Path = Request.Path
                });
            }

            await _mediaService.DeleteMediaAsync(id);
            _logger.LogInformation("Media deleted successfully: {MediaId}", id);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete media: {MediaId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while deleting the media",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Serve media file
    /// </summary>
    /// <param name="year">Year</param>
    /// <param name="month">Month</param>
    /// <param name="fileName">File name</param>
    /// <returns>File content</returns>
    [HttpGet("files/{year}/{month}/{fileName}")]
    [AllowAnonymous]
    public async Task<ActionResult> GetMediaFile(string year, string month, string fileName)
    {
        try
        {
            // This is a simplified implementation
            // In a real application, you'd want to validate the path and check permissions
            var filePath = Path.Combine("media-uploads", year, month, fileName);
            
            if (!System.IO.File.Exists(filePath))
            {
                return NotFound();
            }

            var fileBytes = await System.IO.File.ReadAllBytesAsync(filePath);
            var contentType = GetContentType(fileName);
            
            return File(fileBytes, contentType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to serve media file: {FileName}", fileName);
            return NotFound();
        }
    }

    /// <summary>
    /// Download media file by ID
    /// </summary>
    /// <param name="id">Media ID</param>
    /// <returns>File download</returns>
    [HttpGet("assets/{id}/download")]
    [AllowAnonymous]
    public async Task<ActionResult> DownloadMediaFile(int id)
    {
        try
        {
            var media = await _mediaService.GetMediaByIdAsync(id);
            if (media == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Media with ID {id} not found",
                    Path = Request.Path
                });
            }

            if (!System.IO.File.Exists(media.FilePath))
            {
                _logger.LogWarning("Media file not found on disk: {FilePath}", media.FilePath);
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = "Media file not found on disk",
                    Path = Request.Path
                });
            }

            var fileBytes = await System.IO.File.ReadAllBytesAsync(media.FilePath);
            var contentType = GetContentType(media.FileName);

            // Set headers for download
            Response.Headers.Add("Content-Disposition", $"attachment; filename=\"{media.OriginalFileName}\"");

            return File(fileBytes, contentType, media.OriginalFileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to download media file: {MediaId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while downloading the file",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get media file content by ID
    /// </summary>
    /// <param name="id">Media ID</param>
    /// <returns>File content</returns>
    [HttpGet("assets/{id}/content")]
    [AllowAnonymous]
    public async Task<ActionResult> GetMediaContent(int id)
    {
        try
        {
            var media = await _mediaService.GetMediaByIdAsync(id);
            if (media == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Media with ID {id} not found",
                    Path = Request.Path
                });
            }

            if (!System.IO.File.Exists(media.FilePath))
            {
                _logger.LogWarning("Media file not found on disk: {FilePath}", media.FilePath);
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = "Media file not found on disk",
                    Path = Request.Path
                });
            }

            var fileBytes = await System.IO.File.ReadAllBytesAsync(media.FilePath);
            var contentType = GetContentType(media.FileName);

            return File(fileBytes, contentType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get media content: {MediaId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while accessing the file content",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Replace media file content while keeping the same URL and metadata
    /// </summary>
    /// <param name="id">Media ID</param>
    /// <param name="file">New file to replace the existing one</param>
    /// <returns>Updated media details</returns>
    [HttpPost("assets/{id}/replace")]
    [ProducesResponseType(typeof(Media), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<Media>> ReplaceMediaFile(int id, IFormFile file)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = "File is required",
                    Path = Request.Path
                });
            }

            _logger.LogInformation("Replacing media file for ID: {MediaId} with new file: {FileName}", id, file.FileName);

            var updatedMedia = await _mediaService.ReplaceFileAsync(id, file);

            _logger.LogInformation("Media file replaced successfully: {MediaId}", id);

            return Ok(updatedMedia);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Media not found for replacement: {MediaId}", id);
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to replace media file: {MediaId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while replacing the file",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Generate share link for media file
    /// </summary>
    /// <param name="id">Media ID</param>
    /// <returns>Share link</returns>
    [HttpPost("assets/{id}/share")]
    [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<string>> GenerateShareLink(int id)
    {
        try
        {
            var media = await _mediaService.GetMediaByIdAsync(id);
            if (media == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Media with ID {id} not found",
                    Path = Request.Path
                });
            }

            // Generate share URL using the existing share token or create a new one
            var baseUrl = _configuration["FileUpload:BaseUrl"] ?? "http://localhost:5000";
            var shareUrl = $"{baseUrl}/api/media/share/{media.ShareToken}";

            _logger.LogInformation("Generated share link for media {MediaId}: {ShareUrl}", id, shareUrl);

            return Ok(shareUrl);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate share link for media: {MediaId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while generating the share link",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Access media file via share token
    /// </summary>
    /// <param name="shareToken">Share token</param>
    /// <returns>File content</returns>
    [HttpGet("share/{shareToken}")]
    [AllowAnonymous]
    public async Task<ActionResult> GetSharedMediaFile(string shareToken)
    {
        try
        {
            var media = await _mediaService.GetMediaByShareTokenAsync(shareToken);
            if (media == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = "Shared media not found or link has expired",
                    Path = Request.Path
                });
            }

            if (!System.IO.File.Exists(media.FilePath))
            {
                _logger.LogWarning("Shared media file not found on disk: {FilePath}", media.FilePath);
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = "Media file not found on disk",
                    Path = Request.Path
                });
            }

            var fileBytes = await System.IO.File.ReadAllBytesAsync(media.FilePath);
            var contentType = GetContentType(media.FileName);

            return File(fileBytes, contentType, media.OriginalFileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to access shared media file: {ShareToken}", shareToken);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while accessing the shared file",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Update PublicUrl for existing media records (temporary endpoint)
    /// </summary>
    /// <returns>Update result</returns>
    [HttpPost("update-public-urls")]
    public async Task<ActionResult> UpdatePublicUrls()
    {
        try
        {
            var updatedCount = await _mediaService.UpdatePublicUrlsAsync();
            return Ok(new { message = $"Updated {updatedCount} media records with PublicUrl" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update public URLs");
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while updating public URLs",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Serve media file by ID
    /// </summary>
    /// <param name="id">Media ID</param>
    /// <returns>File content</returns>
    [HttpGet("{id}/file")]
    [AllowAnonymous]
    public async Task<ActionResult> GetMediaFileById(int id)
    {
        try
        {
            var media = await _mediaService.GetMediaByIdAsync(id);
            if (media == null)
            {
                return NotFound();
            }

            if (!System.IO.File.Exists(media.FilePath))
            {
                _logger.LogWarning("Media file not found on disk: {FilePath}", media.FilePath);
                return NotFound();
            }

            var fileBytes = await System.IO.File.ReadAllBytesAsync(media.FilePath);
            var contentType = GetContentType(media.FileName);

            return File(fileBytes, contentType, media.OriginalFileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to serve media file: {MediaId}", id);
            return NotFound();
        }
    }

    private static string GetContentType(string fileName)
    {
        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        return extension switch
        {
            ".jpg" or ".jpeg" => "image/jpeg",
            ".png" => "image/png",
            ".gif" => "image/gif",
            ".pdf" => "application/pdf",
            ".txt" => "text/plain",
            ".doc" => "application/msword",
            ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            _ => "application/octet-stream"
        };
    }
}
