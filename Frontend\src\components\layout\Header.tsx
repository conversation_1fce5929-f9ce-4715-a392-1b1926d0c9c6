import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { LogOut, User, Database } from 'lucide-react';
import { useAuthStore } from '@/lib/store';
import { authApi } from '@/lib/api';
import { toast } from 'sonner';
import { useTenant } from '@/components/tenant/TenantProvider';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { ThemeToggle } from '@/components/theme/ThemeToggle';

export function Header() {
  const { user, logout } = useAuthStore();
  const { currentTenant, isDefaultTenant, setCurrentTenant } = useTenant();
  const navigate = useNavigate();
  const location = useLocation();

  // Navigation items for page title
  const navItems = [
    {
      title: 'Dashboard',
      path: '/dashboard',
    },
    {
      title: 'Collections',
      path: '/content-types',
    },
    {
      title: 'Components',
      path: '/components',
    },
    {
      title: 'Media Library',
      path: '/media-library',
    },
    {
      title: 'Settings',
      path: '/settings',
    },
  ];

  // Handle logout
  const handleLogout = async () => {
    console.log('🚪 Logout button clicked - starting logout process');

    try {
      // Call the backend logout endpoint (but don't wait for it if it fails)
      console.log('📡 Calling backend logout endpoint...');
      await authApi.logout();
      console.log('✅ Backend logout successful');
    } catch (error) {
      console.error('❌ Backend logout failed, but continuing with local cleanup:', error);
    }

    try {
      // Clear all authentication data
      console.log('🧹 Starting local data cleanup...');

      // 1. Clear tenant context first
      if (setCurrentTenant) {
        setCurrentTenant(null);
        console.log('✅ Tenant context cleared');
      }

      // 2. Clear Zustand store
      logout();
      console.log('✅ Auth store cleared');

      // 3. Clear localStorage completely
      localStorage.removeItem('cms_token');
      localStorage.removeItem('auth-storage'); // Zustand persist storage
      console.log('✅ Core localStorage items removed');

      // 4. Clear sessionStorage as well (in case anything is stored there)
      sessionStorage.clear();
      console.log('✅ SessionStorage cleared');

      // 5. Clear any cached data in localStorage that might contain auth info
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.includes('auth') || key.includes('token') || key.includes('user') || key.includes('tenant'))) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key));
      console.log('✅ Additional auth-related keys cleared:', keysToRemove);

      console.log('🎉 All authentication and tenant data cleared successfully');

      // Show success message
      toast.info('You have been logged out');

      // Force a page reload to clear any cached state and redirect to login
      console.log('🔄 Redirecting to login page...');
      window.location.href = '/login';

    } catch (error) {
      console.error('❌ Error during logout cleanup:', error);
      // Even if there's an error, try to redirect
      window.location.href = '/login';
    }
  };

  return (
    <header className="sticky top-0 z-10 flex h-14 items-center justify-between bg-gradient-to-r from-[#6366f1] via-[#8b5cf6] to-[#a855f7] dark:from-[#4c1d95] dark:via-[#6b21a8] dark:to-[#7c2d12] backdrop-blur-sm px-6 text-white border-b border-white/20 dark:border-white/10 shadow-lg shadow-purple-500/20 dark:shadow-purple-900/40 transition-all duration-300">
      <div className="flex items-center">
        <SidebarTrigger className="text-white hover:bg-white/10 dark:hover:bg-white/20 mr-2 transition-colors duration-200" />
        <span className="font-medium dark-text-glow">
          {navItems.find((item) => location.pathname.includes(item.path))?.title || 'Dashboard'}
        </span>
      </div>
      <div className="flex items-center gap-4">
        <ThemeToggle />

        {/* Tenant information */}
        {!isDefaultTenant && currentTenant && (
          <div className="flex items-center bg-white/20 dark:bg-white/10 backdrop-blur-sm px-3 py-1 rounded-md border border-white/30 dark:border-white/20 dark-glass transition-all duration-300 hover:bg-white/30 dark:hover:bg-white/20">
            <Database className="mr-2 h-4 w-4" />
            <span className="font-medium text-sm">{currentTenant}</span>
          </div>
        )}

        {/* User information */}
        <div className="flex items-center dark-hover-lift px-2 py-1 rounded-md transition-all duration-300">
          <User className="mr-2 h-5 w-5" />
          <span className="font-medium">{user?.username || 'User'}</span>
        </div>

        {/* Logout button */}
        <button
          onClick={(e) => {
            e.preventDefault();
            console.log('🔘 Logout button clicked!');
            handleLogout();
          }}
          className="flex items-center gap-2 rounded-md bg-white/90 dark:bg-white/10 px-4 py-1.5 text-purple-600 dark:text-purple-300 font-medium hover:bg-white dark:hover:bg-white/20 hover:text-purple-700 dark:hover:text-purple-200 transition-all duration-300 hover:shadow-lg hover:shadow-white/25 dark:hover:shadow-purple-500/25 hover:scale-105 button-ripple dark-border-glow"
          type="button"
        >
          <LogOut className="h-4 w-4" />
          <span>Logout</span>
        </button>
      </div>
    </header>
  );
}
