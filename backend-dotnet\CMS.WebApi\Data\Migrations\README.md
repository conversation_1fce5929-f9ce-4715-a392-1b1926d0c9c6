# Database Migrations for .NET Backend

This directory contains database migration files for the CMS .NET backend.

## Files

### DatabaseSetup.sql
This is the main database setup file that creates all tables, sequences, and inserts initial data. It's executed automatically by Entity Framework when the application starts. This approach is similar to the Java backend's `database_setup.sql` file.

**Features:**
- Creates all necessary database sequences
- Creates core tables (clients, tenants, users, etc.)
- Creates ASP.NET Identity tables
- Creates field types and configuration tables
- Creates media-related tables
- Inserts initial data (default tenant, roles, field types, config types)
- Uses `ON CONFLICT` clauses to make the script idempotent



## How It Works

1. When the application starts, Entity Framework ensures the database is created
2. The `DatabaseSetup.sql` file is automatically executed by Entity Framework migrations
3. Any pending Entity Framework migrations are applied
4. The database is ready with all tables, sequences, and initial data

## Benefits of SQL File Approach

1. **Simplicity**: No complex service layer needed
2. **Performance**: Direct SQL execution is fast
3. **Maintainability**: All database setup in one place
4. **Consistency**: Similar to Java backend approach
5. **Idempotent**: Can be run multiple times safely using `IF NOT EXISTS` and `ON CONFLICT` clauses
6. **Version Control**: Easy to track changes to database structure

## Adding New Tables or Data

To add new tables or initial data:

1. Edit `DatabaseSetup.sql`
2. Add your table creation statements in the appropriate section
3. Add any initial data with `ON CONFLICT` clauses for idempotency
4. Test the changes by running the application

## Database Schema

The SQL script creates the following main table groups:

- **Core Tables**: clients, tenants
- **Identity Tables**: ASP.NET Identity tables for users, roles, claims
- **Field System**: field_types, config_types, field_configs
- **Media System**: media, media_folders
- **API Security**: api_tokens

## Initial Data

The script inserts the following initial data:

- Default tenant (id: 1, schema: 'public')
- Default roles (Admin, User, Editor)
- Field types (text, number, date, image, etc.)
- Config types (validation, display, behavior, etc.)

## Troubleshooting

If database initialization fails:

1. Check the application logs for specific error messages
2. Verify PostgreSQL connection string in `appsettings.json`
3. Ensure PostgreSQL server is running
4. Check database permissions
5. Verify SQL syntax in `DatabaseSetup.sql`

The application will continue to start even if initialization fails (if `ContinueOnError` is true), but some features may not work properly without the initial data.
