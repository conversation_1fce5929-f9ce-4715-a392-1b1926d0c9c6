using System.ComponentModel.DataAnnotations;

namespace CMS.WebApi.Models.Entities;

public class ConfigType : BaseEntity
{
    public int Id { get; set; }

    [Required(ErrorMessage = "Config type name is required")]
    [StringLength(255)]
    public string ConfigTypeName { get; set; } = string.Empty;

    public string? ConfigTypeDesc { get; set; }

    [StringLength(255)]
    public string? DisplayName { get; set; }

    public string? AdditionalInfo { get; set; }

    public string? DisclaimerText { get; set; }

    public string? PlaceholderText { get; set; }

    public bool IsActive { get; set; } = true;

    public ICollection<FieldConfig> FieldConfigs { get; set; } = new List<FieldConfig>();
}
