using System.ComponentModel.DataAnnotations;

namespace CMS.WebApi.Models.Entities;

public class ApiToken : BaseEntity
{
    public long Id { get; set; }

    [Required(ErrorMessage = "Token name is required")]
    [StringLength(50)]
    public string Name { get; set; } = string.Empty;

    [Required(ErrorMessage = "Token value is required")]
    [StringLength(255)]
    public string TokenValue { get; set; } = string.Empty;

    [StringLength(200)]
    public string? Description { get; set; }

    [Required]
    public DateTime ExpiresAt { get; set; }

    public DateTime? LastUsedAt { get; set; }

    public bool IsActive { get; set; } = true;

    [Required]
    public long UserId { get; set; }
    public User User { get; set; } = null!;
}
