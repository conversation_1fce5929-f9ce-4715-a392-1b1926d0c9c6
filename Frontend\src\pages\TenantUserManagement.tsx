import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Users, Plus, Search, Eye, EyeOff, Database, UserPlus, Info } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { toast as sonnerToast } from 'sonner';
import { authApi } from '@/lib/api';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

// Validation schemas
const addUserSchema = z.object({
  username: z.string()
    .min(3, 'Username must be at least 3 characters')
    .max(50, 'Username must not exceed 50 characters')
    .regex(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores'),
  email: z.string()
    .email('Please enter a valid email address')
    .max(100, 'Email must not exceed 100 characters'),
  password: z.string()
    .min(6, 'Password must be at least 6 characters')
    .max(120, 'Password must not exceed 120 characters'),
  tenantSchemaName: z.string()
    .max(50, 'Tenant schema name must not exceed 50 characters')
    .regex(/^[a-zA-Z0-9_]*$/, 'Tenant schema name can only contain letters, numbers, and underscores')
    .optional(),
});

const listUsersSchema = z.object({
  tenantSchemaName: z.string()
    .min(1, 'Tenant schema name is required')
    .max(50, 'Tenant schema name must not exceed 50 characters')
    .regex(/^[a-zA-Z0-9_]+$/, 'Tenant schema name can only contain letters, numbers, and underscores'),
});

type AddUserFormValues = z.infer<typeof addUserSchema>;
type ListUsersFormValues = z.infer<typeof listUsersSchema>;

interface User {
  id: number;
  username: string;
  email: string;
  isActive: boolean;
  createdAt: string;
  modifiedAt: string;
}

export default function TenantUserManagement() {
  const { toast } = useToast();
  const [isAddingUser, setIsAddingUser] = useState(false);
  const [isListingUsers, setIsListingUsers] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [users, setUsers] = useState<User[]>([]);
  const [currentTenant, setCurrentTenant] = useState<string>('');

  // Form for adding users
  const addUserForm = useForm<AddUserFormValues>({
    resolver: zodResolver(addUserSchema),
    defaultValues: {
      username: '',
      email: '',
      password: '',
      tenantSchemaName: '',
    },
  });

  // Form for listing users
  const listUsersForm = useForm<ListUsersFormValues>({
    resolver: zodResolver(listUsersSchema),
    defaultValues: {
      tenantSchemaName: '',
    },
  });

  // Helper function to extract domain from email
  const extractDomainFromEmail = (email: string): string => {
    if (!email.includes('@')) return '';
    const domain = email.split('@')[1];
    return domain.toLowerCase().replace(/\./g, '_').replace(/[^a-z0-9_]/g, '_');
  };

  // Watch email field to auto-suggest tenant schema
  const watchedEmail = addUserForm.watch('email');
  const suggestedTenant = watchedEmail ? extractDomainFromEmail(watchedEmail) : '';

  // Handle adding user to tenant
  const onAddUser = async (data: AddUserFormValues) => {
    setIsAddingUser(true);
    console.log('Adding user to tenant:', data);

    try {
      const response = await authApi.addUserToTenant(
        data.username,
        data.email,
        data.password,
        data.tenantSchemaName || '' // Send empty string if not provided, backend will derive from email
      );

      console.log('Add user response:', response.data);

      const targetTenant = data.tenantSchemaName || suggestedTenant;
      sonnerToast.success('User Added Successfully', {
        description: `User '${data.username}' has been added to tenant '${targetTenant}'`,
      });

      // Reset form
      addUserForm.reset();

      // If we're currently viewing users for this tenant, refresh the list
      if (currentTenant === targetTenant) {
        await refreshUserList(targetTenant);
      }

    } catch (error: any) {
      console.error('Error adding user to tenant:', error);

      let errorMessage = 'Failed to add user to tenant';
      if (error.response?.data) {
        errorMessage = typeof error.response.data === 'string'
          ? error.response.data
          : error.response.data.message || errorMessage;
      }

      sonnerToast.error('Error Adding User', {
        description: errorMessage,
      });

      toast({
        variant: "destructive",
        title: "Error",
        description: errorMessage,
      });
    } finally {
      setIsAddingUser(false);
    }
  };

  // Handle listing users in tenant
  const onListUsers = async (data: ListUsersFormValues) => {
    setIsListingUsers(true);
    console.log('Listing users in tenant:', data.tenantSchemaName);

    try {
      const response = await authApi.listUsersInTenant(data.tenantSchemaName);
      console.log('List users response:', response.data);

      setUsers(response.data || []);
      setCurrentTenant(data.tenantSchemaName);

      sonnerToast.success('Users Retrieved', {
        description: `Found ${response.data?.length || 0} users in tenant '${data.tenantSchemaName}'`,
      });

    } catch (error: any) {
      console.error('Error listing users in tenant:', error);

      let errorMessage = 'Failed to list users in tenant';
      if (error.response?.data) {
        errorMessage = typeof error.response.data === 'string'
          ? error.response.data
          : error.response.data.message || errorMessage;
      }

      sonnerToast.error('Error Listing Users', {
        description: errorMessage,
      });

      toast({
        variant: "destructive",
        title: "Error",
        description: errorMessage,
      });

      setUsers([]);
      setCurrentTenant('');
    } finally {
      setIsListingUsers(false);
    }
  };

  // Refresh user list for current tenant
  const refreshUserList = async (tenantSchemaName: string) => {
    try {
      const response = await authApi.listUsersInTenant(tenantSchemaName);
      setUsers(response.data || []);
    } catch (error) {
      console.error('Error refreshing user list:', error);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString;
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center space-x-2">
        <Database className="h-8 w-8 text-blue-600" />
        <div>
          <h1 className="text-3xl font-bold">Tenant User Management</h1>
          <p className="text-muted-foreground">
            Manage users within tenant schemas using email domain-based assignment
          </p>
        </div>
      </div>

      {/* Email Domain Info Alert */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>New Feature:</strong> Tenant schemas are now automatically derived from email domains.
          For example, <code><EMAIL></code> → <code>acme_com</code> schema.
          You can still specify a custom tenant schema if needed.
        </AlertDescription>
      </Alert>

      <Tabs defaultValue="add-user" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="add-user" className="flex items-center space-x-2">
            <UserPlus className="h-4 w-4" />
            <span>Add User to Tenant</span>
          </TabsTrigger>
          <TabsTrigger value="list-users" className="flex items-center space-x-2">
            <Users className="h-4 w-4" />
            <span>List Users in Tenant</span>
          </TabsTrigger>
        </TabsList>

        {/* Add User Tab */}
        <TabsContent value="add-user">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Plus className="h-5 w-5" />
                <span>Add User to Tenant</span>
              </CardTitle>
              <CardDescription>
                Add a new user to a tenant schema. The tenant will be automatically derived from the email domain,
                or you can specify a custom tenant schema name.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...addUserForm}>
                <form onSubmit={addUserForm.handleSubmit(onAddUser)} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={addUserForm.control}
                      name="username"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Username</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="john_doe"
                              {...field}
                              disabled={isAddingUser}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={addUserForm.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email</FormLabel>
                          <FormControl>
                            <Input
                              type="email"
                              placeholder="<EMAIL>"
                              {...field}
                              disabled={isAddingUser}
                            />
                          </FormControl>
                          <FormMessage />
                          {suggestedTenant && (
                            <p className="text-xs text-muted-foreground">
                              Will be added to tenant: <code>{suggestedTenant}</code>
                            </p>
                          )}
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={addUserForm.control}
                      name="password"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Password</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Input
                                type={showPassword ? "text" : "password"}
                                placeholder="Enter password"
                                {...field}
                                disabled={isAddingUser}
                              />
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                onClick={() => setShowPassword(!showPassword)}
                                disabled={isAddingUser}
                              >
                                {showPassword ? (
                                  <EyeOff className="h-4 w-4" />
                                ) : (
                                  <Eye className="h-4 w-4" />
                                )}
                              </Button>
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={addUserForm.control}
                      name="tenantSchemaName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tenant Schema Name (Optional)</FormLabel>
                          <FormControl>
                            <Input
                              placeholder={suggestedTenant || "acme_com"}
                              {...field}
                              disabled={isAddingUser}
                            />
                          </FormControl>
                          <FormMessage />
                          <p className="text-xs text-muted-foreground">
                            Leave empty to auto-derive from email domain
                          </p>
                        </FormItem>
                      )}
                    />
                  </div>

                  <Button type="submit" disabled={isAddingUser} className="w-full">
                    {isAddingUser ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                        Adding User...
                      </>
                    ) : (
                      <>
                        <Plus className="h-4 w-4 mr-2" />
                        Add User to Tenant
                      </>
                    )}
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* List Users Tab */}
        <TabsContent value="list-users">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Search className="h-5 w-5" />
                <span>List Users in Tenant</span>
              </CardTitle>
              <CardDescription>
                View all users in a specific tenant schema
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Form {...listUsersForm}>
                <form onSubmit={listUsersForm.handleSubmit(onListUsers)} className="space-y-4">
                  <div className="flex space-x-2">
                    <FormField
                      control={listUsersForm.control}
                      name="tenantSchemaName"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Tenant Schema Name</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="acme_com"
                              {...field}
                              disabled={isListingUsers}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Button type="submit" disabled={isListingUsers}>
                    {isListingUsers ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                        Loading Users...
                      </>
                    ) : (
                      <>
                        <Search className="h-4 w-4 mr-2" />
                        List Users
                      </>
                    )}
                  </Button>
                </form>
              </Form>

              {/* Users Table */}
              {currentTenant && (
                <>
                  <Separator />
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <h3 className="text-lg font-semibold">Users in Tenant</h3>
                        <Badge variant="secondary">{currentTenant}</Badge>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline">
                          {users.length} user{users.length !== 1 ? 's' : ''}
                        </Badge>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => refreshUserList(currentTenant)}
                          disabled={isListingUsers}
                        >
                          Refresh
                        </Button>
                      </div>
                    </div>

                    {users.length > 0 ? (
                      <div className="border rounded-lg">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>ID</TableHead>
                              <TableHead>Username</TableHead>
                              <TableHead>Email</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead>Created At</TableHead>
                              <TableHead>Modified At</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {users.map((user) => (
                              <TableRow key={user.id}>
                                <TableCell className="font-mono">{user.id}</TableCell>
                                <TableCell className="font-medium">{user.username}</TableCell>
                                <TableCell>{user.email}</TableCell>
                                <TableCell>
                                  <Badge variant={user.isActive ? "default" : "secondary"}>
                                    {user.isActive ? "Active" : "Inactive"}
                                  </Badge>
                                </TableCell>
                                <TableCell className="text-sm text-muted-foreground">
                                  {formatDate(user.createdAt)}
                                </TableCell>
                                <TableCell className="text-sm text-muted-foreground">
                                  {formatDate(user.modifiedAt)}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No users found in this tenant</p>
                      </div>
                    )}
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Usage Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>🚀 Email Domain-Based Tenant Management</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-2">✨ New: Automatic Tenant Assignment</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Email domains automatically determine tenant schemas</li>
                <li>• <code><EMAIL></code> → <code>acme_com</code> schema</li>
                <li>• <code><EMAIL></code> → <code>company_org</code> schema</li>
                <li>• Multiple users from same domain share the same tenant</li>
                <li>• Override with custom tenant schema if needed</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">🔐 User Login Process</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Users login with: <code>username@schema</code></li>
                <li>• Example: <code>john_doe@acme_com</code></li>
                <li>• Use the same password set during creation</li>
                <li>• Each tenant has isolated user data</li>
              </ul>
            </div>
          </div>

          <Separator />

          <div>
            <h4 className="font-semibold mb-2">📝 Example Workflow</h4>
            <div className="bg-muted p-4 rounded-lg text-sm">
              <p><strong>1. Register first user:</strong> <code><EMAIL></code> → Creates <code>acme_com</code> tenant</p>
              <p><strong>2. Add more users:</strong> <code><EMAIL></code>, <code><EMAIL></code> → Auto-added to <code>acme_com</code></p>
              <p><strong>3. Login:</strong> <code>john@acme_com</code> with password</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
