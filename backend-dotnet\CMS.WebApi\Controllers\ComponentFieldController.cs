using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using CMS.WebApi.Data;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.Requests;
using CMS.WebApi.Models.Responses;
using CMS.WebApi.Services.Interfaces;
using CMS.WebApi.Security;

namespace CMS.WebApi.Controllers;

[ApiController]
[Route("api/component-fields")]
[Authorize(Policy = AuthorizationPolicies.JwtOrApiToken)]
[Tags("Component Field Management")]
public class ComponentFieldController : ControllerBase
{
    private readonly IComponentService _componentService;
    private readonly IComponentFieldService _componentFieldService;
    private readonly CmsDbContext _context;
    private readonly ILogger<ComponentFieldController> _logger;

    public ComponentFieldController(
        IComponentService componentService,
        IComponentFieldService componentFieldService,
        CmsDbContext context,
        ILogger<ComponentFieldController> logger)
    {
        _componentService = componentService;
        _componentFieldService = componentFieldService;
        _context = context;
        _logger = logger;
    }

    /// <summary>
    /// Get all component fields
    /// </summary>
    /// <returns>List of all component fields</returns>
    [HttpGet("getAll")]
    [ProducesResponseType(typeof(IEnumerable<ComponentField>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    public async Task<ActionResult<IEnumerable<ComponentField>>> GetAllComponentFields()
    {
        try
        {
            var fields = await _componentFieldService.GetAllComponentFieldsAsync();

            if (!fields.Any())
            {
                return NoContent();
            }

            _logger.LogInformation("Retrieved {Count} component fields", fields.Count());
            return Ok(fields);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all component fields");
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving component fields",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get component fields by component ID
    /// </summary>
    /// <param name="componentId">Component ID</param>
    /// <returns>List of component fields for the specified component</returns>
    [HttpGet("getByComponentId/{componentId}")]
    [ProducesResponseType(typeof(IEnumerable<ComponentField>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    public async Task<ActionResult<IEnumerable<ComponentField>>> GetComponentFieldsByComponentId(int componentId)
    {
        try
        {
            var fields = await _componentService.GetComponentFieldsAsync(componentId);
            
            if (!fields.Any())
            {
                _logger.LogInformation("No fields found for component ID: {ComponentId}", componentId);
                return NoContent();
            }

            _logger.LogInformation("Retrieved {Count} fields for component ID: {ComponentId}", fields.Count(), componentId);
            return Ok(fields);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving component fields for component ID: {ComponentId}", componentId);
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving component fields",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Create a new component field
    /// </summary>
    /// <param name="request">Component field details</param>
    /// <returns>Created component field</returns>
    [HttpPost("create")]
    [ProducesResponseType(typeof(ComponentField), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ComponentField>> CreateComponentField([FromBody] ComponentFieldCreateRequest request)
    {
        try
        {
            _logger.LogInformation("Component field creation requested");

            // Map request to entity
            var componentField = new ComponentField
            {
                ComponentId = request.ComponentId > 0 ? request.ComponentId : request.Component?.Id ?? 0,
                FieldTypeId = request.FieldTypeId > 0 ? request.FieldTypeId : request.FieldType?.Id ?? 0,
                DisplayPreference = request.DisplayPreference,
                DependentOnId = request.DependentOnId,
                AdditionalInformation = request.AdditionalInformation
            };

            // Validate that we have required IDs
            if (componentField.ComponentId <= 0)
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = "Component ID is required",
                    Path = Request.Path
                });
            }

            if (componentField.FieldTypeId <= 0)
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = "Field Type ID is required",
                    Path = Request.Path
                });
            }

            var createdField = await _componentFieldService.CreateComponentFieldAsync(componentField);

            _logger.LogInformation("Component field created successfully: {Id}", createdField.Id);

            return CreatedAtAction(
                nameof(GetComponentFieldById),
                new { id = createdField.Id },
                createdField);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid component field data");
            return BadRequest(new ErrorResponse
            {
                Status = 400,
                Error = "Bad Request",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating component field");
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while creating the component field",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get component field by ID
    /// </summary>
    /// <param name="id">Component field ID</param>
    /// <returns>Component field</returns>
    [HttpGet("getById/{id}")]
    [ProducesResponseType(typeof(ComponentField), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ComponentField>> GetComponentFieldById(int id)
    {
        try
        {
            var field = await _componentFieldService.GetComponentFieldByIdAsync(id);

            if (field == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Component field with ID {id} not found",
                    Path = Request.Path
                });
            }

            return Ok(field);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get component field by ID: {Id}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving the component field",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Update an existing component field
    /// </summary>
    /// <param name="id">Component field ID</param>
    /// <param name="request">Updated component field details</param>
    /// <returns>Updated component field</returns>
    [HttpPut("update/{id}")]
    [ProducesResponseType(typeof(ComponentField), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ComponentField>> UpdateComponentField(int id, [FromBody] UpdateComponentFieldRequest request)
    {
        try
        {
            _logger.LogInformation("Updating component field {FieldId} with data: {@Request}", id, request);

            // Map request to entity
            var componentField = new ComponentField
            {
                ComponentId = request.ComponentId > 0 ? request.ComponentId : request.Component?.Id ?? 0,
                FieldTypeId = request.FieldTypeId > 0 ? request.FieldTypeId : request.FieldType?.Id ?? 0,
                DisplayPreference = request.DisplayPreference,
                DependentOnId = request.DependentOnId,
                AdditionalInformation = request.AdditionalInformation
            };

            _logger.LogInformation("Mapped component field: ComponentId={ComponentId}, FieldTypeId={FieldTypeId}",
                componentField.ComponentId, componentField.FieldTypeId);

            // Validate required fields
            if (componentField.FieldTypeId <= 0)
            {
                _logger.LogWarning("Field Type ID validation failed: {FieldTypeId}", componentField.FieldTypeId);
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = $"Field Type ID is required. Received: {componentField.FieldTypeId}",
                    Path = Request.Path
                });
            }

            if (componentField.ComponentId <= 0)
            {
                _logger.LogWarning("Component ID validation failed: {ComponentId}", componentField.ComponentId);
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = $"Component ID is required. Received: {componentField.ComponentId}",
                    Path = Request.Path
                });
            }

            ComponentField updatedField;

            // Use the appropriate service method based on whether configurations are provided
            if (request.Configs.Any())
            {
                _logger.LogInformation("Updating component field with {ConfigCount} configurations", request.Configs.Count);
                updatedField = await _componentFieldService.UpdateComponentFieldWithConfigsAsync(id, request);
            }
            else
            {
                _logger.LogInformation("Updating component field without configurations");
                updatedField = await _componentFieldService.UpdateComponentFieldAsync(id, componentField);
            }

            _logger.LogInformation("Component field updated successfully: {FieldId}", id);

            return Ok(updatedField);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Component field not found: {FieldId}", id);
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating component field: {FieldId}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = $"An error occurred while updating the component field: {ex.Message}",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Delete a component field
    /// </summary>
    /// <param name="id">Component field ID</param>
    /// <returns>No content</returns>
    [HttpDelete("deleteById/{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult> DeleteComponentField(int id)
    {
        try
        {
            _logger.LogInformation("Component field deletion requested for ID: {FieldId}", id);

            // Check if the component field exists first
            var existingField = await _componentFieldService.GetComponentFieldByIdAsync(id);
            if (existingField == null)
            {
                _logger.LogInformation("No component field found with ID: {Id}", id);
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"No component field found with id: {id}",
                    Path = Request.Path
                });
            }

            // Delete the component field
            await _componentFieldService.DeleteComponentFieldAsync(id);

            _logger.LogInformation("Component field deleted successfully: {Id}", id);

            // Return 204 No Content (proper HTTP response for successful deletion)
            return NoContent();
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Component field not found: {Id}", id);
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = $"No component field found with id: {id}",
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting component field: {FieldId}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while deleting the component field",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get next available ID for component field
    /// </summary>
    /// <returns>Next available ID</returns>
    [HttpGet("getNextId")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    public async Task<ActionResult<object>> GetNextId()
    {
        try
        {
            var nextId = await _componentFieldService.GetNextAvailableIdAsync();
            return Ok(new { nextId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting next available ID");
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while getting next available ID",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Reorder component fields
    /// </summary>
    /// <param name="componentId">Component ID</param>
    /// <param name="request">Field IDs in new order</param>
    /// <returns>Updated fields</returns>
    [HttpPost("reorderFields/{componentId}")]
    [ProducesResponseType(typeof(IEnumerable<ComponentField>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<ComponentField>>> ReorderFields(int componentId, [FromBody] ReorderFieldsRequest request)
    {
        try
        {
            _logger.LogInformation("Field reordering requested for component: {ComponentId}", componentId);
            
            return StatusCode(StatusCodes.Status501NotImplemented, new ErrorResponse
            {
                Status = 501,
                Error = "Not Implemented",
                Message = "Field reordering is not yet implemented",
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reordering fields for component: {ComponentId}", componentId);
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while reordering fields",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Create a new component field with configurations
    /// </summary>
    /// <param name="request">Component field creation request with configurations</param>
    /// <returns>Created component field with configurations</returns>
    [HttpPost("create-with-configs")]
    [ProducesResponseType(typeof(ComponentFieldResponse), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ComponentFieldResponse>> CreateComponentFieldWithConfigs([FromBody] CreateComponentFieldRequest request)
    {
        try
        {
            _logger.LogInformation("Creating component field with {ConfigCount} configurations for component: {ComponentId}",
                request.Configurations.Count, request.ComponentId);

            // Log the configurations being sent
            foreach (var config in request.Configurations)
            {
                _logger.LogInformation("Config: FieldConfigId={FieldConfigId}, ConfigValue={ConfigValue}, IsActive={IsActive}",
                    config.FieldConfigId, config.ConfigValue, config.IsActive);
            }

            var createdField = await _componentFieldService.CreateComponentFieldWithConfigsAsync(request);

            // Map to response
            var response = new ComponentFieldResponse
            {
                Id = createdField.Id,
                ComponentId = createdField.ComponentId,
                ComponentName = createdField.Component?.ComponentName ?? "",
                ComponentDisplayName = createdField.Component?.ComponentDisplayName ?? "",
                FieldTypeId = createdField.FieldTypeId,
                FieldTypeName = createdField.FieldType?.FieldTypeName ?? "",
                FieldTypeDisplayName = createdField.FieldType?.DisplayName ?? "",
                DisplayPreference = createdField.DisplayPreference,
                DependentOnId = createdField.DependentOnId,
                AdditionalInformation = createdField.AdditionalInformation,
                Configs = createdField.Configs.Select(c => new ComponentFieldConfigResponse
                {
                    Id = c.Id,
                    ComponentFieldId = c.ComponentFieldId,
                    FieldConfigId = c.FieldConfigId,
                    ConfigName = c.FieldConfig?.ConfigName ?? "",
                    ConfigValue = c.ConfigValue,
                    IsActive = c.IsActive,
                    ValueType = c.FieldConfig?.ValueType,
                    CreatedAt = c.CreatedAt,
                    CreatedBy = c.CreatedBy
                }).ToList(),
                CreatedAt = createdField.CreatedAt,
                CreatedBy = createdField.CreatedBy,
                ModifiedAt = createdField.ModifiedAt,
                ModifiedBy = createdField.ModifiedBy
            };

            _logger.LogInformation("Component field created successfully: ID {Id}", createdField.Id);
            return CreatedAtAction(nameof(GetComponentFieldById), new { id = createdField.Id }, response);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid request for creating component field");
            return BadRequest(new ErrorResponse
            {
                Status = 400,
                Error = "Bad Request",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating component field with configurations");
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while creating the component field",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Debug endpoint to check available field configs
    /// </summary>
    [HttpGet("debug/field-configs")]
    [ProducesResponseType(typeof(IEnumerable<object>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<object>>> GetAvailableFieldConfigs()
    {
        try
        {
            var fieldConfigs = await _context.FieldConfigs
                .Select(fc => new
                {
                    fc.Id,
                    fc.ConfigName,
                    fc.ValueType,
                    fc.IsActive,
                    fc.FieldTypeId,
                    fc.ConfigTypeId
                })
                .ToListAsync();

            return Ok(fieldConfigs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting field configs");
            return StatusCode(500, "Error getting field configs");
        }
    }
}

public class ReorderFieldsRequest
{
    public List<int> FieldIds { get; set; } = new();
}
