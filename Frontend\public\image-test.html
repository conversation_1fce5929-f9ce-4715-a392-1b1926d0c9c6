<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Test Page</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-container {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 8px;
        }
        .image-container {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        img {
            max-width: 100%;
            max-height: 200px;
            object-fit: contain;
        }
        .url-display {
            font-family: monospace;
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            word-break: break-all;
            margin-top: 10px;
        }
        h1 {
            margin-bottom: 20px;
        }
        h2 {
            margin-top: 0;
        }
        .controls {
            margin-bottom: 20px;
        }
        input {
            padding: 8px;
            width: 300px;
        }
        button {
            padding: 8px 16px;
            background-color: #4f46e5;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #4338ca;
        }
        .status {
            margin-top: 10px;
            font-style: italic;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
    </style>
</head>
<body>
    <h1>Image URL Test Page</h1>
    
    <div class="controls">
        <label for="assetId">Asset ID:</label>
        <input type="text" id="assetId" placeholder="Enter asset ID">
        <button onclick="testAllFormats()">Test All URL Formats</button>
        <div id="status" class="status"></div>
    </div>
    
    <div class="test-container">
        <h2>1. Direct Public URL</h2>
        <div class="image-container" id="test1-container">
            <img id="test1-img" alt="Test 1">
        </div>
        <div class="url-display" id="test1-url"></div>
        <div class="status" id="test1-status"></div>
    </div>
    
    <div class="test-container">
        <h2>2. API Content Endpoint</h2>
        <div class="image-container" id="test2-container">
            <img id="test2-img" alt="Test 2">
        </div>
        <div class="url-display" id="test2-url"></div>
        <div class="status" id="test2-status"></div>
    </div>
    
    <div class="test-container">
        <h2>3. Uploads Directory</h2>
        <div class="image-container" id="test3-container">
            <img id="test3-img" alt="Test 3">
        </div>
        <div class="url-display" id="test3-url"></div>
        <div class="status" id="test3-status"></div>
    </div>
    
    <div class="test-container">
        <h2>4. Static Directory</h2>
        <div class="image-container" id="test4-container">
            <img id="test4-img" alt="Test 4">
        </div>
        <div class="url-display" id="test4-url"></div>
        <div class="status" id="test4-status"></div>
    </div>
    
    <script>
        // Function to fetch asset info
        async function fetchAssetInfo(assetId) {
            try {
                document.getElementById('status').textContent = 'Fetching asset info...';
                const response = await fetch(`/api/media/assets/getById/${assetId}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                document.getElementById('status').innerHTML = `<span class="success">Asset found: ${data.originalFileName}</span>`;
                return data;
            } catch (error) {
                document.getElementById('status').innerHTML = `<span class="error">Error fetching asset: ${error.message}</span>`;
                console.error('Error fetching asset:', error);
                return null;
            }
        }
        
        // Function to test an image URL
        function testImageUrl(imgElement, urlElement, statusElement, url) {
            return new Promise((resolve) => {
                urlElement.textContent = url;
                imgElement.src = url;
                
                imgElement.onload = function() {
                    statusElement.innerHTML = `<span class="success">✓ Image loaded successfully</span>`;
                    resolve(true);
                };
                
                imgElement.onerror = function() {
                    statusElement.innerHTML = `<span class="error">✗ Failed to load image</span>`;
                    resolve(false);
                };
            });
        }
        
        // Function to test all URL formats
        async function testAllFormats() {
            const assetId = document.getElementById('assetId').value.trim();
            if (!assetId) {
                document.getElementById('status').innerHTML = `<span class="error">Please enter an asset ID</span>`;
                return;
            }
            
            const asset = await fetchAssetInfo(assetId);
            if (!asset) return;
            
            // Test 1: Direct Public URL
            const url1 = asset.publicUrl;
            await testImageUrl(
                document.getElementById('test1-img'),
                document.getElementById('test1-url'),
                document.getElementById('test1-status'),
                url1
            );
            
            // Test 2: API Content Endpoint
            const url2 = `/api/media/assets/${assetId}/content`;
            await testImageUrl(
                document.getElementById('test2-img'),
                document.getElementById('test2-url'),
                document.getElementById('test2-status'),
                url2
            );
            
            // Test 3: Uploads Directory
            const url3 = `/uploads/${asset.fileName}`;
            await testImageUrl(
                document.getElementById('test3-img'),
                document.getElementById('test3-url'),
                document.getElementById('test3-status'),
                url3
            );
            
            // Test 4: Static Directory
            const url4 = `/static/media/${asset.fileName}`;
            await testImageUrl(
                document.getElementById('test4-img'),
                document.getElementById('test4-url'),
                document.getElementById('test4-status'),
                url4
            );
        }
    </script>
</body>
</html>
