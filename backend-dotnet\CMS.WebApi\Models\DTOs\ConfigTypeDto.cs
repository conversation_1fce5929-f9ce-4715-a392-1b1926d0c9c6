namespace CMS.WebApi.Models.DTOs;

public class ConfigTypeDto
{
    public int Id { get; set; }
    public string ConfigTypeName { get; set; } = string.Empty;
    public string? ConfigTypeDesc { get; set; }
    public string? DisplayName { get; set; }
    public string? AdditionalInfo { get; set; }
    public string? DisclaimerText { get; set; }
    public string? PlaceholderText { get; set; }
    public bool IsActive { get; set; } = true;
}
