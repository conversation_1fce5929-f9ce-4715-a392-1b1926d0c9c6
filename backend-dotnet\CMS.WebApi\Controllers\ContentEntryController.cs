using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.Responses;
using CMS.WebApi.Services.Interfaces;
using CMS.WebApi.Security;

namespace CMS.WebApi.Controllers;

[ApiController]
[Route("api/content-entries")]
[Authorize(Policy = AuthorizationPolicies.JwtOrApiToken)]
[Tags("Content Entry Management")]
public class ContentEntryController : ControllerBase
{
    private readonly IContentEntryService _contentEntryService;
    private readonly ILogger<ContentEntryController> _logger;

    public ContentEntryController(IContentEntryService contentEntryService, ILogger<ContentEntryController> logger)
    {
        _contentEntryService = contentEntryService;
        _logger = logger;
    }

    /// <summary>
    /// Get all content entries
    /// </summary>
    /// <returns>List of content entries</returns>
    [HttpGet("getAll")]
    [ProducesResponseType(typeof(IEnumerable<ContentEntry>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status204NoContent)]
    public async Task<ActionResult<IEnumerable<ContentEntry>>> GetAllContentEntries()
    {
        var entries = await _contentEntryService.GetAllContentEntriesAsync();
        if (!entries.Any())
        {
            return NoContent();
        }
        return Ok(entries);
    }

    /// <summary>
    /// Get content entry by ID
    /// </summary>
    /// <param name="id">Content entry ID</param>
    /// <returns>Content entry details</returns>
    [HttpGet("getById/{id}")]
    [ProducesResponseType(typeof(ContentEntry), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status204NoContent)]
    public async Task<ActionResult<ContentEntry>> GetContentEntryById(int id)
    {
        var entry = await _contentEntryService.GetContentEntryByIdAsync(id);
        if (entry == null)
        {
            return NoContent();
        }
        return Ok(entry);
    }

    /// <summary>
    /// Get content entries by collection
    /// </summary>
    /// <param name="collectionId">Collection ID</param>
    /// <param name="page">Page number (0-based)</param>
    /// <param name="size">Page size</param>
    /// <param name="search">Search text</param>
    /// <returns>Paginated list of content entries</returns>
    [HttpGet("collection/{collectionId}")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    public async Task<ActionResult<object>> GetContentEntriesByCollection(
        int collectionId,
        [FromQuery] int page = 0,
        [FromQuery] int size = 20,
        [FromQuery] string? search = null)
    {
        try
        {
            var (items, totalCount) = await _contentEntryService.GetPagedContentEntriesAsync(
                collectionId, page + 1, size, search); // Convert to 1-based page

            var result = new
            {
                Content = items,
                TotalElements = totalCount,
                TotalPages = (int)Math.Ceiling((double)totalCount / size),
                Size = size,
                Number = page,
                First = page == 0,
                Last = page >= (int)Math.Ceiling((double)totalCount / size) - 1,
                NumberOfElements = items.Count()
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get content entries for collection: {CollectionId}", collectionId);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving content entries",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Create a new content entry
    /// </summary>
    /// <param name="contentEntry">Content entry details</param>
    /// <returns>Created content entry</returns>
    [HttpPost("create")]
    [ProducesResponseType(typeof(ContentEntry), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ContentEntry>> CreateContentEntry([FromBody] ContentEntry contentEntry)
    {
        try
        {
            var createdEntry = await _contentEntryService.CreateContentEntryAsync(contentEntry);
            _logger.LogInformation("Content entry created successfully: {EntryId}", createdEntry.Id);

            return CreatedAtAction(nameof(GetContentEntryById), new { id = createdEntry.Id }, createdEntry);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create content entry");
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while creating the content entry",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Update an existing content entry
    /// </summary>
    /// <param name="id">Content entry ID</param>
    /// <param name="contentEntry">Updated content entry details</param>
    /// <returns>Updated content entry</returns>
    [HttpPut("update/{id}")]
    [ProducesResponseType(typeof(ContentEntry), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ContentEntry>> UpdateContentEntry(int id, [FromBody] ContentEntry contentEntry)
    {
        try
        {
            var updatedEntry = await _contentEntryService.UpdateContentEntryAsync(id, contentEntry);
            _logger.LogInformation("Content entry updated successfully: {EntryId}", id);
            return Ok(updatedEntry);
        }
        catch (ArgumentException ex)
        {
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update content entry: {EntryId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while updating the content entry",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Delete a content entry
    /// </summary>
    /// <param name="id">Content entry ID</param>
    /// <returns>No content</returns>
    [HttpDelete("delete/{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult> DeleteContentEntry(int id)
    {
        try
        {
            var entry = await _contentEntryService.GetContentEntryByIdAsync(id);
            if (entry == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Content entry with ID {id} not found",
                    Path = Request.Path
                });
            }

            await _contentEntryService.DeleteContentEntryAsync(id);
            _logger.LogInformation("Content entry deleted successfully: {EntryId}", id);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete content entry: {EntryId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while deleting the content entry",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Search content entries
    /// </summary>
    /// <param name="collectionId">Collection ID</param>
    /// <param name="searchText">Search text</param>
    /// <returns>List of matching content entries</returns>
    [HttpGet("search")]
    [ProducesResponseType(typeof(IEnumerable<ContentEntry>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<ContentEntry>>> SearchContentEntries(
        [FromQuery] int collectionId,
        [FromQuery] string searchText)
    {
        try
        {
            if (string.IsNullOrEmpty(searchText))
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = "Search text is required",
                    Path = Request.Path
                });
            }

            var entries = await _contentEntryService.SearchContentEntriesAsync(collectionId, searchText);
            return Ok(entries);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to search content entries");
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while searching content entries",
                Path = Request.Path
            });
        }
    }
}
