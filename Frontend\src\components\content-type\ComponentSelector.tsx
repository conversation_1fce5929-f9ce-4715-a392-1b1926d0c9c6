import React, { useEffect, useState } from 'react';
import { componentsApi } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';
import { Component } from '@/lib/store';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Layers } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';

interface ComponentSelectorProps {
  open: boolean;
  onClose: () => void;
  onSelect: (component: Component) => void;
}

export default function ComponentSelector({
  open,
  onClose,
  onSelect,
}: ComponentSelectorProps) {
  const { toast } = useToast();
  const [components, setComponents] = useState<Component[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (open) {
      fetchComponents();
    }
  }, [open]);

  const fetchComponents = async () => {
    setLoading(true);
    try {
      const response = await componentsApi.getActive();
      console.log('Components data:', response.data);
      
      // Transform the data to match our Component interface
      const formattedComponents = response.data.map((component: any) => ({
        id: component.id.toString(),
        name: component.componentName || 'Unnamed Component',
        apiId: component.componentApiId || '',
        fields: [],
        isActive: component.isActive !== false, // Default to true if not specified
        createdAt: '',
        updatedAt: ''
      }));

      setComponents(formattedComponents);
    } catch (error) {
      console.error('Error fetching components:', error);
      toast({
        title: 'Error',
        description: 'Failed to load components',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Filter components based on search term
  const filteredComponents = components.filter(component => 
    component.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    component.apiId.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Select a Component</DialogTitle>
          <DialogDescription>
            Choose a component to use in your content type
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          <Input
            placeholder="Search components..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="mb-4"
          />
          
          {loading ? (
            <div className="grid grid-cols-1 gap-4">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="cursor-pointer">
                  <CardHeader className="pb-2">
                    <Skeleton className="h-5 w-1/2 mb-2" />
                    <Skeleton className="h-4 w-3/4" />
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="h-4 w-full mb-2" />
                    <Skeleton className="h-4 w-2/3" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredComponents.length === 0 ? (
            <div className="text-center py-8">
              <Layers className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No components found</h3>
              <p className="text-sm text-muted-foreground">
                {searchTerm ? 'Try a different search term' : 'Create components first'}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4 max-h-[400px] overflow-y-auto pr-2">
              {filteredComponents.map((component) => (
                <Card 
                  key={component.id} 
                  className="cursor-pointer hover:border-primary transition-colors"
                  onClick={() => onSelect(component)}
                >
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{component.name}</CardTitle>
                      <div className="p-1 rounded-full bg-primary/10">
                        <Layers className="h-4 w-4 text-primary" />
                      </div>
                    </div>
                    <CardDescription>
                      API ID: {component.apiId}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      {component.fields.length} fields
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
