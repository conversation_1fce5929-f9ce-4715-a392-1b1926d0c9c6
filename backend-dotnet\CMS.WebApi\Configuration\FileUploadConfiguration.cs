namespace CMS.WebApi.Configuration;

public class FileUploadConfiguration
{
    public const string SectionName = "FileUpload";

    public string UploadDirectory { get; set; } = "media-uploads";
    public string BaseUrl { get; set; } = "http://localhost:5000";
    public long MaxFileSize { get; set; } = 10 * 1024 * 1024; // 10MB
    public string[] AllowedExtensions { get; set; } = 
    {
        ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", // Images
        ".pdf", ".doc", ".docx", ".txt", ".rtf", // Documents
        ".mp4", ".avi", ".mov", ".wmv", ".flv", // Videos
        ".mp3", ".wav", ".flac", ".aac", // Audio
        ".zip", ".rar", ".7z", ".tar", ".gz" // Archives
    };
    public string[] AllowedMimeTypes { get; set; } = 
    {
        "image/jpeg", "image/png", "image/gif", "image/bmp", "image/webp",
        "application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "text/plain", "application/rtf",
        "video/mp4", "video/x-msvideo", "video/quicktime", "video/x-ms-wmv", "video/x-flv",
        "audio/mpeg", "audio/wav", "audio/flac", "audio/aac",
        "application/zip", "application/x-rar-compressed", "application/x-7z-compressed",
        "application/x-tar", "application/gzip"
    };
    public bool EnableVirusScan { get; set; } = false;
    public bool GenerateThumbnails { get; set; } = true;
    public int ThumbnailWidth { get; set; } = 200;
    public int ThumbnailHeight { get; set; } = 200;
    public bool PreserveOriginalFileName { get; set; } = false;
    public int CleanupOldFilesAfterDays { get; set; } = 365;
}
