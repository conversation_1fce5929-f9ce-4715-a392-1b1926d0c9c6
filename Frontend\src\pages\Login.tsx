
import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Link, useNavigate } from 'react-router-dom';
import { Database, Eye, EyeOff, Info } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { toast as sonnerToast } from 'sonner';
import { authApi, getBaseUrl } from '@/lib/api';
import { useAuthStore } from '@/lib/store';
import { getTenantFromToken, getUsernameFromToken } from '@/lib/jwt';
import { useTenant } from '@/components/tenant/TenantProvider';
import { ThemeToggle } from '@/components/theme/ThemeToggle';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Al<PERSON>, AlertDescription } from '@/components/ui/alert';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

// Validation schema
const loginSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
});

type LoginFormValues = z.infer<typeof loginSchema>;

export default function Login() {
  const { login } = useAuthStore();
  const { setCurrentTenant } = useTenant();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = React.useState(false);
  const [showPassword, setShowPassword] = React.useState(false);

  // Set up form with react-hook-form and zod validation
  const formMethods = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  });

  // Check if already authenticated on mount
  // React.useEffect(() => {
  //   const token = localStorage.getItem('cms_token');
  //   if (token) {
  //     console.log('Already have token, redirecting to dashboard');
  //     navigate('/dashboard');
  //   }
  // }, [navigate]);

  // Handle form submission
  const onSubmit = async (data: LoginFormValues) => {
    setIsLoading(true);
    console.log('Attempting login with:', data.username);

    try {
      console.log('Making login API call to backend...');
      console.log('API base URL:', getBaseUrl()); // Add this line to log the base URL
      console.log('Login endpoint:', '/auth/login');
      console.log('Login payload:', { username: data.username, password: data.password });
      const response = await authApi.login(data.username, data.password);
      console.log('Login API response:', response.data);

      // The backend returns { accessToken: "token_value" }
      if (!response.data || !response.data.accessToken) {
        throw new Error('Invalid response: Missing token');
      }

      const token = response.data.accessToken;

      // Extract username and tenant from token
      const username = getUsernameFromToken(token) || data.username;
      const tenant = getTenantFromToken(token);

      console.log('Extracted from token - Username:', username, 'Tenant:', tenant);

      // Create a user object with tenant information
      const user = {
        id: username,
        username: username,
        email: '', // We don't have this from the login response
        tenant: tenant
      };

      // Save token and user info
      console.log('Saving token to auth store and localStorage');
      login(token, user);

      // Set tenant in context
      if (tenant) {
        console.log('Setting tenant context to:', tenant);
        setCurrentTenant(tenant);
      }

      // Also save to localStorage directly for debugging
      localStorage.setItem('cms_token', token);

      // Verify token was stored correctly
      const storedToken = localStorage.getItem('cms_token');
      console.log('Login successful, token stored:', storedToken ? 'Yes' : 'No');
      console.log('Token format valid:', storedToken?.includes('.') && storedToken?.split('.').length === 3 ? 'Yes' : 'No');

      toast({
        title: 'Login successful',
        description: 'Welcome back to the CMS',
      });

      sonnerToast.success('Login successful', {
        description: 'Welcome back to the CMS'
      });

      // Small delay before navigation to ensure token is stored
      setTimeout(() => {
        navigate('/dashboard');
      }, 300);
    } catch (error: any) {
      console.error('Login error:', error);
      console.error('Error response:', error.response);

      // More detailed error logging
      if (error.response) {
        console.error('Status:', error.response.status);
        console.error('Headers:', error.response.headers);
        console.error('Data:', error.response.data);
      }

      // Try to extract a meaningful error message
      let errorMessage = 'Unable to connect to the server';

      if (error.response?.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        } else if (error.response.data.message) {
          errorMessage = error.response.data.message;
        } else if (error.response.data.error) {
          errorMessage = error.response.data.error;
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      console.log('Login failed:', errorMessage);

      toast({
        title: 'Login failed',
        description: errorMessage,
        variant: 'destructive',
      });

      sonnerToast.error('Login failed', {
        description: errorMessage
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 p-4 relative overflow-hidden">
      {/* Animated Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-pink-500/5 dark:from-blue-500/10 dark:via-purple-500/10 dark:to-pink-500/10"></div>

      {/* Floating Particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-20 animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 10}s`,
              animationDuration: `${10 + Math.random() * 20}s`
            }}
          />
        ))}
      </div>

      {/* Animated Grid Background */}
      <div className="absolute inset-0 opacity-10 dark:opacity-20">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-purple-500/10 to-transparent animate-pulse"></div>
        <div className="grid grid-cols-12 gap-4 h-full w-full">
          {[...Array(144)].map((_, i) => (
            <div
              key={i}
              className="border border-purple-300/20 dark:border-purple-500/30 rounded-sm hover:bg-purple-500/10 transition-all duration-500"
              style={{
                animationDelay: `${i * 0.05}s`
              }}
            />
          ))}
        </div>
      </div>

      {/* Theme Toggle */}
      <div className="absolute top-6 right-6 z-20 animate-in fade-in-0 slide-in-from-top-2 duration-500">
        <div className="relative">
          <ThemeToggle />
          {/* Enhanced glow effect */}
          <div className="absolute inset-0 rounded-md bg-gradient-to-r from-purple-500/30 to-pink-500/30 blur-xl -z-10 opacity-60 animate-pulse"></div>
          <div className="absolute inset-0 rounded-md bg-gradient-to-r from-blue-500/20 to-purple-500/20 blur-2xl -z-20 opacity-40 animate-ping"></div>
        </div>
      </div>

      <Card className="w-full max-w-md shadow-2xl dark:shadow-purple-500/30 bg-white/95 dark:bg-slate-800/95 backdrop-blur-lg border-2 border-purple-200/30 dark:border-purple-500/30 hover:border-purple-400/60 dark:hover:border-purple-400/60 transition-all duration-500 dark-glass dark-hover-lift relative overflow-hidden group animate-in fade-in-0 slide-in-from-bottom-4 duration-700">
        {/* Card glow effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

        {/* Animated border */}
        <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500 opacity-0 group-hover:opacity-20 blur-sm transition-all duration-500"></div>

        <CardHeader className="space-y-4 relative z-10">
          <div className="flex justify-center mb-8">
            <div className="relative group/logo">
              <div className="flex items-center rounded-xl bg-white/90 dark:bg-slate-700/90 p-4 shadow-xl dark:shadow-purple-500/30 transition-all duration-500 hover:scale-110 hover:rotate-1 dark-glass border border-purple-200/50 dark:border-purple-500/30">
                <img src="/images/cloud-logo.png" alt="CMS Logo" className="h-12 w-36 transition-all duration-300 group-hover/logo:brightness-110" />
              </div>
              {/* Logo glow */}
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-purple-500/30 to-pink-500/30 blur-lg opacity-0 group-hover/logo:opacity-60 transition-all duration-500 -z-10"></div>
            </div>
          </div>

          <div className="text-center space-y-2">
            <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 dark:from-purple-400 dark:via-pink-400 dark:to-blue-400 bg-clip-text text-transparent animate-in slide-in-from-top-2 duration-500 delay-200">
              Welcome Back
            </h1>
            <CardDescription className="text-center dark-text-glow animate-in slide-in-from-top-2 duration-500 delay-300">
              Enter your credentials to access your account
            </CardDescription>
          </div>
        </CardHeader>
        <CardContent>

          <Form {...formMethods}>
            <form onSubmit={formMethods.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={formMethods.control}
                name="username"
                render={({ field }) => (
                  <FormItem className="animate-in slide-in-from-left-2 duration-500 delay-400">
                    <FormLabel className="dark-text-glow text-sm font-semibold bg-gradient-to-r from-purple-600 to-pink-600 dark:from-purple-400 dark:to-pink-400 bg-clip-text text-transparent">
                      Username
                    </FormLabel>
                    <FormControl>
                      <div className="relative group">
                        <Input
                          placeholder="Enter your username"
                          {...field}
                          className="dark-input-glow transition-all duration-300 hover:shadow-lg focus:shadow-xl focus:shadow-purple-500/30 dark:focus:shadow-purple-500/50 border-2 border-purple-200/50 dark:border-purple-500/30 focus:border-purple-400 dark:focus:border-purple-400 bg-white/80 dark:bg-slate-800/90 backdrop-blur-sm pl-4 pr-10 py-3 rounded-lg group-hover:scale-[1.02] focus:scale-[1.02] text-slate-900 dark:text-slate-100 placeholder:text-slate-500 dark:placeholder:text-slate-400"
                        />
                        {/* Input glow effect */}
                        <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-purple-500/20 to-pink-500/20 opacity-0 group-hover:opacity-50 group-focus-within:opacity-70 transition-all duration-300 -z-10 blur-sm"></div>
                        {/* Icon */}
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-purple-400 opacity-50 group-focus-within:opacity-100 transition-all duration-300">
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                        </div>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={formMethods.control}
                name="password"
                render={({ field }) => (
                  <FormItem className="animate-in slide-in-from-left-2 duration-500 delay-500">
                    <FormLabel className="dark-text-glow text-sm font-semibold bg-gradient-to-r from-purple-600 to-pink-600 dark:from-purple-400 dark:to-pink-400 bg-clip-text text-transparent">
                      Password
                    </FormLabel>
                    <FormControl>
                      <div className="relative group">
                        <Input
                          type={showPassword ? "text" : "password"}
                          placeholder="••••••••"
                          {...field}
                          className="dark-input-glow transition-all duration-300 hover:shadow-lg focus:shadow-xl focus:shadow-purple-500/30 dark:focus:shadow-purple-500/50 border-2 border-purple-200/50 dark:border-purple-500/30 focus:border-purple-400 dark:focus:border-purple-400 bg-white/80 dark:bg-slate-800/90 backdrop-blur-sm pl-4 pr-12 py-3 rounded-lg group-hover:scale-[1.02] focus:scale-[1.02] text-slate-900 dark:text-slate-100 placeholder:text-slate-500 dark:placeholder:text-slate-400"
                        />
                        {/* Input glow effect */}
                        <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-purple-500/20 to-pink-500/20 opacity-0 group-hover:opacity-50 group-focus-within:opacity-70 transition-all duration-300 -z-10 blur-sm"></div>

                        <button
                          type="button"
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-purple-400 hover:text-purple-600 dark:hover:text-purple-300 transition-all duration-300 hover:scale-110 p-1 rounded-md hover:bg-purple-100 dark:hover:bg-purple-900/30"
                          onClick={() => setShowPassword(!showPassword)}
                          tabIndex={-1}
                        >
                          {showPassword ? (
                            <EyeOff className="h-4 w-4 transition-all duration-300" />
                          ) : (
                            <Eye className="h-4 w-4 transition-all duration-300" />
                          )}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="relative group animate-in slide-in-from-bottom-2 duration-500 delay-600">
                <Button
                  type="submit"
                  className="w-full button-ripple dark-hover-lift dark-border-glow transition-all duration-500 hover:shadow-2xl hover:shadow-purple-500/40 dark:hover:shadow-purple-500/60 bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 hover:from-purple-700 hover:via-pink-700 hover:to-blue-700 text-white border-0 py-3 text-lg font-semibold rounded-lg hover:scale-105 active:scale-95 relative overflow-hidden group"
                  disabled={isLoading}
                >
                  {/* Button shimmer effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                  {/* Loading spinner */}
                  {isLoading && (
                    <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                      <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    </div>
                  )}

                  <span className={`transition-all duration-300 ${isLoading ? 'ml-8' : ''}`}>
                    {isLoading ? 'Logging in...' : 'Login'}
                  </span>


                </Button>

                {/* Button glow effect */}
                <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500 opacity-0 group-hover:opacity-30 blur-lg transition-all duration-500 -z-10"></div>
              </div>
            </form>
          </Form>
        </CardContent>
        <CardFooter className="flex flex-col space-y-4 dark-glass relative z-10">


          <div className="text-center text-sm animate-in slide-in-from-bottom-2 duration-500 delay-800">
            <span className="text-muted-foreground">Don't have an account?</span>{' '}
            <Link
              to="/register"
              className="text-transparent bg-gradient-to-r from-purple-600 to-pink-600 dark:from-purple-400 dark:to-pink-400 bg-clip-text font-semibold hover:from-purple-700 hover:to-pink-700 dark:hover:from-purple-300 dark:hover:to-pink-300 transition-all duration-300 hover:scale-105 inline-block relative group"
            >
              Register
              <span className="absolute inset-x-0 bottom-0 h-0.5 bg-gradient-to-r from-purple-600 to-pink-600 scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></span>
            </Link>
          </div>

          <div className="text-center text-sm animate-in slide-in-from-bottom-2 duration-500 delay-900">
            <Link
              to="/forgot-password"
              className="text-transparent bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400 bg-clip-text font-medium hover:from-blue-700 hover:to-purple-700 dark:hover:from-blue-300 dark:hover:to-purple-300 transition-all duration-300 hover:scale-105 inline-block relative group"
            >
              Forgot your password?
              <span className="absolute inset-x-0 bottom-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></span>
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
