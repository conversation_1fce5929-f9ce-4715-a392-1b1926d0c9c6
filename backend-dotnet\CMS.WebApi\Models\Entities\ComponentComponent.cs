using System.ComponentModel.DataAnnotations;

namespace CMS.WebApi.Models.Entities;

public class ComponentComponent : BaseEntity
{
    public int Id { get; set; }

    [Required]
    public int ParentComponentId { get; set; }
    public ComponentListing ParentComponent { get; set; } = null!;

    [Required]
    public int ChildComponentId { get; set; }
    public ComponentListing ChildComponent { get; set; } = null!;

    public int? DisplayPreference { get; set; }

    public bool IsRepeatable { get; set; } = false;

    public bool IsActive { get; set; } = true;

    public string? AdditionalInformation { get; set; }
}
