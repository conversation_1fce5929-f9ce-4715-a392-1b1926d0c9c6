
import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Link, useNavigate } from 'react-router-dom';
import { Database, Eye, EyeOff, User, Mail, Lock } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { toast as sonnerToast } from 'sonner';
import axios from 'axios';
import { authApi, getBaseUrl } from '@/lib/api';
import { ThemeToggle } from '@/components/theme/ThemeToggle';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

// Validation schema
const registerSchema = z.object({
  username: z.string().min(3, 'Username must be at least 3 characters'),
  email: z.string().email('Please enter a valid email'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  confirmPassword: z.string().min(6, 'Password must be at least 6 characters'),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type RegisterFormValues = z.infer<typeof registerSchema>;

export default function Register() {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = React.useState(false);
  const [showPassword, setShowPassword] = React.useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = React.useState(false);

  // React Hook Form
  const form = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
  });

  // Handle form submission
  const onSubmit = async (data: RegisterFormValues) => {
    setIsLoading(true);
    console.log('Attempting registration with:', data.username, data.email);

    try {
      // Log the API base URL for debugging
      console.log('API Base URL:', getBaseUrl());
      console.log('Making registration API call to backend...');

      // Create a direct axios instance for debugging
      const directResponse = await axios({
        method: 'post',
        url: `${getBaseUrl()}/auth/register`,
        data: {
          username: data.username,
          email: data.email,
          password: data.password
        },
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      console.log('Registration API direct response:', directResponse.data);

      // The backend returns a success message on successful registration
      toast({
        title: 'Registration successful',
        description: 'You can now login with your credentials',
      });

      sonnerToast.success('Registration successful', {
        description: 'You can now login with your credentials'
      });

      navigate('/login');
    } catch (error: any) {
      console.error('Registration error:', error);

      // Log detailed error information
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);
        console.error('Error response headers:', error.response.headers);
      } else if (error.request) {
        // The request was made but no response was received
        console.error('Error request:', error.request);
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error('Error message:', error.message);
      }

      const errorMessage = error.response?.data ||
                         error.response?.data?.message ||
                         error.message ||
                         'An error occurred during registration';

      console.log('Registration failed:', errorMessage);

      toast({
        title: 'Registration failed',
        description: typeof errorMessage === 'string' ? errorMessage : JSON.stringify(errorMessage),
        variant: 'destructive',
      });

      sonnerToast.error('Registration failed', {
        description: typeof errorMessage === 'string' ? errorMessage : JSON.stringify(errorMessage)
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 p-4 relative overflow-hidden">
      {/* Animated Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-pink-500/5 dark:from-blue-500/10 dark:via-purple-500/10 dark:to-pink-500/10"></div>

      {/* Floating shapes */}
      <div className="absolute top-20 left-20 w-32 h-32 bg-purple-500/10 rounded-full blur-xl animate-pulse"></div>
      <div className="absolute bottom-20 right-20 w-40 h-40 bg-pink-500/10 rounded-full blur-xl animate-pulse delay-1000"></div>
      <div className="absolute top-1/2 left-10 w-24 h-24 bg-blue-500/10 rounded-full blur-xl animate-pulse delay-500"></div>
      <div className="absolute bottom-1/3 left-1/3 w-28 h-28 bg-purple-500/10 rounded-full blur-xl animate-pulse delay-700"></div>

      {/* Theme Toggle */}
      <div className="absolute top-4 right-4 z-50">
        <ThemeToggle />
      </div>

      <Card className="w-full max-w-md shadow-2xl dark:shadow-purple-500/30 bg-white/95 dark:bg-slate-800/95 backdrop-blur-lg border-2 border-purple-200/30 dark:border-purple-500/30 hover:border-purple-400/60 dark:hover:border-purple-400/60 transition-all duration-500 dark-glass dark-hover-lift relative overflow-hidden group animate-in fade-in-0 slide-in-from-bottom-4 duration-700">
        {/* Card glow effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

        {/* Card content with relative z-index */}
        <div className="relative z-10">

        <CardHeader className="space-y-4 relative z-10">
          <div className="flex justify-center mb-8">
            <div className="relative group/logo">
              <div className="flex items-center rounded-xl bg-white/90 dark:bg-slate-700/90 p-4 shadow-xl dark:shadow-purple-500/30 transition-all duration-500 hover:scale-110 hover:rotate-1 dark-glass border border-purple-200/50 dark:border-purple-500/30">
                <img src="/images/cloud-logo.png" alt="CMS Logo" className="h-12 w-36 transition-all duration-300 group-hover/logo:brightness-110" />
              </div>
              {/* Logo glow */}
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-purple-500/30 to-pink-500/30 blur-lg opacity-0 group-hover/logo:opacity-60 transition-all duration-500 -z-10"></div>
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-center dark-text-glow bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 dark:from-purple-400 dark:via-pink-400 dark:to-blue-400 bg-clip-text text-transparent animate-in slide-in-from-top-2 duration-500 delay-200">
            Create an Account
          </CardTitle>
          <CardDescription className="text-center text-slate-600 dark:text-slate-300 animate-in slide-in-from-top-2 duration-500 delay-300">
            Register to manage your content
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem className="animate-in slide-in-from-left-2 duration-500 delay-400">
                    <FormLabel className="dark-text-glow text-sm font-semibold bg-gradient-to-r from-purple-600 to-pink-600 dark:from-purple-400 dark:to-pink-400 bg-clip-text text-transparent">
                      Username
                    </FormLabel>
                    <FormControl>
                      <div className="relative group">
                        <Input
                          placeholder="Enter your username"
                          {...field}
                          className="dark-input-glow transition-all duration-300 hover:shadow-lg focus:shadow-xl focus:shadow-purple-500/30 dark:focus:shadow-purple-500/50 border-2 border-purple-200/50 dark:border-purple-500/30 focus:border-purple-400 dark:focus:border-purple-400 bg-white/80 dark:bg-slate-800/90 backdrop-blur-sm pl-4 pr-10 py-3 rounded-lg group-hover:scale-[1.02] focus:scale-[1.02] text-slate-900 dark:text-slate-100 placeholder:text-slate-500 dark:placeholder:text-slate-400"
                        />
                        {/* Input glow effect */}
                        <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-purple-500/20 to-pink-500/20 opacity-0 group-hover:opacity-50 group-focus-within:opacity-70 transition-all duration-300 -z-10 blur-sm"></div>
                        {/* Icon */}
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-purple-400 opacity-50 group-focus-within:opacity-100 transition-all duration-300">
                          <User className="h-4 w-4" />
                        </div>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem className="animate-in slide-in-from-left-2 duration-500 delay-500">
                    <FormLabel className="dark-text-glow text-sm font-semibold bg-gradient-to-r from-purple-600 to-pink-600 dark:from-purple-400 dark:to-pink-400 bg-clip-text text-transparent">
                      Email
                    </FormLabel>
                    <FormControl>
                      <div className="relative group">
                        <Input
                          type="email"
                          placeholder="Enter your email"
                          {...field}
                          className="dark-input-glow transition-all duration-300 hover:shadow-lg focus:shadow-xl focus:shadow-purple-500/30 dark:focus:shadow-purple-500/50 border-2 border-purple-200/50 dark:border-purple-500/30 focus:border-purple-400 dark:focus:border-purple-400 bg-white/80 dark:bg-slate-800/90 backdrop-blur-sm pl-4 pr-10 py-3 rounded-lg group-hover:scale-[1.02] focus:scale-[1.02] text-slate-900 dark:text-slate-100 placeholder:text-slate-500 dark:placeholder:text-slate-400"
                        />
                        {/* Input glow effect */}
                        <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-purple-500/20 to-pink-500/20 opacity-0 group-hover:opacity-50 group-focus-within:opacity-70 transition-all duration-300 -z-10 blur-sm"></div>
                        {/* Icon */}
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-purple-400 opacity-50 group-focus-within:opacity-100 transition-all duration-300">
                          <Mail className="h-4 w-4" />
                        </div>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem className="animate-in slide-in-from-left-2 duration-500 delay-600">
                    <FormLabel className="dark-text-glow text-sm font-semibold bg-gradient-to-r from-purple-600 to-pink-600 dark:from-purple-400 dark:to-pink-400 bg-clip-text text-transparent">
                      Password
                    </FormLabel>
                    <FormControl>
                      <div className="relative group">
                        <Input
                          type={showPassword ? "text" : "password"}
                          placeholder="••••••••"
                          {...field}
                          className="dark-input-glow transition-all duration-300 hover:shadow-lg focus:shadow-xl focus:shadow-purple-500/30 dark:focus:shadow-purple-500/50 border-2 border-purple-200/50 dark:border-purple-500/30 focus:border-purple-400 dark:focus:border-purple-400 bg-white/80 dark:bg-slate-800/90 backdrop-blur-sm pl-4 pr-12 py-3 rounded-lg group-hover:scale-[1.02] focus:scale-[1.02] text-slate-900 dark:text-slate-100 placeholder:text-slate-500 dark:placeholder:text-slate-400"
                        />
                        {/* Input glow effect */}
                        <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-purple-500/20 to-pink-500/20 opacity-0 group-hover:opacity-50 group-focus-within:opacity-70 transition-all duration-300 -z-10 blur-sm"></div>

                        <button
                          type="button"
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-purple-400 hover:text-purple-300 transition-all duration-300 hover:scale-110 focus:outline-none"
                          onClick={() => setShowPassword(!showPassword)}
                          tabIndex={-1}
                        >
                          {showPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem className="animate-in slide-in-from-left-2 duration-500 delay-700">
                    <FormLabel className="dark-text-glow text-sm font-semibold bg-gradient-to-r from-purple-600 to-pink-600 dark:from-purple-400 dark:to-pink-400 bg-clip-text text-transparent">
                      Confirm Password
                    </FormLabel>
                    <FormControl>
                      <div className="relative group">
                        <Input
                          type={showConfirmPassword ? "text" : "password"}
                          placeholder="••••••••"
                          {...field}
                          className="dark-input-glow transition-all duration-300 hover:shadow-lg focus:shadow-xl focus:shadow-purple-500/30 dark:focus:shadow-purple-500/50 border-2 border-purple-200/50 dark:border-purple-500/30 focus:border-purple-400 dark:focus:border-purple-400 bg-white/80 dark:bg-slate-800/90 backdrop-blur-sm pl-4 pr-12 py-3 rounded-lg group-hover:scale-[1.02] focus:scale-[1.02] text-slate-900 dark:text-slate-100 placeholder:text-slate-500 dark:placeholder:text-slate-400"
                        />
                        {/* Input glow effect */}
                        <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-purple-500/20 to-pink-500/20 opacity-0 group-hover:opacity-50 group-focus-within:opacity-70 transition-all duration-300 -z-10 blur-sm"></div>

                        <button
                          type="button"
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-purple-400 hover:text-purple-300 transition-all duration-300 hover:scale-110 focus:outline-none"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          tabIndex={-1}
                        >
                          {showConfirmPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button
                type="submit"
                className="w-full mt-6 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 dark:from-purple-500 dark:to-pink-500 dark:hover:from-purple-600 dark:hover:to-pink-600 text-white font-semibold py-3 px-6 rounded-lg shadow-lg hover:shadow-xl dark:shadow-purple-500/30 dark:hover:shadow-purple-500/50 transition-all duration-300 hover:scale-[1.02] active:scale-[0.98] dark-button-glow animate-in slide-in-from-bottom-2 duration-500 delay-800 group"
                disabled={isLoading}
              >
                <span className="relative z-10 flex items-center justify-center gap-2">
                  {isLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                      Creating account...
                    </>
                  ) : (
                    <>
                      <Database className="h-4 w-4 group-hover:rotate-12 transition-transform duration-300" />
                      Register
                    </>
                  )}
                </span>
                {/* Button shimmer effect */}
                <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-white/0 via-white/20 to-white/0 opacity-0 group-hover:opacity-100 group-hover:animate-shimmer transition-opacity duration-300"></div>
              </Button>
            </form>
          </Form>
        </CardContent>
        <CardFooter className="flex justify-center relative z-10">
          <div className="text-center text-sm text-slate-600 dark:text-slate-300 animate-in slide-in-from-bottom-2 duration-500 delay-900">
            Already have an account?{' '}
            <Link
              to="/login"
              className="text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 font-semibold transition-all duration-300 hover:underline decoration-2 underline-offset-2"
            >
              Login
            </Link>
          </div>
        </CardFooter>

        </div>
      </Card>
    </div>
  );
}
