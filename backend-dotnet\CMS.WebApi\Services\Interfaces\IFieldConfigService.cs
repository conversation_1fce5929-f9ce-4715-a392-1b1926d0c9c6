using CMS.WebApi.Models.Entities;

namespace CMS.WebApi.Services.Interfaces;

public interface IFieldConfigService
{
    Task<IEnumerable<FieldConfig>> GetAllFieldConfigsAsync();
    Task<IEnumerable<FieldConfig>> GetActiveFieldConfigsAsync();
    Task<FieldConfig?> GetFieldConfigByIdAsync(int id);
    Task<IEnumerable<FieldConfig>> GetFieldConfigsByFieldTypeAsync(int fieldTypeId);
    Task<IEnumerable<FieldConfig>> GetFieldConfigsByConfigTypeAsync(int configTypeId);
    Task<FieldConfig> CreateFieldConfigAsync(FieldConfig fieldConfig);
    Task<FieldConfig> UpdateFieldConfigAsync(int id, FieldConfig fieldConfig);
    Task DeleteFieldConfigAsync(int id);
    Task<bool> FieldConfigExistsByConfigNameAsync(string configName);
}
