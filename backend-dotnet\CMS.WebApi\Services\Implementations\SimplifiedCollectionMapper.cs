using CMS.WebApi.Models.DTOs;
using CMS.WebApi.Models.Entities;
using Microsoft.Extensions.Logging;

namespace CMS.WebApi.Services.Implementations;

public class SimplifiedCollectionMapper
{
    private readonly ILogger<SimplifiedCollectionMapper> _logger;

    public SimplifiedCollectionMapper(ILogger<SimplifiedCollectionMapper> logger)
    {
        _logger = logger;
    }
    public SimplifiedCollectionDto ToDto(CollectionListing entity)
    {
        if (entity == null) return null!;

        var dto = new SimplifiedCollectionDto
        {
            Id = entity.Id,
            CollectionName = entity.CollectionName ?? string.Empty,
            CollectionDesc = entity.CollectionDesc,
            CollectionApiId = entity.CollectionApiId ?? string.Empty
        };

        // Map components
        if (entity.Components != null)
        {
            dto.Components = entity.Components.Select(ToComponentDto).ToList();
        }

        // Map fields
        if (entity.Fields != null)
        {
            dto.Fields = entity.Fields.Select(ToCollectionFieldDto).ToList();
        }

        return dto;
    }

    public List<SimplifiedCollectionDto> ToDtoList(List<CollectionListing> entities)
    {
        if (entities == null) return new List<SimplifiedCollectionDto>();
        return entities.Select(ToDto).ToList();
    }

    private SimplifiedComponentDto ToComponentDto(CollectionComponent entity)
    {
        if (entity == null) return null!;

        var dto = new SimplifiedComponentDto
        {
            Id = entity.Id,
            DisplayPreference = entity.DisplayPreference,
            IsRepeatable = entity.IsRepeatable,
            MinRepeatOccurrences = entity.MinRepeatOccurrences,
            MaxRepeatOccurrences = entity.MaxRepeatOccurrences,
            IsActive = entity.IsActive,
            Name = entity.Name,
            DisplayName = entity.DisplayName,
            AdditionalInfo = entity.AdditionalInfo,
            AdditionalInfoImage = entity.AdditionalInfoImage
        };

        if (entity.Component != null)
        {
            dto.Component = ToComponentDetailsDto(entity.Component);
        }

        return dto;
    }

    private SimplifiedComponentDetailsDto ToComponentDetailsDto(ComponentListing entity)
    {
        if (entity == null) return null!;

        var dto = new SimplifiedComponentDetailsDto
        {
            Id = entity.Id,
            ComponentName = entity.ComponentName,
            ComponentDisplayName = entity.ComponentDisplayName,
            ComponentApiId = entity.ComponentApiId,
            IsActive = entity.IsActive,
            GetUrl = entity.GetUrl,
            PostUrl = entity.PostUrl,
            UpdateUrl = entity.UpdateUrl,
            AdditionalInformation = entity.AdditionalInformation,
            AdditionalInfoImage = entity.AdditionalInfoImage
        };

        // Map component fields
        if (entity.Fields != null)
        {
            dto.Fields = entity.Fields.Select(ToFieldDto).ToList();
        }

        // Map child components
        if (entity.ChildComponents != null)
        {
            dto.ChildComponents = entity.ChildComponents.Select(ToChildComponentDto).ToList();
        }

        return dto;
    }

    private SimplifiedFieldDto ToFieldDto(ComponentField entity)
    {
        if (entity == null) return null!;

        _logger.LogDebug("Mapping ComponentField ID: {FieldId}, ComponentId: {ComponentId}",
            entity.Id, entity.ComponentId);

        var dto = new SimplifiedFieldDto
        {
            Id = entity.Id,
            DisplayPreference = entity.DisplayPreference
        };

        if (entity.FieldType != null)
        {
            dto.FieldType = new SimplifiedFieldTypeDto
            {
                Id = entity.FieldType.Id,
                FieldTypeName = entity.FieldType.FieldTypeName ?? string.Empty,
                FieldTypeDesc = entity.FieldType.FieldTypeDesc,
                DisplayName = entity.FieldType.DisplayName,
                HelpText = entity.FieldType.HelpText,
                IsActive = entity.FieldType.IsActive
            };
        }

        // Map configs - convert to nested structure like Java
        if (entity.Configs != null && entity.Configs.Any())
        {
            _logger.LogDebug("ComponentField ID {FieldId} has {ConfigCount} configurations",
                entity.Id, entity.Configs.Count);

            foreach (var config in entity.Configs)
            {
                _logger.LogDebug("Processing config: ID={ConfigId}, FieldConfigId={FieldConfigId}, Value={Value}",
                    config.Id, config.FieldConfigId, config.ConfigValue);

                if (config.FieldConfig?.ConfigType != null)
                {
                    var configTypeName = config.FieldConfig.ConfigType.ConfigTypeName;
                    var configName = config.FieldConfig.ConfigName;
                    var configValue = config.ConfigValue;

                    _logger.LogDebug("Config details: Type={TypeName}, Name={ConfigName}, Value={Value}",
                        configTypeName, configName, configValue);

                    if (!string.IsNullOrEmpty(configTypeName) && !string.IsNullOrEmpty(configName))
                    {
                        // Convert to lowercase to match Java backend format
                        var normalizedTypeName = configTypeName.ToLowerInvariant();

                        if (!dto.Configs.ContainsKey(normalizedTypeName))
                        {
                            dto.Configs[normalizedTypeName] = new Dictionary<string, object>();
                        }

                        var configTypeDict = (Dictionary<string, object>)dto.Configs[normalizedTypeName];
                        var typedValue = ConvertToType(configValue, config.FieldConfig.ValueType);
                        configTypeDict[configName] = typedValue;

                        _logger.LogDebug("Added config: {TypeName}.{ConfigName} = {TypedValue}",
                            normalizedTypeName, configName, typedValue);
                    }
                    else
                    {
                        _logger.LogWarning("Skipping config due to missing type or name: Type={TypeName}, Name={ConfigName}",
                            configTypeName, configName);
                    }
                }
                else
                {
                    _logger.LogWarning("Config ID {ConfigId} has null FieldConfig or ConfigType", config.Id);
                }
            }

            _logger.LogDebug("ComponentField ID {FieldId} final config structure: {ConfigKeys}",
                entity.Id, string.Join(", ", dto.Configs.Keys));
        }
        else
        {
            _logger.LogDebug("ComponentField ID {FieldId} has no configurations", entity.Id);
        }

        return dto;
    }

    private SimplifiedFieldDto ToCollectionFieldDto(CollectionField entity)
    {
        if (entity == null) return null!;

        _logger.LogDebug("Mapping CollectionField ID: {FieldId}, CollectionId: {CollectionId}",
            entity.Id, entity.CollectionId);

        var dto = new SimplifiedFieldDto
        {
            Id = entity.Id,
            DisplayPreference = entity.DisplayPreference
        };

        if (entity.FieldType != null)
        {
            dto.FieldType = new SimplifiedFieldTypeDto
            {
                Id = entity.FieldType.Id,
                FieldTypeName = entity.FieldType.FieldTypeName ?? string.Empty,
                FieldTypeDesc = entity.FieldType.FieldTypeDesc,
                DisplayName = entity.FieldType.DisplayName,
                HelpText = entity.FieldType.HelpText,
                IsActive = entity.FieldType.IsActive
            };
        }

        // Map configs - convert to nested structure like Java
        if (entity.Configs != null && entity.Configs.Any())
        {
            _logger.LogDebug("CollectionField ID {FieldId} has {ConfigCount} configurations",
                entity.Id, entity.Configs.Count);

            foreach (var config in entity.Configs)
            {
                _logger.LogDebug("Processing collection field config: ID={ConfigId}, FieldConfigId={FieldConfigId}, Value={Value}",
                    config.Id, config.FieldConfigId, config.ConfigValue);

                if (config.FieldConfig?.ConfigType != null)
                {
                    var configTypeName = config.FieldConfig.ConfigType.ConfigTypeName;
                    var configName = config.FieldConfig.ConfigName;
                    var configValue = config.ConfigValue;

                    _logger.LogDebug("Collection field config details: Type={TypeName}, Name={ConfigName}, Value={Value}",
                        configTypeName, configName, configValue);

                    if (!string.IsNullOrEmpty(configTypeName) && !string.IsNullOrEmpty(configName))
                    {
                        // Convert to lowercase to match Java backend format
                        var normalizedTypeName = configTypeName.ToLowerInvariant();

                        if (!dto.Configs.ContainsKey(normalizedTypeName))
                        {
                            dto.Configs[normalizedTypeName] = new Dictionary<string, object>();
                        }

                        var configTypeDict = (Dictionary<string, object>)dto.Configs[normalizedTypeName];
                        var typedValue = ConvertToType(configValue, config.FieldConfig.ValueType);
                        configTypeDict[configName] = typedValue;

                        _logger.LogDebug("Added collection field config: {TypeName}.{ConfigName} = {TypedValue}",
                            normalizedTypeName, configName, typedValue);
                    }
                    else
                    {
                        _logger.LogWarning("Skipping collection field config due to missing type or name: Type={TypeName}, Name={ConfigName}",
                            configTypeName, configName);
                    }
                }
                else
                {
                    _logger.LogWarning("Collection field config ID {ConfigId} has null FieldConfig or ConfigType", config.Id);
                }
            }

            _logger.LogDebug("CollectionField ID {FieldId} final config structure: {ConfigKeys}",
                entity.Id, string.Join(", ", dto.Configs.Keys));
        }
        else
        {
            _logger.LogDebug("CollectionField ID {FieldId} has no configurations", entity.Id);
        }

        return dto;
    }

    private SimplifiedChildComponentDto ToChildComponentDto(ComponentComponent entity)
    {
        if (entity == null) return null!;

        var dto = new SimplifiedChildComponentDto
        {
            Id = entity.Id,
            DisplayPreference = entity.DisplayPreference,
            IsRepeatable = entity.IsRepeatable,
            IsActive = entity.IsActive,
            AdditionalInformation = entity.AdditionalInformation
        };

        if (entity.ChildComponent != null)
        {
            dto.ChildComponent = ToComponentDetailsDto(entity.ChildComponent);
        }

        return dto;
    }

    private object ConvertToType(string? value, string? valueType)
    {
        if (string.IsNullOrEmpty(value)) return value ?? string.Empty;

        return valueType?.ToLower() switch
        {
            "boolean" => bool.TryParse(value, out var boolResult) ? boolResult : value,
            "number" => int.TryParse(value, out var intResult) ? intResult : 
                       double.TryParse(value, out var doubleResult) ? doubleResult : value,
            _ => value
        };
    }
}
