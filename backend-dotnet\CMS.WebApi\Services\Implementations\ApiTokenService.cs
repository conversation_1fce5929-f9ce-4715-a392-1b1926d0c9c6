using System.Security.Cryptography;
using System.Text;
using Microsoft.EntityFrameworkCore;
using CMS.WebApi.Data;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Services.Interfaces;

namespace CMS.WebApi.Services.Implementations;

public class ApiTokenService : IApiTokenService
{
    private readonly CmsDbContext _context;
    private readonly ILogger<ApiTokenService> _logger;

    public ApiTokenService(CmsDbContext context, ILogger<ApiTokenService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<IEnumerable<ApiToken>> GetUserApiTokensAsync(long userId)
    {
        return await _context.ApiTokens
            .Where(t => t.UserId == userId)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<ApiToken?> GetApiTokenByIdAsync(long id, long userId)
    {
        return await _context.ApiTokens
            .Include(t => t.User)
            .FirstOrDefaultAsync(t => t.Id == id && t.UserId == userId);
    }

    public async Task<ApiToken?> GetApiTokenByValueAsync(string tokenValue)
    {
        return await _context.ApiTokens
            .Include(t => t.User)
            .FirstOrDefaultAsync(t => t.TokenValue == tokenValue && t.IsActive && t.ExpiresAt > DateTime.UtcNow);
    }

    public async Task<ApiToken> CreateApiTokenAsync(ApiToken token)
    {
        // Generate token value if not provided
        if (string.IsNullOrEmpty(token.TokenValue))
        {
            token.TokenValue = await GenerateTokenValueAsync();
        }

        token.CreatedAt = DateTime.UtcNow;
        _context.ApiTokens.Add(token);
        await _context.SaveChangesAsync();
        
        _logger.LogInformation("API token created: {TokenName} for user {UserId}", token.Name, token.UserId);
        return token;
    }

    public async Task<ApiToken> UpdateApiTokenAsync(long id, long userId, string name, string? description, bool isActive)
    {
        var existingToken = await _context.ApiTokens.FirstOrDefaultAsync(t => t.Id == id && t.UserId == userId);
        if (existingToken == null)
            throw new ArgumentException($"API token with ID {id} not found for user {userId}");

        existingToken.Name = name;
        existingToken.Description = description;
        existingToken.IsActive = isActive;
        existingToken.ModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return existingToken;
    }

    public async Task DeleteApiTokenAsync(long id, long userId)
    {
        var token = await _context.ApiTokens.FirstOrDefaultAsync(t => t.Id == id && t.UserId == userId);
        if (token != null)
        {
            _context.ApiTokens.Remove(token);
            await _context.SaveChangesAsync();
            _logger.LogInformation("API token deleted: {TokenId}", id);
        }
    }

    public async Task RevokeApiTokenAsync(long id, long userId)
    {
        var token = await _context.ApiTokens.FirstOrDefaultAsync(t => t.Id == id && t.UserId == userId);
        if (token == null)
            throw new ArgumentException($"API token with ID {id} not found for user {userId}");

        token.IsActive = false;
        token.ModifiedAt = DateTime.UtcNow;
        await _context.SaveChangesAsync();
    }

    public async Task UpdateLastUsedAsync(long tokenId)
    {
        var token = await _context.ApiTokens.FindAsync(tokenId);
        if (token != null)
        {
            token.LastUsedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
        }
    }

    public async Task<bool> TokenExistsForUserAsync(string name, long userId)
    {
        return await _context.ApiTokens.AnyAsync(t => t.Name == name && t.UserId == userId);
    }

    public async Task<bool> IsTokenValidAsync(string tokenValue)
    {
        var token = await GetApiTokenByValueAsync(tokenValue);
        return token != null;
    }

    public async Task CleanupExpiredTokensAsync()
    {
        var expiredTokens = await _context.ApiTokens
            .Where(t => t.ExpiresAt <= DateTime.UtcNow)
            .ToListAsync();

        if (expiredTokens.Any())
        {
            _context.ApiTokens.RemoveRange(expiredTokens);
            await _context.SaveChangesAsync();
            _logger.LogInformation("Cleaned up {Count} expired API tokens", expiredTokens.Count);
        }
    }

    private async Task<bool> TokenExistsAsync(string tokenValue)
    {
        return await _context.ApiTokens.AnyAsync(t => t.TokenValue == tokenValue);
    }

    public async Task<string> GenerateTokenValueAsync()
    {
        string tokenValue;
        do
        {
            tokenValue = GenerateSecureToken();
        } while (await TokenExistsAsync(tokenValue));

        return tokenValue;
    }

    private static string GenerateSecureToken()
    {
        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[32]; // 256 bits
        rng.GetBytes(bytes);
        
        // Convert to base64 and make it URL-safe
        return Convert.ToBase64String(bytes)
            .Replace('+', '-')
            .Replace('/', '_')
            .Replace("=", "");
    }
}
