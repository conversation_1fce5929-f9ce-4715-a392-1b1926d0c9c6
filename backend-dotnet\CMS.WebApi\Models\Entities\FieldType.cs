using System.ComponentModel.DataAnnotations;

namespace CMS.WebApi.Models.Entities;

public class FieldType : BaseEntity
{
    public int Id { get; set; }

    [Required(ErrorMessage = "Field type name is required")]
    [StringLength(255)]
    public string FieldTypeName { get; set; } = string.Empty;

    public string? FieldTypeDesc { get; set; }

    [StringLength(255)]
    public string? DisplayName { get; set; }

    public string? HelpText { get; set; }

    [StringLength(255)]
    public string? LogoImagePath { get; set; }

    public bool IsActive { get; set; } = true;

    public ICollection<FieldConfig> FieldConfigs { get; set; } = new List<FieldConfig>();
}
