using CMS.WebApi.Models.Entities;

namespace CMS.WebApi.Services.Interfaces;

public interface ICategoryService
{
    Task<IEnumerable<Category>> GetAllCategoriesAsync();
    Task<Category?> GetCategoryByIdAsync(int id);
    Task<IEnumerable<Category>> GetCategoriesByClientIdAsync(int clientId);
    Task<IEnumerable<Category>> GetCategoriesByParentIdAsync(int parentId);
    Task<Category> CreateCategoryAsync(Category category);
    Task<Category> UpdateCategoryAsync(int id, Category category);
    Task DeleteCategoryAsync(int id);
    Task<bool> CategoryExistsAsync(string categoryName, int clientId);
}
