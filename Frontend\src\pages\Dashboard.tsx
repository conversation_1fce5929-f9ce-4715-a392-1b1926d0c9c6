
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Database, FileText, Layers, Users, Plus, RefreshCw, Puzzle } from 'lucide-react';
import { useCollectionStore, useContentEntryStore, useAuthStore, useComponentStore, useMediaStore } from '@/lib/store';
import { collectionsApi, contentEntriesApi, componentsApi, mediaApi, usersApi } from '@/lib/api';
import { PageSpinner, CardSpinner, ButtonSpinner } from '@/components/ui/spinner';

import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export default function Dashboard() {
  const navigate = useNavigate();
  const { user, token } = useAuthStore();
  const { collections, setCollections, setLoading: setCollectionsLoading } = useCollectionStore();
  const { contentEntries, setContentEntries, setLoading: setEntriesLoading } = useContentEntryStore();
  const { components, setComponents, setLoading: setComponentsLoading } = useComponentStore();
  const { assets, setAssets } = useMediaStore();
  const [users, setUsers] = React.useState<any[]>([]);
  const [isLoading, setIsLoading] = React.useState(true);
  const [mediaLoading, setMediaLoading] = React.useState(false);
  const [usersLoading, setUsersLoading] = React.useState(false);

  React.useEffect(() => {
    const fetchDashboardData = async () => {
      // Check if we already have collections, content entries, components, media, and users in the store
      if (collections.length > 0 && contentEntries.length > 0 && components.length > 0 && assets.length > 0 && users.length > 0) {
        console.log('Using cached dashboard data');
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setCollectionsLoading(true);
      setEntriesLoading(true);
      setComponentsLoading(true);
      setMediaLoading(true);
      setUsersLoading(true);

      try {
        // Debug info removed for production

        // Fetch collections
        console.log('Fetching collections from API');
        console.log('Current user:', user);
        console.log('Current token:', token ? 'Present' : 'Missing');
        try {
          const collectionsResponse = await collectionsApi.getAll();
          console.log('Collections API response:', collectionsResponse);

          // Handle both array and non-array responses
          const collectionsData = Array.isArray(collectionsResponse.data)
            ? collectionsResponse.data
            : [];

          setCollections(collectionsData);
          console.log('Collections set:', collectionsData.length);
        } catch (collectionsError) {
          console.error('Error fetching collections:', collectionsError);
          // Set empty array if there's an error
          setCollections([]);
        }

        // Fetch content entries
        console.log('Fetching content entries from API');
        try {
          const entriesResponse = await contentEntriesApi.getAll();
          console.log('Content entries API response:', entriesResponse);

          // Handle both array and non-array responses
          const entriesData = Array.isArray(entriesResponse.data)
            ? entriesResponse.data
            : [];

          setContentEntries(entriesData);
          console.log('Content entries set:', entriesData.length);
        } catch (entriesError) {
          console.error('Error fetching content entries:', entriesError);
          // Set empty array if there's an error
          setContentEntries([]);
        }

        // Fetch components
        console.log('Fetching components from API');
        try {
          const componentsResponse = await componentsApi.getAll();
          console.log('Components API response:', componentsResponse);

          // Handle both array and non-array responses
          const componentsData = Array.isArray(componentsResponse.data)
            ? componentsResponse.data
            : [];

          setComponents(componentsData);
          console.log('Components set:', componentsData.length);
        } catch (componentsError) {
          console.error('Error fetching components:', componentsError);
          // Set empty array if there's an error
          setComponents([]);
        }

        // Fetch media assets
        console.log('Fetching media assets from API');
        try {
          const mediaResponse = await mediaApi.getAllAssets();
          console.log('Media API response:', mediaResponse);

          // Handle paginated response - media items are in Content property
          const mediaData = Array.isArray(mediaResponse.data?.Content)
            ? mediaResponse.data.Content
            : Array.isArray(mediaResponse.data)
            ? mediaResponse.data
            : [];

          setAssets(mediaData);
          console.log('Media assets set:', mediaData.length);
        } catch (mediaError) {
          console.error('Error fetching media assets:', mediaError);
          // Set empty array if there's an error
          setAssets([]);
        }

        // Fetch users
        console.log('Fetching users from API');
        try {
          const usersResponse = await usersApi.getAll();
          console.log('Users API response:', usersResponse);

          // Handle both array and non-array responses
          const usersData = Array.isArray(usersResponse.data)
            ? usersResponse.data
            : [];

          setUsers(usersData);
          console.log('Users set:', usersData.length);
        } catch (usersError) {
          console.error('Error fetching users:', usersError);
          // Set empty array if there's an error
          setUsers([]);
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        // Ensure we have empty arrays even if there's an error
        setCollections([]);
        setContentEntries([]);
        setComponents([]);
        setAssets([]);
        setUsers([]);
      } finally {
        setIsLoading(false);
        setCollectionsLoading(false);
        setEntriesLoading(false);
        setComponentsLoading(false);
        setMediaLoading(false);
        setUsersLoading(false);
      }
    };

    fetchDashboardData();
  }, []); // Remove dependencies to prevent infinite loops and request cancellations

  // Dashboard stats
  const stats = [
    {
      title: 'Collections',
      value: collections.length,
      icon: Database,
      color: 'bg-gradient-to-r from-indigo-500 to-purple-600',
      path: '/collections',
    },
    {
      title: 'Components',
      value: components.length,
      icon: Puzzle,
      color: 'bg-gradient-to-r from-purple-500 to-pink-500',
      path: '/components',
    },
    {
      title: 'Media Items',
      value: assets.length,
      icon: Layers,
      color: 'bg-gradient-to-r from-blue-500 to-indigo-500',
      path: '/media-library',
    },
    {
      title: 'Users',
      value: users.length,
      icon: Users,
      color: 'bg-gradient-to-r from-purple-600 to-indigo-600',
      path: '/settings/users',
    },
  ];

  // Function to manually refresh dashboard data
  const refreshDashboardData = async () => {
    setIsLoading(true);
    setCollectionsLoading(true);
    setEntriesLoading(true);
    setComponentsLoading(true);
    setMediaLoading(true);
    setUsersLoading(true);

    try {
      console.log('Manually refreshing dashboard data');

      // Fetch collections with error handling
      try {
        const collectionsResponse = await collectionsApi.getAll();
        console.log('Refresh - Collections API response:', collectionsResponse);

        const collectionsData = Array.isArray(collectionsResponse.data)
          ? collectionsResponse.data
          : [];

        setCollections(collectionsData);
        console.log('Refresh - Collections set:', collectionsData.length);
      } catch (collectionsError) {
        console.error('Error refreshing collections:', collectionsError);
        setCollections([]);
      }

      // Fetch content entries with error handling
      try {
        const entriesResponse = await contentEntriesApi.getAll();
        console.log('Refresh - Content entries API response:', entriesResponse);

        const entriesData = Array.isArray(entriesResponse.data)
          ? entriesResponse.data
          : [];

        setContentEntries(entriesData);
        console.log('Refresh - Content entries set:', entriesData.length);
      } catch (entriesError) {
        console.error('Error refreshing content entries:', entriesError);
        setContentEntries([]);
      }

      // Fetch components with error handling
      try {
        const componentsResponse = await componentsApi.getAll();
        console.log('Refresh - Components API response:', componentsResponse);

        const componentsData = Array.isArray(componentsResponse.data)
          ? componentsResponse.data
          : [];

        setComponents(componentsData);
        console.log('Refresh - Components set:', componentsData.length);
      } catch (componentsError) {
        console.error('Error refreshing components:', componentsError);
        setComponents([]);
      }

      // Fetch media assets with error handling
      try {
        const mediaResponse = await mediaApi.getAllAssets();
        console.log('Refresh - Media API response:', mediaResponse);

        // Handle paginated response - media items are in Content property
        const mediaData = Array.isArray(mediaResponse.data?.Content)
          ? mediaResponse.data.Content
          : Array.isArray(mediaResponse.data)
          ? mediaResponse.data
          : [];

        setAssets(mediaData);
        console.log('Refresh - Media assets set:', mediaData.length);
      } catch (mediaError) {
        console.error('Error refreshing media assets:', mediaError);
        setAssets([]);
      }

      // Fetch users with error handling
      try {
        const usersResponse = await usersApi.getAll();
        console.log('Refresh - Users API response:', usersResponse);

        const usersData = Array.isArray(usersResponse.data)
          ? usersResponse.data
          : [];

        setUsers(usersData);
        console.log('Refresh - Users set:', usersData.length);
      } catch (usersError) {
        console.error('Error refreshing users:', usersError);
        setUsers([]);
      }

    } catch (error) {
      console.error('Error refreshing dashboard data:', error);
      // Ensure we have empty arrays even if there's an error
      setCollections([]);
      setContentEntries([]);
      setComponents([]);
      setAssets([]);
      setUsers([]);
    } finally {
      setIsLoading(false);
      setCollectionsLoading(false);
      setEntriesLoading(false);
      setComponentsLoading(false);
      setMediaLoading(false);
      setUsersLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h1 className="text-3xl font-bold">Dashboard</h1>
          <Button variant="outline" size="icon" onClick={refreshDashboardData} disabled={isLoading} className="button-ripple interactive-hover dark-hover-lift dark-border-glow" title={isLoading ? 'Refreshing...' : 'Refresh Data'}>
            {isLoading ? <ButtonSpinner /> : <RefreshCw className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[
            { title: "Collections", description: "Loading collections..." },
            { title: "Components", description: "Loading components..." },
            { title: "Media Items", description: "Loading media..." },
            { title: "Users", description: "Loading users..." }
          ].map((item, i) => (
            <CardSpinner key={i} title={item.title} description={item.description} />
          ))}
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            {stats.map((stat, index) => (
              <Card
                key={index}
                className="group relative overflow-hidden hover:shadow-lg hover:shadow-purple-500/25 dark:hover:shadow-purple-500/40 transition-all duration-300 hover:-translate-y-1 hover:scale-105 cursor-pointer border-2 hover:border-purple-300/50 dark:hover:border-purple-400/50 bg-gradient-to-br from-white/90 to-purple-50/30 dark:from-slate-800/90 dark:to-purple-900/20 backdrop-blur-sm dark-glass dark-hover-lift"
                onClick={() => navigate(stat.path)}
              >
                {/* Animated background gradient */}
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-pink-500/5 dark:from-blue-500/10 dark:via-purple-500/10 dark:to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                {/* Shimmer effect */}
                <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 bg-gradient-to-r from-transparent via-white/20 dark:via-white/10 to-transparent"></div>

                <CardHeader className="pb-2 relative z-10">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-sm font-medium group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors duration-300">{stat.title}</CardTitle>
                    <div className={`p-2 rounded-full ${stat.color} group-hover:scale-110 group-hover:rotate-12 transition-all duration-300 shadow-lg group-hover:shadow-xl dark:shadow-purple-500/30 dark-glow`}>
                      <stat.icon className="h-4 w-4 text-white group-hover:animate-pulse" />
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="relative z-10">
                  <p className="text-3xl font-bold group-hover:text-purple-800 dark:group-hover:text-purple-200 transition-colors duration-300 group-hover:scale-110 transform origin-left">{stat.value}</p>

                  {/* Animated dots */}
                  <div className="flex space-x-1 mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-500 delay-200">
                    <div className="w-1 h-1 bg-indigo-400 dark:bg-indigo-300 rounded-full animate-bounce"></div>
                    <div className="w-1 h-1 bg-purple-400 dark:bg-purple-300 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                    <div className="w-1 h-1 bg-pink-400 dark:bg-pink-300 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                  </div>
                </CardContent>

                {/* Corner accent */}
                <div className="absolute top-0 right-0 w-0 h-0 border-l-[20px] border-l-transparent border-t-[20px] border-t-purple-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </Card>
            ))}
          </div>

          {/* Empty State for New Tenants */}
          {collections.length === 0 && !isLoading && (
            <Card className="mt-6">
              <CardHeader className="text-center">
                <CardTitle className="flex items-center justify-center space-x-2">
                  <Database className="h-6 w-6" />
                  <span>Welcome to Your CMS Dashboard!</span>
                </CardTitle>
                <CardDescription>
                  It looks like you're just getting started. Use the navigation menu to explore the CMS features and begin building your content structure.
                </CardDescription>
              </CardHeader>
              <CardContent className="text-center space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div className="p-4 border rounded-lg">
                    <Database className="h-8 w-8 mx-auto mb-2 text-blue-500" />
                    <h4 className="font-semibold">1. Explore Collections</h4>
                    <p className="text-muted-foreground">Browse and manage your content types</p>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <FileText className="h-8 w-8 mx-auto mb-2 text-green-500" />
                    <h4 className="font-semibold">2. Manage Content</h4>
                    <p className="text-muted-foreground">View and organize your content entries</p>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <Layers className="h-8 w-8 mx-auto mb-2 text-purple-500" />
                    <h4 className="font-semibold">3. Media Library</h4>
                    <p className="text-muted-foreground">Browse and manage your media files</p>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-center">
                <Button variant="outline" onClick={refreshDashboardData} className="flex items-center space-x-2 button-ripple interactive-hover">
                  <RefreshCw className="h-4 w-4" />
                  <span>Refresh Data</span>
                </Button>
              </CardFooter>
            </Card>
          )}


        </>
      )}

      {/* Recent Activity and Content Distribution sections have been removed */}
    </div>
  );
}
