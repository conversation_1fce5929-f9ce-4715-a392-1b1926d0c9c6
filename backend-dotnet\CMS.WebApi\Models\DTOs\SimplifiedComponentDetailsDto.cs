namespace CMS.WebApi.Models.DTOs;

public class SimplifiedComponentDetailsDto
{
    public int Id { get; set; }
    public string? ComponentName { get; set; }
    public string? ComponentDisplayName { get; set; }
    public string? ComponentApiId { get; set; }
    public bool? IsActive { get; set; }
    public string? GetUrl { get; set; }
    public string? PostUrl { get; set; }
    public string? UpdateUrl { get; set; }
    public string? AdditionalInformation { get; set; }
    public string? AdditionalInfoImage { get; set; }
    public List<SimplifiedFieldDto> Fields { get; set; } = new List<SimplifiedFieldDto>();
    public List<SimplifiedChildComponentDto> ChildComponents { get; set; } = new List<SimplifiedChildComponentDto>();
}
