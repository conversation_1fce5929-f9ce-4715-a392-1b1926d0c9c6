using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.Responses;
using CMS.WebApi.Services.Interfaces;
using CMS.WebApi.Security;

namespace CMS.WebApi.Controllers;

[ApiController]
[Route("api/config-types")]
[Authorize(Policy = AuthorizationPolicies.JwtOrApiToken)]
[Tags("Config Type Management")]
public class ConfigTypeController : ControllerBase
{
    private readonly IConfigTypeService _configTypeService;
    private readonly ILogger<ConfigTypeController> _logger;

    public ConfigTypeController(IConfigTypeService configTypeService, ILogger<ConfigTypeController> logger)
    {
        _configTypeService = configTypeService;
        _logger = logger;
    }

    /// <summary>
    /// Get all config types
    /// </summary>
    /// <returns>List of all config types</returns>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<ConfigType>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<IEnumerable<ConfigType>>> GetAllConfigTypes()
    {
        try
        {
            var configTypes = await _configTypeService.GetAllConfigTypesAsync();
            
            if (!configTypes.Any())
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = "No config types found",
                    Path = Request.Path
                });
            }

            _logger.LogInformation("Retrieved {Count} config types", configTypes.Count());
            return Ok(configTypes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve config types");
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving config types",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get active config types only
    /// </summary>
    /// <returns>List of active config types</returns>
    [HttpGet("active")]
    [ProducesResponseType(typeof(IEnumerable<ConfigType>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<IEnumerable<ConfigType>>> GetActiveConfigTypes()
    {
        try
        {
            var configTypes = await _configTypeService.GetActiveConfigTypesAsync();
            
            if (!configTypes.Any())
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = "No active config types found",
                    Path = Request.Path
                });
            }

            _logger.LogInformation("Retrieved {Count} active config types", configTypes.Count());
            return Ok(configTypes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve active config types");
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving active config types",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get config type by ID
    /// </summary>
    /// <param name="id">Config type ID</param>
    /// <returns>Config type details</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(ConfigType), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ConfigType>> GetConfigTypeById(int id)
    {
        try
        {
            var configType = await _configTypeService.GetConfigTypeByIdAsync(id);
            
            if (configType == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Config type with ID {id} not found",
                    Path = Request.Path
                });
            }

            return Ok(configType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve config type with ID: {ConfigTypeId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving the config type",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Create a new config type
    /// </summary>
    /// <param name="configType">Config type details</param>
    /// <returns>Created config type</returns>
    [HttpPost]
    [ProducesResponseType(typeof(ConfigType), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ConfigType>> CreateConfigType([FromBody] ConfigType configType)
    {
        try
        {
            // Check if config type with same name already exists
            if (await _configTypeService.ConfigTypeExistsAsync(configType.ConfigTypeName))
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = $"Config type with name '{configType.ConfigTypeName}' already exists",
                    Path = Request.Path
                });
            }

            var createdConfigType = await _configTypeService.CreateConfigTypeAsync(configType);
            _logger.LogInformation("Config type created successfully: {ConfigTypeName}", createdConfigType.ConfigTypeName);

            return CreatedAtAction(nameof(GetConfigTypeById), new { id = createdConfigType.Id }, createdConfigType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create config type: {ConfigTypeName}", configType.ConfigTypeName);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while creating the config type",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Update an existing config type
    /// </summary>
    /// <param name="id">Config type ID</param>
    /// <param name="configType">Updated config type details</param>
    /// <returns>Updated config type</returns>
    [HttpPut("{id}")]
    [ProducesResponseType(typeof(ConfigType), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ConfigType>> UpdateConfigType(int id, [FromBody] ConfigType configType)
    {
        try
        {
            var updatedConfigType = await _configTypeService.UpdateConfigTypeAsync(id, configType);
            _logger.LogInformation("Config type updated successfully: {ConfigTypeId}", id);
            return Ok(updatedConfigType);
        }
        catch (ArgumentException ex)
        {
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update config type: {ConfigTypeId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while updating the config type",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Delete a config type
    /// </summary>
    /// <param name="id">Config type ID</param>
    /// <returns>No content</returns>
    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult> DeleteConfigType(int id)
    {
        try
        {
            var configType = await _configTypeService.GetConfigTypeByIdAsync(id);
            if (configType == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Config type with ID {id} not found",
                    Path = Request.Path
                });
            }

            await _configTypeService.DeleteConfigTypeAsync(id);
            _logger.LogInformation("Config type deleted successfully: {ConfigTypeId}", id);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete config type: {ConfigTypeId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while deleting the config type",
                Path = Request.Path
            });
        }
    }
}
