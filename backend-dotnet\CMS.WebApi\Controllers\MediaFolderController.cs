using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.DTOs;
using CMS.WebApi.Models.Responses;
using CMS.WebApi.Services.Interfaces;
using CMS.WebApi.Security;

namespace CMS.WebApi.Controllers;

[ApiController]
[Route("api/media-folders")]
[Authorize(Policy = AuthorizationPolicies.JwtOrApiToken)]
[Tags("Media Folder Management")]
public class MediaFolderController : ControllerBase
{
    private readonly IMediaFolderService _mediaFolderService;
    private readonly ILogger<MediaFolderController> _logger;

    public MediaFolderController(IMediaFolderService mediaFolderService, ILogger<MediaFolderController> logger)
    {
        _mediaFolderService = mediaFolderService;
        _logger = logger;
    }

    /// <summary>
    /// Get all media folders
    /// </summary>
    /// <returns>List of all media folders</returns>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<MediaFolderDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<IEnumerable<MediaFolderDto>>> GetAllMediaFolders()
    {
        try
        {
            var mediaFolders = await _mediaFolderService.GetAllMediaFoldersAsync();

            if (!mediaFolders.Any())
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = "No media folders found",
                    Path = Request.Path
                });
            }

            // Convert to DTOs with media count
            var folderDtos = mediaFolders.Select(folder => new MediaFolderDto
            {
                Id = folder.Id,
                FolderName = folder.FolderName,
                Description = folder.Description,
                ParentId = folder.ParentId,
                ParentName = folder.Parent?.FolderName,
                UserId = folder.UserId,
                CreatedByUsername = folder.User?.UserName,
                MediaCount = folder.MediaFiles?.Count ?? 0,
                CreatedAt = folder.CreatedAt,
                ModifiedAt = folder.ModifiedAt
            }).ToList();

            _logger.LogInformation("Retrieved {Count} media folders", folderDtos.Count);
            return Ok(folderDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve media folders");
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving media folders",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get media folder by ID
    /// </summary>
    /// <param name="id">Media folder ID</param>
    /// <returns>Media folder details</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(MediaFolder), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<MediaFolder>> GetMediaFolderById(int id)
    {
        try
        {
            var mediaFolder = await _mediaFolderService.GetMediaFolderByIdAsync(id);
            
            if (mediaFolder == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Media folder with ID {id} not found",
                    Path = Request.Path
                });
            }

            return Ok(mediaFolder);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve media folder with ID: {MediaFolderId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving the media folder",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get media folders by parent folder
    /// </summary>
    /// <param name="parentId">Parent folder ID (null for root folders)</param>
    /// <returns>List of child media folders</returns>
    [HttpGet("by-parent/{parentId?}")]
    [ProducesResponseType(typeof(IEnumerable<MediaFolderDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<MediaFolderDto>>> GetMediaFoldersByParent(int? parentId = null)
    {
        try
        {
            var mediaFolders = await _mediaFolderService.GetMediaFoldersByParentAsync(parentId);

            // Convert to DTOs with media count
            var folderDtos = mediaFolders.Select(folder => new MediaFolderDto
            {
                Id = folder.Id,
                FolderName = folder.FolderName,
                Description = folder.Description,
                ParentId = folder.ParentId,
                ParentName = folder.Parent?.FolderName,
                UserId = folder.UserId,
                CreatedByUsername = folder.User?.UserName,
                MediaCount = folder.MediaFiles?.Count ?? 0,
                CreatedAt = folder.CreatedAt,
                ModifiedAt = folder.ModifiedAt
            }).ToList();

            _logger.LogInformation("Retrieved {Count} media folders for parent {ParentId}", folderDtos.Count, parentId);
            return Ok(folderDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve media folders for parent: {ParentId}", parentId);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving media folders",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Create a new media folder
    /// </summary>
    /// <param name="mediaFolder">Media folder details</param>
    /// <returns>Created media folder</returns>
    [HttpPost]
    [ProducesResponseType(typeof(MediaFolder), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<MediaFolder>> CreateMediaFolder([FromBody] MediaFolder mediaFolder)
    {
        try
        {
            // Check if folder with same name already exists in the same parent
            if (await _mediaFolderService.MediaFolderExistsAsync(mediaFolder.FolderName, mediaFolder.ParentId))
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = $"Media folder with name '{mediaFolder.FolderName}' already exists in this location",
                    Path = Request.Path
                });
            }

            var createdMediaFolder = await _mediaFolderService.CreateMediaFolderAsync(mediaFolder);
            _logger.LogInformation("Media folder created successfully: {FolderName}", createdMediaFolder.FolderName);

            return CreatedAtAction(nameof(GetMediaFolderById), new { id = createdMediaFolder.Id }, createdMediaFolder);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create media folder: {FolderName}", mediaFolder.FolderName);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while creating the media folder",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Update an existing media folder
    /// </summary>
    /// <param name="id">Media folder ID</param>
    /// <param name="mediaFolder">Updated media folder details</param>
    /// <returns>Updated media folder</returns>
    [HttpPut("{id}")]
    [ProducesResponseType(typeof(MediaFolder), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<MediaFolder>> UpdateMediaFolder(int id, [FromBody] MediaFolder mediaFolder)
    {
        try
        {
            var updatedMediaFolder = await _mediaFolderService.UpdateMediaFolderAsync(id, mediaFolder);
            _logger.LogInformation("Media folder updated successfully: {MediaFolderId}", id);
            return Ok(updatedMediaFolder);
        }
        catch (ArgumentException ex)
        {
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update media folder: {MediaFolderId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while updating the media folder",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Delete a media folder
    /// </summary>
    /// <param name="id">Media folder ID</param>
    /// <returns>No content</returns>
    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult> DeleteMediaFolder(int id)
    {
        try
        {
            var mediaFolder = await _mediaFolderService.GetMediaFolderByIdAsync(id);
            if (mediaFolder == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Media folder with ID {id} not found",
                    Path = Request.Path
                });
            }

            // Check if folder has children or media files
            if (await _mediaFolderService.HasChildrenOrMediaAsync(id))
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = "Cannot delete folder that contains subfolders or media files",
                    Path = Request.Path
                });
            }

            await _mediaFolderService.DeleteMediaFolderAsync(id);
            _logger.LogInformation("Media folder deleted successfully: {MediaFolderId}", id);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete media folder: {MediaFolderId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while deleting the media folder",
                Path = Request.Path
            });
        }
    }
}
