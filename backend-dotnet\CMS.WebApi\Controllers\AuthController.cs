using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.Requests;
using CMS.WebApi.Models.Responses;
using CMS.WebApi.Services.Interfaces;

namespace CMS.WebApi.Controllers;

[ApiController]
[Route("api/[controller]")]
[Tags("Authentication")]
public class AuthController : ControllerBase
{
    private readonly UserManager<User> _userManager;
    private readonly SignInManager<User> _signInManager;
    private readonly IJwtTokenService _jwtTokenService;
    private readonly IUserService _userService;
    private readonly ILogger<AuthController> _logger;

    public AuthController(
        UserManager<User> userManager,
        SignInManager<User> signInManager,
        IJwtTokenService jwtTokenService,
        IUserService userService,
        ILogger<AuthController> logger)
    {
        _userManager = userManager;
        _signInManager = signInManager;
        _jwtTokenService = jwtTokenService;
        _userService = userService;
        _logger = logger;
    }

    /// <summary>
    /// Health check endpoint for authentication service
    /// </summary>
    /// <returns>Service status</returns>
    [HttpGet("health")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    public IActionResult Health()
    {
        return Ok(new { status = "healthy", service = "authentication", timestamp = DateTime.UtcNow });
    }

    /// <summary>
    /// Authenticate user and return JWT token
    /// </summary>
    /// <param name="request">Login credentials</param>
    /// <returns>JWT token response</returns>
    [HttpPost("login")]
    [ProducesResponseType(typeof(JwtAuthResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<JwtAuthResponse>> Login([FromBody] LoginRequest request)
    {
        _logger.LogInformation("Login attempt for username: {Username}", request.Username);

        try
        {
            // Find user by username
            var user = await _userManager.FindByNameAsync(request.Username);
            if (user == null)
            {
                _logger.LogWarning("Login failed: User not found - {Username}", request.Username);
                return Unauthorized(new ErrorResponse
                {
                    Status = 401,
                    Error = "Unauthorized",
                    Message = "Invalid username or password",
                    Path = Request.Path
                });
            }

            // Check if user is active
            if (!user.IsActive)
            {
                _logger.LogWarning("Login failed: User is inactive - {Username}", request.Username);
                return Unauthorized(new ErrorResponse
                {
                    Status = 401,
                    Error = "Unauthorized",
                    Message = "User account is inactive",
                    Path = Request.Path
                });
            }

            // Validate password
            var result = await _signInManager.CheckPasswordSignInAsync(user, request.Password, false);
            if (!result.Succeeded)
            {
                _logger.LogWarning("Login failed: Invalid password - {Username}", request.Username);
                return Unauthorized(new ErrorResponse
                {
                    Status = 401,
                    Error = "Unauthorized",
                    Message = "Invalid username or password",
                    Path = Request.Path
                });
            }

            // Generate JWT token
            var token = _jwtTokenService.GenerateToken(user, string.Empty);
            var tokenExpiration = _jwtTokenService.GetTokenExpiration(token);

            _logger.LogInformation("Login successful for user: {Username}", request.Username);

            return Ok(new JwtAuthResponse
            {
                AccessToken = token,
                TokenType = "Bearer",
                ExpiresAt = tokenExpiration,
                Username = user.UserName!,
                Email = user.Email!,
                TenantId = string.Empty
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Login failed for user: {Username}", request.Username);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred during login",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Register a new user
    /// </summary>
    /// <param name="request">User registration details</param>
    /// <returns>Success message</returns>
    [HttpPost("register")]
    [ProducesResponseType(typeof(string), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<string>> Register([FromBody] SignupRequest request)
    {
        _logger.LogInformation("Registering user: {Username} with email: {Email}", request.Username, request.Email);

        try
        {
            // Check if user already exists
            if (await _userService.UserExistsAsync(request.Username))
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = "Username already exists",
                    Path = Request.Path
                });
            }

            // Check if email already exists
            if (await _userService.EmailExistsAsync(request.Email))
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = "Email already exists",
                    Path = Request.Path
                });
            }

            // Create new user
            var user = new User
            {
                UserName = request.Username,
                Email = request.Email,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            var result = await _userManager.CreateAsync(user, request.Password);
            if (result.Succeeded)
            {
                _logger.LogInformation("User registered successfully: {Username}", request.Username);
                return CreatedAtAction(nameof(Register), new { username = user.UserName }, "User registered successfully");
            }

            var errors = string.Join(", ", result.Errors.Select(e => e.Description));
            _logger.LogWarning("User registration failed: {Username}, Errors: {Errors}", request.Username, errors);
            
            return BadRequest(new ErrorResponse
            {
                Status = 400,
                Error = "Bad Request",
                Message = $"User registration failed: {errors}",
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Registration failed for user: {Username}", request.Username);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred during registration",
                Path = Request.Path
            });
        }
    }
}
