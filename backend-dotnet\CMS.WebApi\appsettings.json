{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=content_new;Username=********;Password=********"}, "JwtSettings": {"SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!", "Issuer": "CMS.WebApi", "Audience": "CMS.WebApi.Users", "ExpirationInMinutes": 1440}, "MultiTenancy": {"DefaultTenant": "public"}, "FileUpload": {"UploadDirectory": "media-uploads", "BaseUrl": "http://************:5000", "MaxFileSize": 52428800, "AllowedExtensions": [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".pdf", ".doc", ".docx", ".txt", ".rtf", ".mp4", ".avi", ".mov", ".mp3", ".wav", ".zip", ".rar"], "AllowedMimeTypes": ["image/jpeg", "image/png", "image/gif", "image/bmp", "image/webp", "application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "text/plain", "application/rtf", "video/mp4", "video/x-msvideo", "video/quicktime", "audio/mpeg", "audio/wav", "application/zip", "application/x-rar-compressed"], "EnableVirusScan": false, "GenerateThumbnails": true, "ThumbnailWidth": 200, "ThumbnailHeight": 200, "PreserveOriginalFileName": false, "CleanupOldFilesAfterDays": 365}, "Cors": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:5173", "http://************:3000", "http://************:5173"], "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "AllowedHeaders": ["*"]}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/cms-api-.log", "rollingInterval": "Day", "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}]}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information"}}, "AllowedHosts": "*"}