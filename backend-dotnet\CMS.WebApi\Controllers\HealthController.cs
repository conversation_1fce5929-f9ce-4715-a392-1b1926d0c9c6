using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using CMS.WebApi.Data;

namespace CMS.WebApi.Controllers;

[ApiController]
[Route("api/[controller]")]
[Tags("Health Check")]
public class HealthController : ControllerBase
{
    private readonly CmsDbContext _context;
    private readonly ILogger<HealthController> _logger;

    public HealthController(CmsDbContext context, ILogger<HealthController> logger)
    {
        _context = context;
        _logger = logger;
    }

    /// <summary>
    /// Basic health check endpoint
    /// </summary>
    /// <returns>Health status</returns>
    [HttpGet]
    [AllowAnonymous]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(object), StatusCodes.Status503ServiceUnavailable)]
    public async Task<ActionResult<object>> GetHealth()
    {
        try
        {
            var healthStatus = new
            {
                Status = "Healthy",
                Timestamp = DateTime.UtcNow,
                Version = GetType().Assembly.GetName().Version?.ToString(),
                Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT"),
                Checks = await PerformHealthChecksAsync()
            };

            var hasUnhealthyChecks = healthStatus.Checks.Any(c => ((dynamic)c).Status != "Healthy");
            
            if (hasUnhealthyChecks)
            {
                return StatusCode(503, new
                {
                    Status = "Unhealthy",
                    Timestamp = healthStatus.Timestamp,
                    Version = healthStatus.Version,
                    Environment = healthStatus.Environment,
                    Checks = healthStatus.Checks
                });
            }

            return Ok(healthStatus);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Health check failed");
            
            return StatusCode(503, new
            {
                Status = "Unhealthy",
                Timestamp = DateTime.UtcNow,
                Error = "Health check failed",
                Message = ex.Message
            });
        }
    }

    /// <summary>
    /// Detailed health check with database connectivity
    /// </summary>
    /// <returns>Detailed health status</returns>
    [HttpGet("detailed")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(object), StatusCodes.Status503ServiceUnavailable)]
    public async Task<ActionResult<object>> GetDetailedHealth()
    {
        try
        {
            var checks = await PerformDetailedHealthChecksAsync();
            var hasUnhealthyChecks = checks.Any(c => ((dynamic)c).Status != "Healthy");
            
            var healthStatus = new
            {
                Status = hasUnhealthyChecks ? "Unhealthy" : "Healthy",
                Timestamp = DateTime.UtcNow,
                Version = GetType().Assembly.GetName().Version?.ToString(),
                Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT"),
                MachineName = Environment.MachineName,
                ProcessId = Environment.ProcessId,
                WorkingSet = GC.GetTotalMemory(false),
                Checks = checks
            };

            return hasUnhealthyChecks ? StatusCode(503, healthStatus) : Ok(healthStatus);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Detailed health check failed");
            
            return StatusCode(503, new
            {
                Status = "Unhealthy",
                Timestamp = DateTime.UtcNow,
                Error = "Detailed health check failed",
                Message = ex.Message
            });
        }
    }

    private async Task<List<object>> PerformHealthChecksAsync()
    {
        var checks = new List<object>();

        // Database connectivity check
        try
        {
            await _context.Database.CanConnectAsync();
            checks.Add(new { Name = "Database", Status = "Healthy", ResponseTime = "< 1s" });
        }
        catch (Exception ex)
        {
            checks.Add(new { Name = "Database", Status = "Unhealthy", Error = ex.Message });
        }

        return checks;
    }

    private async Task<List<object>> PerformDetailedHealthChecksAsync()
    {
        var checks = new List<object>();

        // Database connectivity check with timing
        var dbCheckStart = DateTime.UtcNow;
        try
        {
            await _context.Database.CanConnectAsync();
            var dbCheckDuration = DateTime.UtcNow - dbCheckStart;
            checks.Add(new 
            { 
                Name = "Database", 
                Status = "Healthy", 
                ResponseTime = $"{dbCheckDuration.TotalMilliseconds:F0}ms",
                Details = new { ConnectionString = _context.Database.GetConnectionString()?.Split(';')[0] }
            });
        }
        catch (Exception ex)
        {
            var dbCheckDuration = DateTime.UtcNow - dbCheckStart;
            checks.Add(new 
            { 
                Name = "Database", 
                Status = "Unhealthy", 
                ResponseTime = $"{dbCheckDuration.TotalMilliseconds:F0}ms",
                Error = ex.Message 
            });
        }

        // System status check
        checks.Add(new
        {
            Name = "System",
            Status = "Healthy",
            Details = new { Mode = "Single-tenant" }
        });

        // Memory check
        var totalMemory = GC.GetTotalMemory(false);
        var memoryStatus = totalMemory > 500_000_000 ? "Warning" : "Healthy"; // 500MB threshold
        checks.Add(new 
        { 
            Name = "Memory", 
            Status = memoryStatus,
            Details = new 
            { 
                TotalMemory = $"{totalMemory / 1024 / 1024:F0} MB",
                Gen0Collections = GC.CollectionCount(0),
                Gen1Collections = GC.CollectionCount(1),
                Gen2Collections = GC.CollectionCount(2)
            }
        });

        return checks;
    }
}
