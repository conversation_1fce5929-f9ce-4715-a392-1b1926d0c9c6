using Microsoft.EntityFrameworkCore;
using CMS.WebApi.Data;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Services.Interfaces;

namespace CMS.WebApi.Services.Implementations;

public class MediaFolderService : IMediaFolderService
{
    private readonly CmsDbContext _context;
    private readonly ILogger<MediaFolderService> _logger;

    public MediaFolderService(CmsDbContext context, ILogger<MediaFolderService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<IEnumerable<MediaFolder>> GetAllMediaFoldersAsync()
    {
        return await _context.MediaFolders
            .Include(f => f.Parent)
            .Include(f => f.User)
            .Include(f => f.MediaFiles)
            .OrderBy(f => f.FolderName)
            .ToListAsync();
    }

    public async Task<MediaFolder?> GetMediaFolderByIdAsync(int id)
    {
        return await _context.MediaFolders
            .Include(f => f.Parent)
            .Include(f => f.User)
            .Include(f => f.Children)
            .FirstOrDefaultAsync(f => f.Id == id);
    }

    public async Task<IEnumerable<MediaFolder>> GetMediaFoldersByParentAsync(int? parentId)
    {
        return await _context.MediaFolders
            .Include(f => f.User)
            .Include(f => f.MediaFiles)
            .Where(f => f.ParentId == parentId)
            .OrderBy(f => f.FolderName)
            .ToListAsync();
    }

    public async Task<MediaFolder> CreateMediaFolderAsync(MediaFolder folder)
    {
        // Check if folder with same name already exists in the same parent
        if (await MediaFolderExistsAsync(folder.FolderName, folder.ParentId))
        {
            throw new InvalidOperationException($"Folder '{folder.FolderName}' already exists in this location");
        }

        folder.CreatedAt = DateTime.UtcNow;
        _context.MediaFolders.Add(folder);
        await _context.SaveChangesAsync();
        
        _logger.LogInformation("Media folder created successfully: {FolderName}", folder.FolderName);
        return folder;
    }

    public async Task<MediaFolder> UpdateMediaFolderAsync(int id, MediaFolder folder)
    {
        var existingFolder = await _context.MediaFolders.FindAsync(id);
        if (existingFolder == null)
            throw new ArgumentException($"Media folder with ID {id} not found");

        // Check if new name conflicts with existing folders
        if (existingFolder.FolderName != folder.FolderName &&
            await MediaFolderExistsAsync(folder.FolderName, folder.ParentId))
        {
            throw new InvalidOperationException($"Folder '{folder.FolderName}' already exists in this location");
        }

        existingFolder.FolderName = folder.FolderName;
        existingFolder.Description = folder.Description;
        existingFolder.ParentId = folder.ParentId;
        existingFolder.ModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        _logger.LogInformation("Media folder updated successfully: {FolderId}", id);
        return existingFolder;
    }

    public async Task DeleteMediaFolderAsync(int id)
    {
        var folder = await _context.MediaFolders
            .Include(f => f.Children)
            .Include(f => f.MediaFiles)
            .FirstOrDefaultAsync(f => f.Id == id);
        
        if (folder != null)
        {
            // Check if folder has children or media files
            if (folder.Children.Any())
            {
                throw new InvalidOperationException("Cannot delete folder that contains subfolders");
            }

            if (folder.MediaFiles.Any())
            {
                throw new InvalidOperationException("Cannot delete folder that contains media files");
            }

            _context.MediaFolders.Remove(folder);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Media folder deleted successfully: {FolderId}", id);
        }
    }

    public async Task<bool> MediaFolderExistsAsync(string name, int? parentId)
    {
        return await _context.MediaFolders.AnyAsync(f =>
            f.FolderName == name && f.ParentId == parentId);
    }

    public async Task<bool> HasChildrenOrMediaAsync(int folderId)
    {
        var folder = await _context.MediaFolders
            .Include(f => f.Children)
            .Include(f => f.MediaFiles)
            .FirstOrDefaultAsync(f => f.Id == folderId);

        return folder != null && (folder.Children.Any() || folder.MediaFiles.Any());
    }
}
