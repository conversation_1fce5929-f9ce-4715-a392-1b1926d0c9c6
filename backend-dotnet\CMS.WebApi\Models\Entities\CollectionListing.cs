using System.ComponentModel.DataAnnotations;

namespace CMS.WebApi.Models.Entities;

public class CollectionListing : BaseEntity
{
    public int Id { get; set; }

    [Required(ErrorMessage = "Collection name is required")]
    [StringLength(255)]
    public string CollectionName { get; set; } = string.Empty;

    public string? CollectionDesc { get; set; }

    public string? AdditionalInformation { get; set; }

    public string? DisclaimerText { get; set; }

    [Required]
    [StringLength(255)]
    public string CollectionApiId { get; set; } = string.Empty;

    [Required]
    public int CategoryId { get; set; }
    public Category? Category { get; set; }

    public ICollection<CollectionComponent> Components { get; set; } = new List<CollectionComponent>();
    public ICollection<CollectionField> Fields { get; set; } = new List<CollectionField>();
    public ICollection<ContentEntry> ContentEntries { get; set; } = new List<ContentEntry>();
}
