using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.Responses;
using CMS.WebApi.Services.Interfaces;
using CMS.WebApi.Security;

namespace CMS.WebApi.Controllers;

[ApiController]
[Route("api/field-types")]
[Authorize(Policy = AuthorizationPolicies.JwtOrApiToken)]
[Tags("Field Type Management")]
public class FieldTypeController : ControllerBase
{
    private readonly IFieldTypeService _fieldTypeService;
    private readonly ILogger<FieldTypeController> _logger;

    public FieldTypeController(IFieldTypeService fieldTypeService, ILogger<FieldTypeController> logger)
    {
        _fieldTypeService = fieldTypeService;
        _logger = logger;
    }

    /// <summary>
    /// Get all field types
    /// </summary>
    /// <returns>List of all field types</returns>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<FieldType>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<IEnumerable<FieldType>>> GetAllFieldTypes()
    {
        try
        {
            var fieldTypes = await _fieldTypeService.GetAllFieldTypesAsync();
            
            if (!fieldTypes.Any())
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = "No field types found",
                    Path = Request.Path
                });
            }

            _logger.LogInformation("Retrieved {Count} field types", fieldTypes.Count());
            return Ok(fieldTypes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve field types");
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving field types",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get active field types only
    /// </summary>
    /// <returns>List of active field types</returns>
    [HttpGet("active")]
    [ProducesResponseType(typeof(IEnumerable<FieldType>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<IEnumerable<FieldType>>> GetActiveFieldTypes()
    {
        try
        {
            var fieldTypes = await _fieldTypeService.GetActiveFieldTypesAsync();
            
            if (!fieldTypes.Any())
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = "No active field types found",
                    Path = Request.Path
                });
            }

            _logger.LogInformation("Retrieved {Count} active field types", fieldTypes.Count());
            return Ok(fieldTypes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve active field types");
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving active field types",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get field type by ID
    /// </summary>
    /// <param name="id">Field type ID</param>
    /// <returns>Field type details</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(FieldType), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<FieldType>> GetFieldTypeById(int id)
    {
        try
        {
            var fieldType = await _fieldTypeService.GetFieldTypeByIdAsync(id);
            
            if (fieldType == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Field type with ID {id} not found",
                    Path = Request.Path
                });
            }

            return Ok(fieldType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve field type with ID: {FieldTypeId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving the field type",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Create a new field type
    /// </summary>
    /// <param name="fieldType">Field type details</param>
    /// <returns>Created field type</returns>
    [HttpPost]
    [ProducesResponseType(typeof(FieldType), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<FieldType>> CreateFieldType([FromBody] FieldType fieldType)
    {
        try
        {
            // Check if field type with same name already exists
            if (await _fieldTypeService.FieldTypeExistsAsync(fieldType.FieldTypeName))
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = $"Field type with name '{fieldType.FieldTypeName}' already exists",
                    Path = Request.Path
                });
            }

            var createdFieldType = await _fieldTypeService.CreateFieldTypeAsync(fieldType);
            _logger.LogInformation("Field type created successfully: {FieldTypeName}", createdFieldType.FieldTypeName);

            return CreatedAtAction(nameof(GetFieldTypeById), new { id = createdFieldType.Id }, createdFieldType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create field type: {FieldTypeName}", fieldType.FieldTypeName);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while creating the field type",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Update an existing field type
    /// </summary>
    /// <param name="id">Field type ID</param>
    /// <param name="fieldType">Updated field type details</param>
    /// <returns>Updated field type</returns>
    [HttpPut("{id}")]
    [ProducesResponseType(typeof(FieldType), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<FieldType>> UpdateFieldType(int id, [FromBody] FieldType fieldType)
    {
        try
        {
            var updatedFieldType = await _fieldTypeService.UpdateFieldTypeAsync(id, fieldType);
            _logger.LogInformation("Field type updated successfully: {FieldTypeId}", id);
            return Ok(updatedFieldType);
        }
        catch (ArgumentException ex)
        {
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update field type: {FieldTypeId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while updating the field type",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Delete a field type
    /// </summary>
    /// <param name="id">Field type ID</param>
    /// <returns>No content</returns>
    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult> DeleteFieldType(int id)
    {
        try
        {
            var fieldType = await _fieldTypeService.GetFieldTypeByIdAsync(id);
            if (fieldType == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Field type with ID {id} not found",
                    Path = Request.Path
                });
            }

            await _fieldTypeService.DeleteFieldTypeAsync(id);
            _logger.LogInformation("Field type deleted successfully: {FieldTypeId}", id);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete field type: {FieldTypeId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while deleting the field type",
                Path = Request.Path
            });
        }
    }
}
