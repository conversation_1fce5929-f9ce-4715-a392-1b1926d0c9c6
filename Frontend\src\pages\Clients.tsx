import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, ArrowUp, Search, Building, Edit, ArrowLeft } from 'lucide-react';
import { clientsApi } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useClickedClient } from '@/lib/store';
import { PageSpinner, ButtonSpinner } from '@/components/ui/spinner';

interface Client {
  id: number;
  name: string;
  createdBy: string;
  createdAt: string;
  modifiedBy: string;
  modifiedAt: string;
}

export default function Clients() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [createClientDialogOpen, setCreateClientDialogOpen] = useState(false);
  const [editClientDialogOpen, setEditClientDialogOpen] = useState(false);
  const [clientName, setClientName] = useState('');
  const [editingClient, setEditingClient] = useState<Client | null>(null);
  const [editClientName, setEditClientName] = useState('');
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  const { clientId, setClientId } = useClickedClient();

  // Fetch clients on component mount
  useEffect(() => {
    fetchClients();

    // Add scroll listener
    const handleScroll = () => {
      setShowScrollButton(window.scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const fetchClients = async () => {
    setLoading(true);
    try {
      const response = await clientsApi.getAll();
      setClients(response.data || []);
    } catch (error) {
      console.error('Error fetching clients:', error);
      toast({
        title: 'Error',
        description: 'Failed to load clients',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateClient = async () => {
    if (!clientName.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Client name is required',
        variant: 'destructive',
      });
      return;
    }

    setIsCreating(true);
    try {
      const clientData = {
        name: clientName.trim()
      };

      const response = await clientsApi.create(clientData);
      console.log('Client created successfully:', response.data);

      toast({
        title: 'Client created',
        description: `Client "${clientName}" has been created successfully`,
      });

      // Refresh clients list
      fetchClients();

      // Close the dialog and reset form
      setCreateClientDialogOpen(false);
      setClientName('');
    } catch (error: any) {
      console.error('Error creating client:', error);
      toast({
        title: 'Error',
        description: 'Failed to create client: ' + error.message,
        variant: 'destructive',
      });
    } finally {
      setIsCreating(false);
    }
  };

  // Handle edit client
  const handleEditClient = (client: Client) => {
    setEditingClient(client);
    setEditClientName(client.name);
    setEditClientDialogOpen(true);
  };

  // Handle update client
  const handleUpdateClient = async () => {
    if (!editClientName.trim() || !editingClient) {
      toast({
        title: 'Error',
        description: 'Client name cannot be empty',
        variant: 'destructive',
      });
      return;
    }

    setIsUpdating(true);
    try {
      const clientData = {
        name: editClientName.trim()
      };

      await clientsApi.update(editingClient.id.toString(), clientData);

      toast({
        title: 'Client updated',
        description: `Client "${editClientName}" has been updated successfully`,
      });

      // Refresh clients list
      fetchClients();

      // Close the dialog and reset form
      setEditClientDialogOpen(false);
      setEditingClient(null);
      setEditClientName('');
    } catch (error: any) {
      console.error('Error updating client:', error);
      toast({
        title: 'Error',
        description: 'Failed to update client: ' + (error.response?.data?.message || error.message),
        variant: 'destructive',
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const getFilteredClients = () => {
    if (!searchQuery.trim()) return clients;
    return clients.filter(client =>
      client.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4 md:space-y-0 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/dashboard')}
            className="mr-2 dark-hover-lift transition-all duration-300 hover:shadow-md dark:hover:shadow-purple-500/20"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <h1 className="text-3xl font-bold dark-text-glow">Clients</h1>
        </div>
        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search clients..."
              className="pl-8 w-64 dark-glass dark:border-purple-500/30 transition-all duration-300"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button onClick={() => setCreateClientDialogOpen(true)} className="button-ripple dark-hover-lift dark-border-glow transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/25 dark:hover:shadow-purple-500/40">
            <Plus className="mr-2 h-4 w-4" />
            Create Client
          </Button>
        </div>
      </div>



      <div className="space-y-4">
        {loading ? (
          <PageSpinner text="Loading clients..." />
        ) : getFilteredClients().length === 0 ? (
          <div className="dark-glass dark:border-purple-500/30 rounded-md p-6 text-center transition-all duration-300">
            <Building className="h-12 w-12 text-muted-foreground mx-auto mb-4 dark:text-purple-400" />
            <h3 className="text-lg font-medium mb-2 dark-text-glow">No clients found</h3>
            <p className="text-sm text-muted-foreground mb-4 dark:text-slate-300">
              {searchQuery ? 'No clients match your search query' : 'Create your first client to get started'}
            </p>
            <Button onClick={() => setCreateClientDialogOpen(true)} className="button-ripple dark-hover-lift dark-border-glow transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/25 dark:hover:shadow-purple-500/40">
              <Plus className="mr-2 h-4 w-4" />
              Create Client
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {getFilteredClients().map((client) => (
              <div
                key={client.id}
                className="group relative overflow-hidden hover:shadow-lg hover:shadow-purple-500/25 dark:hover:shadow-purple-500/40 transition-all duration-300 hover:-translate-y-1 hover:scale-105 cursor-pointer border-2 hover:border-purple-300/50 dark:hover:border-purple-400/50 bg-gradient-to-br from-white/90 to-purple-50/30 dark:from-slate-800/90 dark:to-purple-900/20 backdrop-blur-sm dark-glass dark-hover-lift"
                onClick={() => {
                  navigate(`/parent-categories`);
                  useClickedClient.getState().setClientId(client.id);
                }}
              >
                {/* Animated background gradient */}
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 via-blue-500/5 to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                {/* Shimmer effect */}
                <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 bg-gradient-to-r from-transparent via-white/20 dark:via-purple-400/20 to-transparent"></div>

                <div className="p-4 border-b dark:border-purple-500/30 relative z-10">
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-primary/10 dark:bg-purple-500/20 text-primary dark:text-purple-400 flex items-center justify-center mr-3 group-hover:scale-110 group-hover:rotate-12 transition-all duration-300 group-hover:bg-purple-100 dark:group-hover:bg-purple-500/30 group-hover:text-purple-600 dark:group-hover:text-purple-300 shadow-lg group-hover:shadow-xl dark:group-hover:shadow-purple-500/50">
                      <Building className="h-4 w-4 group-hover:animate-pulse" />
                    </div>
                    <h3 className="font-medium group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors duration-300 dark-text-glow">{client.name}</h3>
                  </div>
                </div>
                <div className="p-4 flex items-center justify-between relative z-10">
                  <div className="text-sm text-muted-foreground dark:text-slate-300">
                    <span className="group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors duration-300 group-hover:scale-105 transform origin-left">
                      Created {new Date(client.createdAt).toLocaleDateString()}
                    </span>

                    {/* Animated dots */}
                    <div className="flex space-x-1 mt-1 opacity-0 group-hover:opacity-100 transition-opacity duration-500 delay-200">
                      <div className="w-1 h-1 bg-purple-400 rounded-full animate-bounce"></div>
                      <div className="w-1 h-1 bg-blue-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                      <div className="w-1 h-1 bg-pink-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 group-hover:bg-purple-50 dark:group-hover:bg-purple-500/20 group-hover:text-purple-600 dark:group-hover:text-purple-300 transition-colors duration-300 dark-hover-lift"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditClient(client);
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Corner accent */}
                <div className="absolute top-0 right-0 w-0 h-0 border-l-[20px] border-l-transparent border-t-[20px] border-t-purple-500/20 dark:border-t-purple-400/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Create client dialog */}
      <Dialog open={createClientDialogOpen} onOpenChange={setCreateClientDialogOpen}>
        <DialogContent className="sm:max-w-[600px] dark-glass dark:border-purple-500/30">
          <DialogHeader className="flex flex-row items-center">
            <div className="flex items-center">
              <div className="flex items-center justify-center w-8 h-8 mr-2 bg-primary dark:bg-purple-500 text-primary-foreground rounded">
                C
              </div>
              <DialogTitle className="dark-text-glow">Create a client</DialogTitle>
            </div>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div>
              <h3 className="text-lg font-medium dark-text-glow">Client</h3>
              <p className="text-sm text-muted-foreground dark:text-slate-300">Create a client to organize your collections</p>
            </div>
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <label htmlFor="clientName" className="text-sm font-medium dark-text-glow">
                    Name
                  </label>
                  <Input
                    id="clientName"
                    placeholder="Enter client name"
                    value={clientName}
                    onChange={(e) => setClientName(e.target.value)}
                    className="dark-glass dark:border-purple-500/30 transition-all duration-300"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setCreateClientDialogOpen(false)} className="dark-hover-lift transition-all duration-300">
              Cancel
            </Button>
            <Button onClick={handleCreateClient} disabled={isCreating} className="button-ripple dark-hover-lift dark-border-glow transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/25 dark:hover:shadow-purple-500/40">
              {isCreating ? (
                <>
                  <ButtonSpinner />
                  Creating...
                </>
              ) : (
                'Create Client'
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit client dialog */}
      <Dialog open={editClientDialogOpen} onOpenChange={setEditClientDialogOpen}>
        <DialogContent className="sm:max-w-[600px] dark-glass dark:border-purple-500/30">
          <DialogHeader className="flex flex-row items-center">
            <div className="flex items-center">
              <div className="flex items-center justify-center w-8 h-8 mr-2 bg-primary dark:bg-purple-500 text-primary-foreground rounded">
                <Edit className="h-4 w-4" />
              </div>
              <DialogTitle className="dark-text-glow">Edit Client</DialogTitle>
            </div>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div>
              <h3 className="text-lg font-medium dark-text-glow">Rename Client</h3>
              <p className="text-sm text-muted-foreground dark:text-slate-300">Update the client name</p>
            </div>
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <label htmlFor="editClientName" className="text-sm font-medium dark-text-glow">
                    Name
                  </label>
                  <Input
                    id="editClientName"
                    placeholder="Enter client name"
                    value={editClientName}
                    onChange={(e) => setEditClientName(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleUpdateClient();
                      }
                    }}
                    className="dark-glass dark:border-purple-500/30 transition-all duration-300"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="flex justify-end space-x-2">
            <Button
              variant="outline"
              onClick={() => {
                setEditClientDialogOpen(false);
                setEditingClient(null);
                setEditClientName('');
              }}
              className="dark-hover-lift transition-all duration-300"
            >
              Cancel
            </Button>
            <Button onClick={handleUpdateClient} disabled={isUpdating} className="button-ripple dark-hover-lift dark-border-glow transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/25 dark:hover:shadow-purple-500/40">
              {isUpdating ? (
                <>
                  <ButtonSpinner />
                  Updating...
                </>
              ) : (
                'Update Client'
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Scroll to top button */}
      {showScrollButton && (
        <Button
          className="fixed bottom-6 right-6 rounded-full w-12 h-12 shadow-lg flex items-center justify-center bg-primary dark:bg-purple-500 hover:bg-primary/90 dark:hover:bg-purple-600 transition-all float-animation button-ripple dark-hover-lift dark-border-glow hover:shadow-purple-500/25 dark:hover:shadow-purple-500/40"
          onClick={scrollToTop}
          aria-label="Scroll to top"
        >
          <ArrowUp className="h-5 w-5 text-white" />
        </Button>
      )}
    </div>
  );
}