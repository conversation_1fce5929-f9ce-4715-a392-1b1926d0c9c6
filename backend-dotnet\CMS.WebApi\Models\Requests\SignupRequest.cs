using System.ComponentModel.DataAnnotations;

namespace CMS.WebApi.Models.Requests;

public class SignupRequest
{
    [Required(ErrorMessage = "Username is required")]
    [StringLength(50, ErrorMessage = "Username cannot exceed 50 characters")]
    public string Username { get; set; } = string.Empty;

    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email format")]
    [StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
    public string Email { get; set; } = string.Empty;

    [Required(ErrorMessage = "Password is required")]
    [StringLength(120, MinimumLength = 6, ErrorMessage = "Password must be between 6 and 120 characters")]
    public string Password { get; set; } = string.Empty;
}
