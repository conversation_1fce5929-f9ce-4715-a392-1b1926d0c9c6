using Microsoft.EntityFrameworkCore;
using CMS.WebApi.Data;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Services.Interfaces;

namespace CMS.WebApi.Services.Implementations;

public class ContentEntryService : IContentEntryService
{
    private readonly CmsDbContext _context;
    private readonly ILogger<ContentEntryService> _logger;

    public ContentEntryService(CmsDbContext context, ILogger<ContentEntryService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<IEnumerable<ContentEntry>> GetAllContentEntriesAsync()
    {
        return await _context.ContentEntries.ToListAsync();
    }

    public async Task<ContentEntry?> GetContentEntryByIdAsync(int id)
    {
        return await _context.ContentEntries.FindAsync(id);
    }

    public async Task<IEnumerable<ContentEntry>> GetContentEntriesByCollectionAsync(int collectionId)
    {
        return await _context.ContentEntries.Where(ce => ce.CollectionId == collectionId).ToListAsync();
    }

    public async Task<ContentEntry> CreateContentEntryAsync(ContentEntry contentEntry)
    {
        contentEntry.CreatedAt = DateTime.UtcNow;
        _context.ContentEntries.Add(contentEntry);
        await _context.SaveChangesAsync();

        _logger.LogInformation("Content entry created successfully: {EntryId}", contentEntry.Id);
        return contentEntry;
    }

    public async Task<ContentEntry> UpdateContentEntryAsync(int id, ContentEntry contentEntry)
    {
        var existingEntry = await _context.ContentEntries.FindAsync(id);
        if (existingEntry == null)
            throw new ArgumentException($"Content entry with ID {id} not found");

        existingEntry.DataJson = contentEntry.DataJson;
        existingEntry.ModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        _logger.LogInformation("Content entry updated successfully: {EntryId}", id);
        return existingEntry;
    }

    public async Task DeleteContentEntryAsync(int id)
    {
        var entry = await _context.ContentEntries.FindAsync(id);
        if (entry != null)
        {
            _context.ContentEntries.Remove(entry);
            await _context.SaveChangesAsync();
        }

        _logger.LogInformation("Content entry deleted successfully: {EntryId}", id);
    }

    public async Task<IEnumerable<ContentEntry>> SearchContentEntriesAsync(int collectionId, string searchText)
    {
        return await _context.ContentEntries
            .Where(ce => ce.CollectionId == collectionId && ce.DataJson != null && ce.DataJson.Contains(searchText))
            .ToListAsync();
    }

    public async Task<(IEnumerable<ContentEntry> Items, int TotalCount)> GetPagedContentEntriesAsync(
        int collectionId, int pageNumber, int pageSize, string? searchText = null)
    {
        var query = _context.ContentEntries.Where(ce => ce.CollectionId == collectionId);

        if (!string.IsNullOrEmpty(searchText))
        {
            query = query.Where(ce => ce.DataJson != null && ce.DataJson.Contains(searchText));
        }

        var totalCount = await query.CountAsync();
        var items = await query
            .OrderByDescending(ce => ce.CreatedAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return (items, totalCount);
    }

    public async Task<IEnumerable<ContentEntry>> SearchContentEntriesAsync(string query, int? collectionId = null)
    {
        var queryable = _context.ContentEntries.AsQueryable();

        // Filter by collection if specified
        if (collectionId.HasValue)
        {
            queryable = queryable.Where(ce => ce.CollectionId == collectionId.Value);
        }

        // Search in the JSON data field
        queryable = queryable.Where(ce => ce.DataJson != null && ce.DataJson.Contains(query));

        return await queryable
            .OrderByDescending(ce => ce.CreatedAt)
            .ToListAsync();
    }
}
