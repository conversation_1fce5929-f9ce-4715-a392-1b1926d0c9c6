using Microsoft.EntityFrameworkCore;
using CMS.WebApi.Data;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Services.Interfaces;

namespace CMS.WebApi.Services.Implementations;

public class CategoryService : ICategoryService
{
    private readonly CmsDbContext _context;
    private readonly ILogger<CategoryService> _logger;

    public CategoryService(CmsDbContext context, ILogger<CategoryService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<IEnumerable<Category>> GetAllCategoriesAsync()
    {
        return await _context.Categories.Include(c => c.Client).ToListAsync();
    }

    public async Task<Category?> GetCategoryByIdAsync(int id)
    {
        return await _context.Categories.Include(c => c.Client).FirstOrDefaultAsync(c => c.Id == id);
    }

    public async Task<IEnumerable<Category>> GetCategoriesByClientIdAsync(int clientId)
    {
        return await _context.Categories.Where(c => c.ClientId == clientId).ToListAsync();
    }

    public async Task<IEnumerable<Category>> GetCategoriesByParentIdAsync(int parentId)
    {
        return await _context.Categories
            .Include(c => c.Client)
            .Where(c => c.ParentCategoryId == parentId)
            .ToListAsync();
    }

    public async Task<Category> CreateCategoryAsync(Category category)
    {
        category.CreatedAt = DateTime.UtcNow;
        _context.Categories.Add(category);
        await _context.SaveChangesAsync();
        return category;
    }

    public async Task<Category> UpdateCategoryAsync(int id, Category category)
    {
        var existingCategory = await _context.Categories.FindAsync(id);
        if (existingCategory == null)
            throw new ArgumentException($"Category with ID {id} not found");

        existingCategory.CategoryName = category.CategoryName;
        existingCategory.ClientId = category.ClientId;
        existingCategory.ModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return existingCategory;
    }

    public async Task DeleteCategoryAsync(int id)
    {
        var category = await _context.Categories.FindAsync(id);
        if (category != null)
        {
            _context.Categories.Remove(category);
            await _context.SaveChangesAsync();
        }
    }

    public async Task<bool> CategoryExistsAsync(string categoryName, int clientId)
    {
        return await _context.Categories.AnyAsync(c => c.CategoryName == categoryName && c.ClientId == clientId);
    }
}