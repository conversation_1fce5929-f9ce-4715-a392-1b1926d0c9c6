# IDE and Editor files
.idea/
.vscode/
*.swp
*.swo
*~

# Node.js dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Frontend build outputs
frontend/dist/
frontend/build/
frontend/.next/
frontend/out/

# Backend build outputs
backend/target/
backend/build/

# Logs
logs/
*.log

# Environment variables
.env
.env.local
.env.development
.env.development.local
.env.test.local
.env.production.local

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Media uploads (if you want to ignore them)
backend/uploads/
backend/media-uploads/

# Database files (if using local SQLite)
*.db
*.sqlite
*.sqlite3

# Coverage reports
coverage/
*.lcov

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/
