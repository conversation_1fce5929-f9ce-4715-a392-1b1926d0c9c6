using CMS.WebApi.Models.Responses;
using CMS.WebApi.Services.Interfaces;

namespace CMS.WebApi.Services.Implementations;

public class ErrorHandlingService : IErrorHandlingService
{
    private readonly ILogger<ErrorHandlingService> _logger;
    private readonly IWebHostEnvironment _environment;

    public ErrorHandlingService(ILogger<ErrorHandlingService> logger, IWebHostEnvironment environment)
    {
        _logger = logger;
        _environment = environment;
    }

    public ErrorResponse CreateErrorResponse(int statusCode, string error, string message, string path, object? details = null)
    {
        return new ErrorResponse
        {
            Status = statusCode,
            Error = error,
            Message = message,
            Path = path,
            Timestamp = DateTime.UtcNow,
            Details = details
        };
    }

    public ErrorResponse CreateValidationErrorResponse(string message, string path, Dictionary<string, string[]> validationErrors)
    {
        return new ErrorResponse
        {
            Status = 400,
            Error = "Validation Failed",
            Message = message,
            Path = path,
            Timestamp = DateTime.UtcNow,
            ValidationErrors = validationErrors
        };
    }

    public void LogError(Exception exception, string message, params object[] args)
    {
        _logger.LogError(exception, message, args);
    }

    public void LogWarning(string message, params object[] args)
    {
        _logger.LogWarning(message, args);
    }
}
