namespace CMS.WebApi.Models.DTOs;

public class MediaFolderDto
{
    public int Id { get; set; }
    public string FolderName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int? ParentId { get; set; }
    public string? ParentName { get; set; }
    public long? UserId { get; set; }
    public string? CreatedByUsername { get; set; }
    public int MediaCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? ModifiedAt { get; set; }
}
