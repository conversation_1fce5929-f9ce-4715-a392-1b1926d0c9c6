# CMS API Postman Collection

## Overview
This comprehensive Postman collection covers all the main entities in the CMS API system:

- **ContentEntry** - Content management
- **FieldConfig** - Field configuration settings
- **FieldType** - Field type definitions
- **Media** - Media file management
- **MediaFolder** - Media folder organization
- **User** - User management
- **ComponentComponent** - Component relationships
- **ComponentField** - Component field definitions
- **ComponentFieldConfig** - Component field configurations
- **ConfigType** - Configuration type management

## Setup Instructions

### 1. Import the Collection
1. Open Postman
2. Click "Import" button
3. Select the `CMS_API_Collection.postman_collection.json` file
4. Click "Import"

### 2. Configure Environment Variables
The collection uses these variables:
- `base_url`: Set to `http://localhost:5000/api` (or your API base URL)
- `jwt_token`: Automatically set after successful login

### 3. Authentication Setup
1. **First, run the Login request** in the "Authentication" folder
2. Use these default credentials:
   ```json
   {
     "username": "admin",
     "password": "Admin123!"
   }
   ```
3. The JWT token will be automatically stored in the `jwt_token` variable
4. All subsequent requests will use this token for authentication

## Collection Structure

### 🔐 Authentication
- **Login** - Authenticate and get JWT token
- **Register** - Create new user account
- **Health Check** - Test API connectivity

### 📄 Content Entries
- Get all content entries
- Get content entry by ID
- Get content entries by collection (with pagination)
- Create new content entry
- Update content entry
- Delete content entry

### 🏷️ Field Types
- Get all field types
- Get active field types only
- Get field type by ID
- Create new field type
- Update field type
- Delete field type (soft delete)

### ⚙️ Field Configs
- Get all field configurations
- Get field config by ID
- Get field configs by field type ID
- Create new field config
- Update field config
- Delete field config

### 🖼️ Media
- Get all media assets (with pagination)
- Get media asset by ID
- Get media assets by folder
- Upload media file (multipart form)
- Update media asset
- Delete media asset
- Get media file content

### 📁 Media Folders
- Get all media folders
- Get media folder by ID
- Get root media folders
- Get folders by parent
- Create new media folder
- Update media folder
- Delete media folder

### 👥 Users
- Get all users
- Get current user profile
- Get user by ID
- Create new user
- Update user
- Delete user
- Change password

### 🔗 Component Components
- Get all component relationships
- Get component relationship by ID
- Get all components with relationships
- Create component relationship
- Update component relationship
- Delete component relationship
- Reorder component relationships

### 🏗️ Component Fields
- Get all component fields
- Get component field by ID
- Get fields by component ID
- Get next available ID
- Create component field
- Create component field with configs
- Update component field
- Delete component field
- Reorder component fields
- Debug: Get available field configs

### 🔧 Component Field Configs
- Get all component field configs
- Get config by ID
- Get configs by component field ID
- Create component field config
- Update component field config
- Delete component field config

### 📋 Config Types
- Get all config types
- Get active config types only
- Get config type by ID
- Create new config type
- Update config type
- Delete config type

### 🧪 Test Endpoints
- Public test endpoint (no auth required)
- Private test endpoint (auth required)

## Usage Tips

### 1. Start with Authentication
Always run the Login request first to get your JWT token.

### 2. Check Response Status
- **200 OK** - Success
- **201 Created** - Resource created successfully
- **204 No Content** - Success but no data returned
- **400 Bad Request** - Invalid request data
- **401 Unauthorized** - Authentication required
- **404 Not Found** - Resource not found
- **500 Internal Server Error** - Server error

### 3. Pagination Parameters
Many GET endpoints support pagination:
- `page` - Page number (0-based)
- `size` - Number of items per page
- `sort` - Sort field
- `direction` - Sort direction (ASC/DESC)

### 4. File Upload
For media upload, use the "form-data" body type and select a file for the "file" field.

### 5. JSON Request Bodies
Most POST/PUT requests expect JSON bodies. Examples are provided in each request.

## Common Request Examples

### Create Content Entry
```json
{
  "collectionId": 1,
  "dataJson": "{\"title\":\"Sample Title\",\"content\":\"Sample content text\",\"author\":\"John Doe\"}"
}
```

### Create Field Type
```json
{
  "fieldTypeName": "Custom Text Field",
  "fieldTypeDesc": "A custom text input field",
  "displayName": "Custom Text",
  "helpText": "Enter custom text here",
  "logoImagePath": "/images/text-field.png",
  "isActive": true
}
```

### Upload Media File
Use form-data with:
- `file`: [Select file]
- `folderId`: 1 (optional)
- `description`: "Sample media file"
- `altText`: "Sample alt text"
- `isPublic`: false

## Troubleshooting

### 401 Unauthorized
- Make sure you've run the Login request first
- Check that the JWT token is set in the collection variables

### 404 Not Found
- Verify the resource ID exists
- Check the endpoint URL is correct

### 400 Bad Request
- Validate your JSON request body
- Check required fields are provided
- Ensure data types match expectations

## Environment Configuration

You can create different environments for different deployment stages:

**Development Environment:**
- `base_url`: `http://localhost:5000/api`

**Staging Environment:**
- `base_url`: `https://staging-api.yourapp.com/api`

**Production Environment:**
- `base_url`: `https://api.yourapp.com/api`
