using System.ComponentModel.DataAnnotations;

namespace CMS.WebApi.Models.Entities;

public class CollectionComponent : BaseEntity
{
    public int Id { get; set; }

    [Required]
    public int CollectionId { get; set; }
    public CollectionListing Collection { get; set; } = null!;

    [Required]
    public int ComponentId { get; set; }
    public ComponentListing Component { get; set; } = null!;

    public int? DisplayPreference { get; set; }

    public bool IsRepeatable { get; set; } = false;

    public int? MinRepeatOccurrences { get; set; }

    public int? MaxRepeatOccurrences { get; set; }

    public bool IsActive { get; set; } = true;

    [StringLength(255)]
    public string? Name { get; set; }

    [StringLength(255)]
    public string? DisplayName { get; set; }

    public string? AdditionalInfo { get; set; }

    [StringLength(255)]
    public string? AdditionalInfoImage { get; set; }
}
