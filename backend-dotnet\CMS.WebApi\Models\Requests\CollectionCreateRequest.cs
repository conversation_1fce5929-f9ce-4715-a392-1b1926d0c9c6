using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace CMS.WebApi.Models.Requests;

public class CollectionCreateRequest
{
    [Required(ErrorMessage = "Collection name is required")]
    [StringLength(255)]
    public string CollectionName { get; set; } = string.Empty;

    public string? CollectionDesc { get; set; }

    public string? AdditionalInformation { get; set; }

    public string? DisclaimerText { get; set; }

    [Required]
    [StringLength(255)]
    public string CollectionApiId { get; set; } = string.Empty;

    // Support both direct CategoryId and nested Category object
    public int? CategoryId { get; set; }

    public CategoryReference? Category { get; set; }

    // Computed property to get the actual category ID
    [JsonIgnore]
    public int ActualCategoryId => CategoryId ?? Category?.Id ?? 0;
}

public class CategoryReference
{
    public int Id { get; set; }
}
