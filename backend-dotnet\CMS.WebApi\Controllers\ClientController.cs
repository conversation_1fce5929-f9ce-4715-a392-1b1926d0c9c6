using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.Responses;
using CMS.WebApi.Services.Interfaces;

namespace CMS.WebApi.Controllers;

[ApiController]
[Route("api/clients")]
[Authorize]
[Tags("Client Management")]
public class ClientController : ControllerBase
{
    private readonly IClientService _clientService;
    private readonly ILogger<ClientController> _logger;

    public ClientController(IClientService clientService, ILogger<ClientController> logger)
    {
        _clientService = clientService;
        _logger = logger;
    }

    /// <summary>
    /// Get all clients
    /// </summary>
    /// <returns>List of clients</returns>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<Client>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status204NoContent)]
    public async Task<ActionResult<IEnumerable<Client>>> GetAllClients()
    {
        var clients = await _clientService.GetAllClientsAsync();
        if (!clients.Any())
        {
            return NoContent();
        }
        return Ok(clients);
    }

    /// <summary>
    /// Get client by ID
    /// </summary>
    /// <param name="id">Client ID</param>
    /// <returns>Client details</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(Client), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<Client>> GetClientById(int id)
    {
        var client = await _clientService.GetClientByIdAsync(id);
        if (client == null)
        {
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = $"Client not found with id: {id}",
                Path = Request.Path
            });
        }
        return Ok(client);
    }

    /// <summary>
    /// Get client by name
    /// </summary>
    /// <param name="name">Client name</param>
    /// <returns>Client details</returns>
    [HttpGet("by-name/{name}")]
    [ProducesResponseType(typeof(Client), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<Client>> GetClientByName(string name)
    {
        var client = await _clientService.GetClientByNameAsync(name);
        if (client == null)
        {
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = $"Client not found with name: {name}",
                Path = Request.Path
            });
        }
        return Ok(client);
    }

    /// <summary>
    /// Create a new client
    /// </summary>
    /// <param name="client">Client details</param>
    /// <returns>Created client</returns>
    [HttpPost]
    [ProducesResponseType(typeof(Client), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<Client>> CreateClient([FromBody] Client client)
    {
        try
        {
            // Check if client with same name already exists
            if (await _clientService.ClientExistsAsync(client.Name))
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = $"Client with name '{client.Name}' already exists",
                    Path = Request.Path
                });
            }

            var createdClient = await _clientService.CreateClientAsync(client);
            _logger.LogInformation("Client created successfully: {ClientName}", createdClient.Name);

            return CreatedAtAction(nameof(GetClientById), new { id = createdClient.Id }, createdClient);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create client: {ClientName}", client.Name);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while creating the client",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Update an existing client
    /// </summary>
    /// <param name="id">Client ID</param>
    /// <param name="client">Updated client details</param>
    /// <returns>Updated client</returns>
    [HttpPut("{id}")]
    [ProducesResponseType(typeof(Client), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<Client>> UpdateClient(int id, [FromBody] Client client)
    {
        try
        {
            var updatedClient = await _clientService.UpdateClientAsync(id, client);
            _logger.LogInformation("Client updated successfully: {ClientId}", id);
            return Ok(updatedClient);
        }
        catch (ArgumentException ex)
        {
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update client: {ClientId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while updating the client",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Delete a client
    /// </summary>
    /// <param name="id">Client ID</param>
    /// <returns>No content</returns>
    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult> DeleteClient(int id)
    {
        try
        {
            var client = await _clientService.GetClientByIdAsync(id);
            if (client == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Client with ID {id} not found",
                    Path = Request.Path
                });
            }

            await _clientService.DeleteClientAsync(id);
            _logger.LogInformation("Client deleted successfully: {ClientId}", id);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete client: {ClientId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while deleting the client",
                Path = Request.Path
            });
        }
    }
}
