import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { BrowserRouter } from 'react-router-dom';
import ComponentCreate from './ComponentCreate';

// Mock the API
jest.mock('@/lib/api', () => ({
  componentsApi: {
    create: jest.fn(),
  },
}));

// Mock the store
jest.mock('@/lib/store', () => ({
  useComponentStore: () => ({
    addComponent: jest.fn(),
  }),
}));

// Mock the toast hook
jest.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn(),
  }),
}));

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

const ComponentCreateWrapper = () => (
  <BrowserRouter>
    <ComponentCreate />
  </BrowserRouter>
);

describe('ComponentCreate', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all form fields correctly', () => {
    render(<ComponentCreateWrapper />);

    expect(screen.getByLabelText(/^Name$/)).toBeInTheDocument();
    expect(screen.getByLabelText(/^Display Name$/)).toBeInTheDocument();
    expect(screen.getByLabelText(/^API ID$/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Description/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Active/)).toBeInTheDocument();
  });

  it('does not auto-generate display name when name changes', () => {
    render(<ComponentCreateWrapper />);

    const nameInput = screen.getByLabelText(/^Name$/);
    const displayNameInput = screen.getByLabelText(/^Display Name$/);

    // Type in the name field
    fireEvent.change(nameInput, { target: { value: 'Product Details' } });

    // Display name should remain empty (not auto-generated)
    expect(displayNameInput).toHaveValue('');
  });

  it('auto-generates API ID when name changes', () => {
    render(<ComponentCreateWrapper />);

    const nameInput = screen.getByLabelText(/^Name$/);
    const apiIdInput = screen.getByLabelText(/^API ID$/);

    // Type in the name field
    fireEvent.change(nameInput, { target: { value: 'Product Details' } });

    // API ID should be auto-generated
    expect(apiIdInput).toHaveValue('product_details');
  });

  it('allows manual input for display name', () => {
    render(<ComponentCreateWrapper />);

    const displayNameInput = screen.getByLabelText(/^Display Name$/);

    // Type in the display name field
    fireEvent.change(displayNameInput, { target: { value: 'Custom Display Name' } });

    // Display name should have the manually entered value
    expect(displayNameInput).toHaveValue('Custom Display Name');
  });

  it('validates required fields including display name', async () => {
    render(<ComponentCreateWrapper />);

    const submitButton = screen.getByRole('button', { name: /create component/i });

    // Try to submit without filling required fields
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Component name is required')).toBeInTheDocument();
      expect(screen.getByText('Display name is required')).toBeInTheDocument();
      expect(screen.getByText('API ID is required')).toBeInTheDocument();
    });
  });

  it('allows form submission when all required fields are filled', async () => {
    const { componentsApi } = require('@/lib/api');
    componentsApi.create.mockResolvedValue({ data: { id: 1 } });

    render(<ComponentCreateWrapper />);

    const nameInput = screen.getByLabelText(/^Name$/);
    const displayNameInput = screen.getByLabelText(/^Display Name$/);
    const apiIdInput = screen.getByLabelText(/^API ID$/);
    const submitButton = screen.getByRole('button', { name: /create component/i });

    // Fill in all required fields
    fireEvent.change(nameInput, { target: { value: 'Product Details' } });
    fireEvent.change(displayNameInput, { target: { value: 'Product Details Component' } });
    fireEvent.change(apiIdInput, { target: { value: 'product_details' } });

    // Submit the form
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(componentsApi.create).toHaveBeenCalledWith({
        componentName: 'Product Details',
        componentDisplayName: 'Product Details Component',
        componentApiId: 'product_details',
        componentDesc: '',
        isActive: true,
      });
    });
  });

  it('shows correct field descriptions', () => {
    render(<ComponentCreateWrapper />);

    expect(screen.getByText('The internal name of your component')).toBeInTheDocument();
    expect(screen.getByText('The display name shown to users')).toBeInTheDocument();
    expect(screen.getByText('Used in the API. Only lowercase letters, numbers, and underscores')).toBeInTheDocument();
  });
});
