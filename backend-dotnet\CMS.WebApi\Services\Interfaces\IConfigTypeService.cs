using CMS.WebApi.Models.Entities;

namespace CMS.WebApi.Services.Interfaces;

public interface IConfigTypeService
{
    Task<IEnumerable<ConfigType>> GetAllConfigTypesAsync();
    Task<IEnumerable<ConfigType>> GetActiveConfigTypesAsync();
    Task<ConfigType?> GetConfigTypeByIdAsync(int id);
    Task<ConfigType?> GetConfigTypeByNameAsync(string configTypeName);
    Task<ConfigType> CreateConfigTypeAsync(ConfigType configType);
    Task<ConfigType> UpdateConfigTypeAsync(int id, ConfigType configType);
    Task DeleteConfigTypeAsync(int id);
    Task<bool> ConfigTypeExistsAsync(string configTypeName);
}
