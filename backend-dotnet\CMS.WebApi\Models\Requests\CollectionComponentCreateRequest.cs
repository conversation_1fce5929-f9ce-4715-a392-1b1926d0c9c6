using System.ComponentModel.DataAnnotations;

namespace CMS.WebApi.Models.Requests;

public class CollectionComponentCreateRequest
{
    /// <summary>
    /// Collection ID
    /// </summary>
    [Required]
    public int CollectionId { get; set; }

    /// <summary>
    /// Component ID
    /// </summary>
    [Required]
    public int ComponentId { get; set; }

    /// <summary>
    /// Display preference for ordering
    /// </summary>
    public int? DisplayPreference { get; set; }

    /// <summary>
    /// Whether the component is repeatable
    /// </summary>
    public bool IsRepeatable { get; set; } = false;

    /// <summary>
    /// Minimum repeat occurrences
    /// </summary>
    public int? MinRepeatOccurrences { get; set; }

    /// <summary>
    /// Maximum repeat occurrences
    /// </summary>
    public int? MaxRepeatOccurrences { get; set; }

    /// <summary>
    /// Whether the component is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Component name
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Display name for the component
    /// </summary>
    public string? DisplayName { get; set; }

    /// <summary>
    /// Additional information
    /// </summary>
    public string? AdditionalInfo { get; set; }

    /// <summary>
    /// Additional info image URL
    /// </summary>
    public string? AdditionalInfoImage { get; set; }

    /// <summary>
    /// Collection reference (alternative to CollectionId)
    /// </summary>
    public CollectionReference? Collection { get; set; }

    /// <summary>
    /// Component reference (alternative to ComponentId)
    /// </summary>
    public ComponentReference? Component { get; set; }
}
