{"info": {"_postman_id": "cms-api-collection-2024", "name": "CMS API Collection - Complete", "description": "Comprehensive Postman collection for CMS API covering all entities: ContentEntry, FieldConfig, FieldType, Media, MediaFolder, User, ComponentComponent, ComponentField, ComponentFieldConfig, ConfigType, ApiToken, Category, Client, CollectionComponent, CollectionField, CollectionFieldConfig, and CollectionListing", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:5000/api", "type": "string"}, {"key": "jwt_token", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('jwt_token', response.accessToken);", "    pm.test('Login successful', function () {", "        pm.response.to.have.status(200);", "    });", "} else {", "    pm.test('<PERSON><PERSON> failed', function () {", "        pm.response.to.not.have.status(200);", "    });", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"admin\",\n    \"password\": \"Admin123!\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}}, {"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"newuser\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"NewUser123!\"\n}"}, "url": {"raw": "{{base_url}}/auth/register", "host": ["{{base_url}}"], "path": ["auth", "register"]}}}, {"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/auth/health", "host": ["{{base_url}}"], "path": ["auth", "health"]}}}]}, {"name": "Content Entries", "item": [{"name": "Get All Content Entries", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/content-entries/getAll", "host": ["{{base_url}}"], "path": ["content-entries", "getAll"]}}}, {"name": "Get Content Entry by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/content-entries/getById/1", "host": ["{{base_url}}"], "path": ["content-entries", "getById", "1"]}}}, {"name": "Get Content Entries by Collection", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/content-entries/collection/1?page=0&size=20&search=", "host": ["{{base_url}}"], "path": ["content-entries", "collection", "1"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "20"}, {"key": "search", "value": ""}]}}}, {"name": "Create Content Entry", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"collectionId\": 1,\n    \"dataJson\": \"{\\\"title\\\":\\\"Complete Blog Post Example\\\",\\\"content\\\":\\\"This is a comprehensive blog post with rich content including multiple paragraphs, formatting, and metadata.\\\",\\\"author\\\":\\\"<PERSON>\\\",\\\"publishDate\\\":\\\"2024-07-22T10:00:00Z\\\",\\\"category\\\":\\\"Technology\\\",\\\"tags\\\":[\\\"CMS\\\",\\\"API\\\",\\\"Development\\\"],\\\"featured\\\":true,\\\"excerpt\\\":\\\"A brief summary of the blog post content\\\",\\\"seoTitle\\\":\\\"Complete Blog Post Example - CMS Tutorial\\\",\\\"seoDescription\\\":\\\"Learn how to create comprehensive blog posts using our CMS API with this detailed example\\\",\\\"slug\\\":\\\"complete-blog-post-example\\\",\\\"status\\\":\\\"published\\\",\\\"viewCount\\\":0,\\\"likes\\\":0,\\\"comments\\\":[],\\\"metadata\\\":{\\\"readTime\\\":5,\\\"difficulty\\\":\\\"beginner\\\",\\\"language\\\":\\\"en\\\"}}\"\n}"}, "url": {"raw": "{{base_url}}/content-entries/create", "host": ["{{base_url}}"], "path": ["content-entries", "create"]}}}, {"name": "Update Content Entry", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"id\": 1,\n    \"collectionId\": 1,\n    \"dataJson\": \"{\\\"title\\\":\\\"Updated Complete Blog Post\\\",\\\"content\\\":\\\"This is the updated version of our comprehensive blog post with enhanced content and additional features.\\\",\\\"author\\\":\\\"<PERSON>\\\",\\\"publishDate\\\":\\\"2024-07-22T14:30:00Z\\\",\\\"category\\\":\\\"Technology\\\",\\\"tags\\\":[\\\"CMS\\\",\\\"API\\\",\\\"Development\\\",\\\"Updated\\\"],\\\"featured\\\":true,\\\"excerpt\\\":\\\"An updated brief summary of the enhanced blog post content\\\",\\\"seoTitle\\\":\\\"Updated Complete Blog Post - Advanced CMS Tutorial\\\",\\\"seoDescription\\\":\\\"Discover the enhanced features of our CMS API with this updated comprehensive example\\\",\\\"slug\\\":\\\"updated-complete-blog-post\\\",\\\"status\\\":\\\"published\\\",\\\"viewCount\\\":150,\\\"likes\\\":25,\\\"comments\\\":[{\\\"author\\\":\\\"Reader1\\\",\\\"text\\\":\\\"Great post!\\\",\\\"date\\\":\\\"2024-07-22T12:00:00Z\\\"}],\\\"metadata\\\":{\\\"readTime\\\":7,\\\"difficulty\\\":\\\"intermediate\\\",\\\"language\\\":\\\"en\\\",\\\"lastUpdated\\\":\\\"2024-07-22T14:30:00Z\\\"}}\"\n}"}, "url": {"raw": "{{base_url}}/content-entries/update/1", "host": ["{{base_url}}"], "path": ["content-entries", "update", "1"]}}}, {"name": "Delete Content Entry", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/content-entries/deleteById/1", "host": ["{{base_url}}"], "path": ["content-entries", "deleteById", "1"]}}}]}, {"name": "Field Types", "item": [{"name": "Get All Field Types", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/field-types", "host": ["{{base_url}}"], "path": ["field-types"]}}}, {"name": "Get Active Field Types", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/field-types/active", "host": ["{{base_url}}"], "path": ["field-types", "active"]}}}, {"name": "Get Field Type by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/field-types/1", "host": ["{{base_url}}"], "path": ["field-types", "1"]}}}, {"name": "Create Field Type", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"fieldTypeName\": \"Rich Text Editor\",\n    \"fieldTypeDesc\": \"A comprehensive rich text editor field with formatting capabilities, media embedding, and advanced text styling options\",\n    \"displayName\": \"Rich Text Editor\",\n    \"helpText\": \"Use this field to create rich content with formatting, links, images, and other media. Supports HTML, Markdown, and WYSIWYG editing modes.\",\n    \"logoImagePath\": \"/images/field-types/rich-text-editor.svg\",\n    \"isActive\": true,\n    \"createdBy\": \"admin\",\n    \"modifiedBy\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/field-types", "host": ["{{base_url}}"], "path": ["field-types"]}}}, {"name": "Update Field Type", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"fieldTypeName\": \"Advanced Rich Text Editor\",\n    \"fieldTypeDesc\": \"An enhanced rich text editor field with advanced formatting capabilities, custom plugins, media embedding, collaborative editing, and extensive customization options\",\n    \"displayName\": \"Advanced Rich Text Editor\",\n    \"helpText\": \"Use this advanced field to create sophisticated content with enhanced formatting, custom components, media galleries, tables, code blocks, and collaborative features. Supports multiple editing modes and custom plugins.\",\n    \"logoImagePath\": \"/images/field-types/advanced-rich-text-editor.svg\",\n    \"isActive\": true,\n    \"modifiedBy\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/field-types/1", "host": ["{{base_url}}"], "path": ["field-types", "1"]}}}, {"name": "Delete Field Type", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/field-types/1", "host": ["{{base_url}}"], "path": ["field-types", "1"]}}}]}, {"name": "Field Configs", "item": [{"name": "Get All Field Configs", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/field-configs", "host": ["{{base_url}}"], "path": ["field-configs"]}}}, {"name": "Get Field Config by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/field-configs/1", "host": ["{{base_url}}"], "path": ["field-configs", "1"]}}}, {"name": "Get Field Configs by Field Type ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/field-configs/getByFieldTypeId/1", "host": ["{{base_url}}"], "path": ["field-configs", "getByFieldTypeId", "1"]}}}, {"name": "Get Field Configs by Field Type ID (Alternative)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/field-configs/by-field-type/1", "host": ["{{base_url}}"], "path": ["field-configs", "by-field-type", "1"]}}}, {"name": "Create Field Config", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"configName\": \"maxCharacterLimit\",\n    \"valueType\": \"number\",\n    \"isActive\": true,\n    \"fieldTypeId\": 1,\n    \"configTypeId\": 1,\n    \"createdBy\": \"admin\",\n    \"modifiedBy\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/field-configs", "host": ["{{base_url}}"], "path": ["field-configs"]}}}, {"name": "Update Field Config", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"configName\": \"maxCharacterLimit\",\n    \"valueType\": \"number\",\n    \"isActive\": true,\n    \"fieldTypeId\": 1,\n    \"configTypeId\": 1,\n    \"modifiedBy\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/field-configs/1", "host": ["{{base_url}}"], "path": ["field-configs", "1"]}}}, {"name": "Delete Field Config", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/field-configs/1", "host": ["{{base_url}}"], "path": ["field-configs", "1"]}}}]}, {"name": "Media", "item": [{"name": "Get All Media Assets", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/media/assets?page=0&size=20&sort=CreatedAt&direction=DESC", "host": ["{{base_url}}"], "path": ["media", "assets"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "20"}, {"key": "sort", "value": "CreatedAt"}, {"key": "direction", "value": "DESC"}]}}}, {"name": "Get Media Asset by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/media/assets/1", "host": ["{{base_url}}"], "path": ["media", "assets", "1"]}}}, {"name": "Get Media Assets by Folder", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/media/assets/folder/1", "host": ["{{base_url}}"], "path": ["media", "assets", "folder", "1"]}}}, {"name": "Get Root Media Assets", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/media/assets/folder/", "host": ["{{base_url}}"], "path": ["media", "assets", "folder", ""]}}}, {"name": "Upload Media File", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "Select an image, video, audio, or document file to upload"}, {"key": "folderId", "value": "1", "type": "text", "description": "ID of the folder to upload to (optional)"}, {"key": "description", "value": "Professional product photography showcasing the latest collection with detailed lighting and composition", "type": "text", "description": "Detailed description of the media file"}, {"key": "altText", "value": "Professional product photography showing modern furniture collection in natural lighting", "type": "text", "description": "Alternative text for accessibility"}, {"key": "isPublic", "value": "false", "type": "text", "description": "Whether the file should be publicly accessible"}]}, "url": {"raw": "{{base_url}}/media/upload", "host": ["{{base_url}}"], "path": ["media", "upload"]}}}, {"name": "Update Media Asset", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"fileName\": \"updated-professional-product-photo.jpg\",\n    \"description\": \"Updated professional product photography showcasing the enhanced collection with improved lighting, composition, and post-processing effects\",\n    \"altText\": \"Updated professional product photography displaying modern furniture collection with enhanced natural lighting and artistic composition\",\n    \"isPublic\": true,\n    \"modifiedBy\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/media/assets/1", "host": ["{{base_url}}"], "path": ["media", "assets", "1"]}}}, {"name": "Delete Media Asset", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/media/assets/1", "host": ["{{base_url}}"], "path": ["media", "assets", "1"]}}}, {"name": "Get Media File Content", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/media/files/2024/01/sample-file.jpg", "host": ["{{base_url}}"], "path": ["media", "files", "2024", "01", "sample-file.jpg"]}}}]}, {"name": "Media Folders", "item": [{"name": "Get All Media Folders", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/media-folders", "host": ["{{base_url}}"], "path": ["media-folders"]}}}, {"name": "Get Media Folder by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/media-folders/1", "host": ["{{base_url}}"], "path": ["media-folders", "1"]}}}, {"name": "Get Root Media Folders", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/media-folders/root", "host": ["{{base_url}}"], "path": ["media-folders", "root"]}}}, {"name": "Get Media Folders by Parent", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/media-folders/parent/1", "host": ["{{base_url}}"], "path": ["media-folders", "parent", "1"]}}}, {"name": "Create Media Folder", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"folderName\": \"Product Photography 2024\",\n    \"description\": \"Comprehensive collection of professional product photography for the 2024 catalog, including lifestyle shots, detail images, and promotional materials organized by product category and season\",\n    \"parentId\": null,\n    \"createdBy\": \"admin\",\n    \"modifiedBy\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/media-folders/create", "host": ["{{base_url}}"], "path": ["media-folders", "create"]}}}, {"name": "Update Media Folder", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"folderName\": \"Enhanced Product Photography 2024\",\n    \"description\": \"Updated comprehensive collection of professional product photography for the 2024 catalog, featuring enhanced lifestyle shots, detailed product images, promotional materials, and new seasonal collections organized by product category, brand, and marketing campaign\",\n    \"parentId\": null,\n    \"modifiedBy\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/media-folders/update/1", "host": ["{{base_url}}"], "path": ["media-folders", "update", "1"]}}}, {"name": "Delete Media Folder", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/media-folders/delete/1", "host": ["{{base_url}}"], "path": ["media-folders", "delete", "1"]}}}]}, {"name": "Users", "item": [{"name": "Get All Users", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/users", "host": ["{{base_url}}"], "path": ["users"]}}}, {"name": "Get User Profile", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/users/profile", "host": ["{{base_url}}"], "path": ["users", "profile"]}}}, {"name": "Get User by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/users/1", "host": ["{{base_url}}"], "path": ["users", "1"]}}}, {"name": "Create User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"newuser\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"NewUser123!\",\n    \"isActive\": true\n}"}, "url": {"raw": "{{base_url}}/users/create", "host": ["{{base_url}}"], "path": ["users", "create"]}}}, {"name": "Update User", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"updateduser\",\n    \"email\": \"<EMAIL>\",\n    \"isActive\": true\n}"}, "url": {"raw": "{{base_url}}/users/update/1", "host": ["{{base_url}}"], "path": ["users", "update", "1"]}}}, {"name": "Delete User", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/users/delete/1", "host": ["{{base_url}}"], "path": ["users", "delete", "1"]}}}, {"name": "Change Password", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"currentPassword\": \"OldPassword123!\",\n    \"newPassword\": \"NewPassword123!\"\n}"}, "url": {"raw": "{{base_url}}/users/change-password", "host": ["{{base_url}}"], "path": ["users", "change-password"]}}}]}, {"name": "Component Components", "item": [{"name": "Get All Component Components", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/component-components/getAll", "host": ["{{base_url}}"], "path": ["component-components", "getAll"]}}}, {"name": "Get Component Component by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/component-components/getById/1", "host": ["{{base_url}}"], "path": ["component-components", "getById", "1"]}}}, {"name": "Get All Components with Relationships", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/component-components/getAllWithRelationships", "host": ["{{base_url}}"], "path": ["component-components", "getAllWithRelationships"]}}}, {"name": "Create Component Component", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"parentComponentId\": 1,\n    \"childComponentId\": 2,\n    \"isRepeatable\": false,\n    \"displayPreference\": 1,\n    \"isActive\": true,\n    \"additionalInformation\": \"Complex parent-child component relationship defining hierarchical content structure with nested components for advanced content management and dynamic layout rendering\",\n    \"createdBy\": \"admin\",\n    \"modifiedBy\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/component-components/create", "host": ["{{base_url}}"], "path": ["component-components", "create"]}}}, {"name": "Update Component Component", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"parentComponentId\": 1,\n    \"childComponentId\": 2,\n    \"isRepeatable\": true,\n    \"displayPreference\": 2,\n    \"isActive\": true,\n    \"additionalInformation\": \"Enhanced complex parent-child component relationship with repeatable functionality, defining advanced hierarchical content structure with nested components for sophisticated content management, dynamic layout rendering, and flexible content organization\",\n    \"modifiedBy\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/component-components/update/1", "host": ["{{base_url}}"], "path": ["component-components", "update", "1"]}}}, {"name": "Delete Component Component", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/component-components/delete/1", "host": ["{{base_url}}"], "path": ["component-components", "delete", "1"]}}}, {"name": "Reorder Component Components", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "[1, 3, 2, 4]"}, "url": {"raw": "{{base_url}}/component-components/reorder/1", "host": ["{{base_url}}"], "path": ["component-components", "reorder", "1"]}}}]}, {"name": "Component Fields", "item": [{"name": "Get All Component Fields", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/component-fields/getAll", "host": ["{{base_url}}"], "path": ["component-fields", "getAll"]}}}, {"name": "Get Component Field by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/component-fields/getById/1", "host": ["{{base_url}}"], "path": ["component-fields", "getById", "1"]}}}, {"name": "Get Component Fields by Component ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/component-fields/getByComponentId/1", "host": ["{{base_url}}"], "path": ["component-fields", "getByComponentId", "1"]}}}, {"name": "Get Next Available ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/component-fields/getNextId", "host": ["{{base_url}}"], "path": ["component-fields", "getNextId"]}}}, {"name": "Create Component Field", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"componentId\": 1,\n    \"fieldTypeId\": 1,\n    \"displayPreference\": 1,\n    \"additionalInformation\": \"Advanced component field with sophisticated configuration options, validation rules, conditional logic, and dynamic behavior for enhanced content management and user experience optimization\",\n    \"isActive\": true,\n    \"createdBy\": \"admin\",\n    \"modifiedBy\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/component-fields/create", "host": ["{{base_url}}"], "path": ["component-fields", "create"]}}}, {"name": "Create Component Field with Configs", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"componentId\": 1,\n    \"fieldTypeId\": 1,\n    \"displayPreference\": 1,\n    \"additionalInformation\": \"Advanced component field with comprehensive configuration settings, including validation rules, display options, conditional logic, data formatting, and user interaction behaviors for sophisticated content management\",\n    \"isActive\": true,\n    \"configs\": [\n        {\n            \"fieldConfigId\": 1,\n            \"configValue\": \"true\",\n            \"isActive\": true\n        },\n        {\n            \"fieldConfigId\": 2,\n            \"configValue\": \"500\",\n            \"isActive\": true\n        },\n        {\n            \"fieldConfigId\": 3,\n            \"configValue\": \"required\",\n            \"isActive\": true\n        }\n    ],\n    \"createdBy\": \"admin\",\n    \"modifiedBy\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/component-fields/create-with-configs", "host": ["{{base_url}}"], "path": ["component-fields", "create-with-configs"]}}}, {"name": "Update Component Field", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"componentId\": 1,\n    \"fieldTypeId\": 1,\n    \"displayPreference\": 2,\n    \"additionalInformation\": \"Updated component field\",\n    \"isActive\": true\n}"}, "url": {"raw": "{{base_url}}/component-fields/update/1", "host": ["{{base_url}}"], "path": ["component-fields", "update", "1"]}}}, {"name": "Delete Component Field", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/component-fields/deleteById/1", "host": ["{{base_url}}"], "path": ["component-fields", "deleteById", "1"]}}}, {"name": "Reorder Component Fields", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"fieldIds\": [3, 1, 2, 4]\n}"}, "url": {"raw": "{{base_url}}/component-fields/reorderFields/1", "host": ["{{base_url}}"], "path": ["component-fields", "reorderFields", "1"]}}}, {"name": "Debug - Get Available Field Configs", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/component-fields/debug/field-configs", "host": ["{{base_url}}"], "path": ["component-fields", "debug", "field-configs"]}}}]}, {"name": "Component Field Configs", "item": [{"name": "Get All Component Field Configs", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/component-field-configs/getAll", "host": ["{{base_url}}"], "path": ["component-field-configs", "getAll"]}}}, {"name": "Get Component Field Config by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/component-field-configs/getById/1", "host": ["{{base_url}}"], "path": ["component-field-configs", "getById", "1"]}}}, {"name": "Get Component Field Configs by Component Field ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/component-field-configs/getByFieldId/1", "host": ["{{base_url}}"], "path": ["component-field-configs", "getByFieldId", "1"]}}}, {"name": "Create Component Field Config", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"componentFieldId\": 1,\n    \"fieldConfigId\": 1,\n    \"configValue\": \"true\",\n    \"isActive\": true,\n    \"createdBy\": \"admin\",\n    \"modifiedBy\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/component-field-configs/create", "host": ["{{base_url}}"], "path": ["component-field-configs", "create"]}}}, {"name": "Update Component Field Config", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"componentFieldId\": 1,\n    \"fieldConfigId\": 1,\n    \"configValue\": \"false\",\n    \"isActive\": true,\n    \"modifiedBy\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/component-field-configs/update/1", "host": ["{{base_url}}"], "path": ["component-field-configs", "update", "1"]}}}, {"name": "Delete Component Field Config", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/component-field-configs/deleteById/1", "host": ["{{base_url}}"], "path": ["component-field-configs", "deleteById", "1"]}}}]}, {"name": "Config Types", "item": [{"name": "Get All Config Types", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/config-types", "host": ["{{base_url}}"], "path": ["config-types"]}}}, {"name": "Get Active Config Types", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/config-types/active", "host": ["{{base_url}}"], "path": ["config-types", "active"]}}}, {"name": "Get Config Type by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/config-types/1", "host": ["{{base_url}}"], "path": ["config-types", "1"]}}}, {"name": "Create Config Type", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"configTypeName\": \"Advanced Validation Rules\",\n    \"configTypeDesc\": \"Comprehensive configuration type for advanced validation rules including data type validation, format checking, business logic validation, cross-field validation, conditional validation, and custom validation patterns for enhanced data integrity and user experience\",\n    \"isActive\": true,\n    \"createdBy\": \"admin\",\n    \"modifiedBy\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/config-types", "host": ["{{base_url}}"], "path": ["config-types"]}}}, {"name": "Update Config Type", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"configTypeName\": \"Enhanced Advanced Validation Rules\",\n    \"configTypeDesc\": \"Enhanced comprehensive configuration type for sophisticated validation rules including advanced data type validation, complex format checking, multi-layer business logic validation, dynamic cross-field validation, intelligent conditional validation, machine learning-powered validation patterns, and custom rule engines for superior data integrity and optimized user experience\",\n    \"isActive\": true,\n    \"modifiedBy\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/config-types/1", "host": ["{{base_url}}"], "path": ["config-types", "1"]}}}, {"name": "Delete Config Type", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/config-types/1", "host": ["{{base_url}}"], "path": ["config-types", "1"]}}}]}, {"name": "API Tokens", "item": [{"name": "Get All API Tokens", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api-tokens", "host": ["{{base_url}}"], "path": ["api-tokens"]}}}, {"name": "Get API Token by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api-tokens/1", "host": ["{{base_url}}"], "path": ["api-tokens", "1"]}}}, {"name": "Generate New API Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Enterprise Integration Token\",\n    \"description\": \"High-security API token for enterprise-level system integration, providing authenticated access to CMS resources with comprehensive logging, rate limiting, and advanced security features for production environments\",\n    \"expirationDays\": 90,\n    \"userId\": 1,\n    \"isActive\": true,\n    \"createdBy\": \"admin\",\n    \"modifiedBy\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/api-tokens", "host": ["{{base_url}}"], "path": ["api-tokens"]}}}, {"name": "Update API Token", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Enhanced Enterprise Integration Token\",\n    \"description\": \"Enhanced high-security API token for advanced enterprise-level system integration, providing authenticated access to CMS resources with comprehensive audit logging, intelligent rate limiting, advanced security features, multi-factor authentication support, and enhanced monitoring capabilities for mission-critical production environments\",\n    \"isActive\": true,\n    \"userId\": 1,\n    \"modifiedBy\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/api-tokens/1", "host": ["{{base_url}}"], "path": ["api-tokens", "1"]}}}, {"name": "Revoke API Token", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api-tokens/1", "host": ["{{base_url}}"], "path": ["api-tokens", "1"]}}}]}, {"name": "Categories", "item": [{"name": "Get All Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/categories", "host": ["{{base_url}}"], "path": ["categories"]}}}, {"name": "Get Category by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/categories/1", "host": ["{{base_url}}"], "path": ["categories", "1"]}}}, {"name": "Get Categories by Client ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/categories/client/1", "host": ["{{base_url}}"], "path": ["categories", "client", "1"]}}}, {"name": "Get Categories by Parent ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/categories/parent/1", "host": ["{{base_url}}"], "path": ["categories", "parent", "1"]}}}, {"name": "Create Category", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"categoryName\": \"Technology & Innovation\",\n    \"categoryDesc\": \"Comprehensive category covering cutting-edge technology trends, innovative solutions, digital transformation, emerging technologies, software development, hardware advancements, and tech industry insights for modern businesses and consumers\",\n    \"isActive\": true,\n    \"parentId\": null,\n    \"createdBy\": \"admin\",\n    \"modifiedBy\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/categories", "host": ["{{base_url}}"], "path": ["categories"]}}}, {"name": "Update Category", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"categoryName\": \"Advanced Technology & Digital Innovation\",\n    \"categoryDesc\": \"Enhanced comprehensive category covering advanced technology trends, breakthrough innovations, digital transformation strategies, emerging technologies, AI and machine learning, software development best practices, next-generation hardware, cybersecurity, and comprehensive tech industry insights for modern enterprises and tech-savvy consumers\",\n    \"isActive\": true,\n    \"parentId\": null,\n    \"modifiedBy\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/categories/1", "host": ["{{base_url}}"], "path": ["categories", "1"]}}}, {"name": "Delete Category", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/categories/1", "host": ["{{base_url}}"], "path": ["categories", "1"]}}}]}, {"name": "Clients", "item": [{"name": "Get All Clients", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/clients", "host": ["{{base_url}}"], "path": ["clients"]}}}, {"name": "Get Client by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/clients/1", "host": ["{{base_url}}"], "path": ["clients", "1"]}}}, {"name": "Get Client by Name", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/clients/by-name/ClientName", "host": ["{{base_url}}"], "path": ["clients", "by-name", "ClientName"]}}}, {"name": "Create Client", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"TechCorp Solutions Inc.\",\n    \"description\": \"Leading enterprise technology solutions provider specializing in digital transformation, cloud infrastructure, cybersecurity, and innovative software development services for Fortune 500 companies and emerging businesses worldwide\",\n    \"isActive\": true,\n    \"createdBy\": \"admin\",\n    \"modifiedBy\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/clients", "host": ["{{base_url}}"], "path": ["clients"]}}}, {"name": "Update Client", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"TechCorp Advanced Solutions Inc.\",\n    \"description\": \"Premier enterprise technology solutions provider specializing in advanced digital transformation, multi-cloud infrastructure, next-generation cybersecurity, AI-powered software development, and comprehensive managed services for Fortune 500 companies, government agencies, and rapidly scaling businesses across global markets\",\n    \"isActive\": true,\n    \"modifiedBy\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/clients/1", "host": ["{{base_url}}"], "path": ["clients", "1"]}}}, {"name": "Delete Client", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/clients/1", "host": ["{{base_url}}"], "path": ["clients", "1"]}}}]}, {"name": "Collections", "item": [{"name": "Get All Collections", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/collections", "host": ["{{base_url}}"], "path": ["collections"]}}}, {"name": "Get All Collections with Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/collections/with-details", "host": ["{{base_url}}"], "path": ["collections", "with-details"]}}}, {"name": "Get Collection by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/collections/1", "host": ["{{base_url}}"], "path": ["collections", "1"]}}}, {"name": "Get Collection by API ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/collections/api-id/sample_collection", "host": ["{{base_url}}"], "path": ["collections", "api-id", "sample_collection"]}}}, {"name": "Get Collections by Category ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/collections/category/1", "host": ["{{base_url}}"], "path": ["collections", "category", "1"]}}}, {"name": "Get Collection Fields", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/collections/1/fields", "host": ["{{base_url}}"], "path": ["collections", "1", "fields"]}}}, {"name": "Create Collection", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"collectionName\": \"Enterprise Blog Posts\",\n    \"collectionDisplayName\": \"Enterprise Blog & Articles Collection\",\n    \"collectionApiId\": \"enterprise_blog_posts\",\n    \"collectionDesc\": \"Comprehensive collection for managing enterprise-level blog posts, thought leadership articles, industry insights, technical documentation, and corporate communications with advanced content management features, SEO optimization, and multi-author collaboration capabilities\",\n    \"isActive\": true,\n    \"clientId\": 1,\n    \"categoryId\": 1,\n    \"createdBy\": \"admin\",\n    \"modifiedBy\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/collections", "host": ["{{base_url}}"], "path": ["collections"]}}}, {"name": "Add Field to Collection", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"fieldTypeId\": 1,\n    \"displayPreference\": 1,\n    \"additionalInformation\": \"New field for collection\"\n}"}, "url": {"raw": "{{base_url}}/collections/1/fields", "host": ["{{base_url}}"], "path": ["collections", "1", "fields"]}}}, {"name": "Update Collection", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"collectionName\": \"Advanced Enterprise Blog Posts\",\n    \"collectionDisplayName\": \"Advanced Enterprise Blog & Articles Collection\",\n    \"collectionApiId\": \"advanced_enterprise_blog_posts\",\n    \"collectionDesc\": \"Enhanced comprehensive collection for managing advanced enterprise-level blog posts, thought leadership articles, industry insights, technical documentation, corporate communications, white papers, case studies, and research publications with advanced content management features, AI-powered SEO optimization, multi-author collaboration, content versioning, and automated publishing workflows\",\n    \"isActive\": true,\n    \"clientId\": 1,\n    \"categoryId\": 1,\n    \"modifiedBy\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/collections/1", "host": ["{{base_url}}"], "path": ["collections", "1"]}}}, {"name": "Delete Collection", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/collections/1", "host": ["{{base_url}}"], "path": ["collections", "1"]}}}, {"name": "Remove Field from Collection", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/collections/1/fields/1", "host": ["{{base_url}}"], "path": ["collections", "1", "fields", "1"]}}}]}, {"name": "Collection Components", "item": [{"name": "Get All Collection Components", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/collection-components/getAll", "host": ["{{base_url}}"], "path": ["collection-components", "getAll"]}}}, {"name": "Get Collection Component by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/collection-components/getById/1", "host": ["{{base_url}}"], "path": ["collection-components", "getById", "1"]}}}, {"name": "Get Collection Components by Collection ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/collection-components/getByCollectionId/1", "host": ["{{base_url}}"], "path": ["collection-components", "getByCollectionId", "1"]}}}, {"name": "Get Next Available ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/collection-components/getNextId", "host": ["{{base_url}}"], "path": ["collection-components", "getNextId"]}}}, {"name": "Create Collection Component", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"collectionId\": 1,\n    \"componentId\": 1,\n    \"isRepeatable\": true,\n    \"minRepeatOccurrences\": 1,\n    \"maxRepeatOccurrences\": 10,\n    \"displayPreference\": 1,\n    \"name\": \"Advanced Content Block\",\n    \"displayName\": \"Advanced Content Block Component\",\n    \"additionalInfo\": \"Sophisticated content block component with advanced layout options, dynamic content rendering, conditional display logic, and comprehensive customization capabilities for enhanced user experience\",\n    \"additionalInfoImage\": \"/images/components/advanced-content-block.svg\",\n    \"isActive\": true,\n    \"createdBy\": \"admin\",\n    \"modifiedBy\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/collection-components/create", "host": ["{{base_url}}"], "path": ["collection-components", "create"]}}}, {"name": "Update Collection Component", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"collectionId\": 1,\n    \"componentId\": 1,\n    \"isRepeatable\": true,\n    \"minRepeatOccurrences\": 1,\n    \"maxRepeatOccurrences\": 5,\n    \"displayPreference\": 2,\n    \"displayName\": \"Updated Component Display Name\",\n    \"description\": \"Updated component description\",\n    \"sortOrder\": 2,\n    \"isRequired\": true,\n    \"isVisible\": true\n}"}, "url": {"raw": "{{base_url}}/collection-components/update/1", "host": ["{{base_url}}"], "path": ["collection-components", "update", "1"]}}}, {"name": "Update Display Preference", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"displayPreference\": 3\n}"}, "url": {"raw": "{{base_url}}/collection-components/updateDisplayPreference/1", "host": ["{{base_url}}"], "path": ["collection-components", "updateDisplayPreference", "1"]}}}, {"name": "Delete Collection Component", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/collection-components/deleteById/1", "host": ["{{base_url}}"], "path": ["collection-components", "deleteById", "1"]}}}]}, {"name": "Collection Fields", "item": [{"name": "Get All Collection Fields", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/collection-fields/getAll", "host": ["{{base_url}}"], "path": ["collection-fields", "getAll"]}}}, {"name": "Get Collection Field by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/collection-fields/getById/1", "host": ["{{base_url}}"], "path": ["collection-fields", "getById", "1"]}}}, {"name": "Get Collection Fields by Collection ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/collection-fields/getByCollectionId/1", "host": ["{{base_url}}"], "path": ["collection-fields", "getByCollectionId", "1"]}}}, {"name": "Get Next Available ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/collection-fields/getNextId", "host": ["{{base_url}}"], "path": ["collection-fields", "getNextId"]}}}, {"name": "Create Collection Field", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"collectionId\": 1,\n    \"fieldTypeId\": 1,\n    \"displayPreference\": 1,\n    \"dependentOnId\": null,\n    \"additionalInformation\": \"Advanced collection field with sophisticated configuration options, validation rules, conditional display logic, data transformation capabilities, and enhanced user interaction features for comprehensive content management and optimal user experience\",\n    \"createdBy\": \"admin\",\n    \"modifiedBy\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/collection-fields/create", "host": ["{{base_url}}"], "path": ["collection-fields", "create"]}}}, {"name": "Update Collection Field", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"collectionId\": 1,\n    \"fieldTypeId\": 1,\n    \"displayPreference\": 2,\n    \"dependentOnId\": null,\n    \"additionalInformation\": \"Updated collection field description\"\n}"}, "url": {"raw": "{{base_url}}/collection-fields/update/1", "host": ["{{base_url}}"], "path": ["collection-fields", "update", "1"]}}}, {"name": "Delete Collection Field", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/collection-fields/deleteById/1", "host": ["{{base_url}}"], "path": ["collection-fields", "deleteById", "1"]}}}]}, {"name": "Collection Field Configs", "item": [{"name": "Get All Collection Field Configs", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/collection-field-configs/getAll", "host": ["{{base_url}}"], "path": ["collection-field-configs", "getAll"]}}}, {"name": "Get Collection Field Config by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/collection-field-configs/getById/1", "host": ["{{base_url}}"], "path": ["collection-field-configs", "getById", "1"]}}}, {"name": "Get Collection Field Configs by Collection Field ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/collection-field-configs/getByFieldId/1", "host": ["{{base_url}}"], "path": ["collection-field-configs", "getByFieldId", "1"]}}}, {"name": "Create Collection Field Config", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"collectionFieldId\": 1,\n    \"fieldConfigId\": 1,\n    \"configValue\": \"true\",\n    \"isActive\": true,\n    \"createdBy\": \"admin\",\n    \"modifiedBy\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/collection-field-configs/create", "host": ["{{base_url}}"], "path": ["collection-field-configs", "create"]}}}, {"name": "Update Collection Field Config", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"collectionFieldId\": 1,\n    \"fieldConfigId\": 1,\n    \"configValue\": \"false\",\n    \"isActive\": true\n}"}, "url": {"raw": "{{base_url}}/collection-field-configs/update/1", "host": ["{{base_url}}"], "path": ["collection-field-configs", "update", "1"]}}}, {"name": "Delete Collection Field Config", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/collection-field-configs/deleteById/1", "host": ["{{base_url}}"], "path": ["collection-field-configs", "deleteById", "1"]}}}]}, {"name": "Users", "item": [{"name": "Get User Profile", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/users/profile", "host": ["{{base_url}}"], "path": ["users", "profile"]}}}, {"name": "Update User Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"john.doe.enterprise\",\n    \"email\": \"<EMAIL>\",\n    \"firstName\": \"<PERSON>\",\n    \"lastName\": \"<PERSON><PERSON>\",\n    \"phoneNumber\": \"******-0123\",\n    \"department\": \"Technology & Innovation\",\n    \"jobTitle\": \"Senior Software Architect\",\n    \"bio\": \"Experienced software architect specializing in enterprise-level content management systems, cloud infrastructure, and digital transformation initiatives\",\n    \"profileImageUrl\": \"/images/profiles/john-doe.jpg\",\n    \"timezone\": \"America/New_York\",\n    \"language\": \"en-US\",\n    \"modifiedBy\": \"john.doe.enterprise\"\n}"}, "url": {"raw": "{{base_url}}/users/profile", "host": ["{{base_url}}"], "path": ["users", "profile"]}}}, {"name": "Change Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"currentPassword\": \"CurrentSecurePassword123!\",\n    \"newPassword\": \"NewSecurePassword456!\",\n    \"confirmPassword\": \"NewSecurePassword456!\"\n}"}, "url": {"raw": "{{base_url}}/users/change-password", "host": ["{{base_url}}"], "path": ["users", "change-password"]}}}]}, {"name": "Test Endpoints", "item": [{"name": "Public Test", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/test/getPublic", "host": ["{{base_url}}"], "path": ["test", "getPublic"]}}}, {"name": "Private Test (Auth Required)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/test/getPrivate", "host": ["{{base_url}}"], "path": ["test", "getPrivate"]}}}]}]}