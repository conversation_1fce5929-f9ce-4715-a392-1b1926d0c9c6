using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.Responses;
using CMS.WebApi.Services.Interfaces;
using CMS.WebApi.Security;

namespace CMS.WebApi.Controllers;

[ApiController]
[Route("api/components")]
[Authorize(Policy = AuthorizationPolicies.JwtOrApiToken)]
[Tags("Component Management")]
public class ComponentController : ControllerBase
{
    private readonly IComponentService _componentService;
    private readonly ILogger<ComponentController> _logger;

    public ComponentController(IComponentService componentService, ILogger<ComponentController> logger)
    {
        _componentService = componentService;
        _logger = logger;
    }

    /// <summary>
    /// Get all components
    /// </summary>
    /// <returns>List of components</returns>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<ComponentListing>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<ComponentListing>>> GetAllComponents()
    {
        var components = await _componentService.GetAllComponentsAsync();
        return Ok(components);
    }

    /// <summary>
    /// Get active components
    /// </summary>
    /// <returns>List of active components</returns>
    [HttpGet("active")]
    [ProducesResponseType(typeof(IEnumerable<ComponentListing>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    public async Task<ActionResult<IEnumerable<ComponentListing>>> GetActiveComponents()
    {
        try
        {
            var activeComponents = await _componentService.GetActiveComponentsAsync();

            if (!activeComponents.Any())
            {
                _logger.LogInformation("No active components found");
                return NoContent();
            }

            _logger.LogInformation("Retrieved {Count} active components", activeComponents.Count());
            return Ok(activeComponents);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get active components");
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving active components",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get component by ID
    /// </summary>
    /// <param name="id">Component ID</param>
    /// <returns>Component details</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(ComponentListing), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ComponentListing>> GetComponentById(int id)
    {
        var component = await _componentService.GetComponentByIdAsync(id);
        if (component == null)
        {
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = $"Component with ID {id} not found",
                Path = Request.Path
            });
        }
        return Ok(component);
    }

    /// <summary>
    /// Get component by name
    /// </summary>
    /// <param name="name">Component name</param>
    /// <returns>Component details</returns>
    [HttpGet("name/{name}")]
    [ProducesResponseType(typeof(ComponentListing), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ComponentListing>> GetComponentByName(string name)
    {
        var component = await _componentService.GetComponentByNameAsync(name);
        if (component == null)
        {
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = $"Component with name '{name}' not found",
                Path = Request.Path
            });
        }
        return Ok(component);
    }

    /// <summary>
    /// Get component by API ID
    /// </summary>
    /// <param name="apiId">Component API ID</param>
    /// <returns>Component details</returns>
    [HttpGet("api-id/{apiId}")]
    [ProducesResponseType(typeof(ComponentListing), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ComponentListing>> GetComponentByApiId(string apiId)
    {
        var component = await _componentService.GetComponentByApiIdAsync(apiId);
        if (component == null)
        {
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = $"Component with API ID '{apiId}' not found",
                Path = Request.Path
            });
        }
        return Ok(component);
    }

    /// <summary>
    /// Create a new component
    /// </summary>
    /// <param name="component">Component details</param>
    /// <returns>Created component</returns>
    [HttpPost]
    [ProducesResponseType(typeof(ComponentListing), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ComponentListing>> CreateComponent([FromBody] ComponentListing component)
    {
        try
        {
            // Check if component with same name already exists
            if (await _componentService.ComponentExistsAsync(component.ComponentName))
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = $"Component with name '{component.ComponentName}' already exists",
                    Path = Request.Path
                });
            }

            var createdComponent = await _componentService.CreateComponentAsync(component);
            _logger.LogInformation("Component created successfully: {ComponentName}", createdComponent.ComponentName);

            return CreatedAtAction(nameof(GetComponentById), new { id = createdComponent.Id }, createdComponent);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create component: {ComponentName}", component.ComponentName);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while creating the component",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Update an existing component
    /// </summary>
    /// <param name="id">Component ID</param>
    /// <param name="component">Updated component details</param>
    /// <returns>Updated component</returns>
    [HttpPut("{id}")]
    [ProducesResponseType(typeof(ComponentListing), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ComponentListing>> UpdateComponent(int id, [FromBody] ComponentListing component)
    {
        try
        {
            var updatedComponent = await _componentService.UpdateComponentAsync(id, component);
            _logger.LogInformation("Component updated successfully: {ComponentId}", id);
            return Ok(updatedComponent);
        }
        catch (ArgumentException ex)
        {
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update component: {ComponentId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while updating the component",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Delete a component
    /// </summary>
    /// <param name="id">Component ID</param>
    /// <returns>No content</returns>
    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult> DeleteComponent(int id)
    {
        try
        {
            var component = await _componentService.GetComponentByIdAsync(id);
            if (component == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Component with ID {id} not found",
                    Path = Request.Path
                });
            }

            await _componentService.DeleteComponentAsync(id);
            _logger.LogInformation("Component deleted successfully: {ComponentId}", id);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete component: {ComponentId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while deleting the component",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get component fields
    /// </summary>
    /// <param name="id">Component ID</param>
    /// <returns>List of component fields</returns>
    [HttpGet("{id}/fields")]
    [ProducesResponseType(typeof(IEnumerable<ComponentField>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<ComponentField>>> GetComponentFields(int id)
    {
        var fields = await _componentService.GetComponentFieldsAsync(id);
        return Ok(fields);
    }

    /// <summary>
    /// Get component fields by component ID (alternative endpoint matching frontend call)
    /// </summary>
    /// <param name="componentId">Component ID</param>
    /// <returns>List of component fields for the specified component</returns>
    [HttpGet("fields/getByComponentId/{componentId}")]
    [ProducesResponseType(typeof(IEnumerable<ComponentField>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    public async Task<ActionResult<IEnumerable<ComponentField>>> GetFieldsByComponentId(int componentId)
    {
        try
        {
            var fields = await _componentService.GetComponentFieldsAsync(componentId);

            if (!fields.Any())
            {
                _logger.LogInformation("No fields found for component ID: {ComponentId}", componentId);
                return NoContent();
            }

            _logger.LogInformation("Retrieved {Count} fields for component ID: {ComponentId}", fields.Count(), componentId);
            return Ok(fields);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving component fields for component ID: {ComponentId}", componentId);
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving component fields",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Add field to component
    /// </summary>
    /// <param name="id">Component ID</param>
    /// <param name="field">Field details</param>
    /// <returns>Added field</returns>
    [HttpPost("{id}/fields")]
    [ProducesResponseType(typeof(ComponentField), StatusCodes.Status201Created)]
    public async Task<ActionResult<ComponentField>> AddFieldToComponent(int id, [FromBody] ComponentField field)
    {
        try
        {
            var addedField = await _componentService.AddFieldToComponentAsync(id, field);
            return CreatedAtAction(nameof(GetComponentFields), new { id }, addedField);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add field to component: {ComponentId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while adding the field",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Remove field from component
    /// </summary>
    /// <param name="id">Component ID</param>
    /// <param name="fieldId">Field ID</param>
    /// <returns>No content</returns>
    [HttpDelete("{id}/fields/{fieldId}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    public async Task<ActionResult> RemoveFieldFromComponent(int id, int fieldId)
    {
        try
        {
            await _componentService.RemoveFieldFromComponentAsync(id, fieldId);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to remove field from component: {ComponentId}, FieldId: {FieldId}", id, fieldId);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while removing the field",
                Path = Request.Path
            });
        }
    }
}
