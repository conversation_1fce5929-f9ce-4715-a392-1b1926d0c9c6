import React, { useCallback, useEffect, useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ArrowUpDown } from 'lucide-react';
import { Field, FieldTypeEnum } from '@/lib/store';
import { componentFieldsApi, componentFieldConfigsApi } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

interface ComponentFieldDisplayPreferencesEditorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  componentId: string;
  fields: Field[];
  onFieldsReordered: () => void;
}

export function ComponentFieldDisplayPreferencesEditor({
  open,
  onOpenChange,
  componentId,
  fields,
  onFieldsReordered,
}: ComponentFieldDisplayPreferencesEditorProps) {
  const { toast } = useToast();
  const [orderedFields, setOrderedFields] = useState<Field[]>([]);
  const [loading, setLoading] = useState(false);
  const [fieldDisplayNames, setFieldDisplayNames] = useState<Record<string, string>>({});

  // Function to fetch display names for all fields
  const fetchFieldDisplayNames = useCallback(async (fieldsToProcess: Field[]) => {
    const displayNames: Record<string, string> = {};

    try {
      // Fetch display names for each field
      await Promise.all(
        fieldsToProcess.map(async (field) => {
          try {
            const configResponse = await componentFieldConfigsApi.getByComponentFieldId(field.id.toString());
            const configs = configResponse.data || [];

            // Find the display-name config
            const displayNameConfig = configs.find((config: any) => config.configName === 'display-name');
            if (displayNameConfig && displayNameConfig.configValue) {
              displayNames[field.id.toString()] = displayNameConfig.configValue;
            } else {
              // Fallback to field type name if no display name is set
              displayNames[field.id.toString()] = field.fieldType?.fieldTypeName || 'Unknown Field';
            }
          } catch (error) {
            console.error(`Error fetching display name for field ${field.id}:`, error);
            // Fallback to field type name
            displayNames[field.id.toString()] = field.fieldType?.fieldTypeName || 'Unknown Field';
          }
        })
      );

      setFieldDisplayNames(displayNames);
    } catch (error) {
      console.error('Error fetching field display names:', error);
    }
  }, []);

  // Function to fetch the latest data from the backend
  const fetchLatestData = useCallback(async () => {
    if (!componentId) return;

    // Check if we have a timestamp for the last update
    const lastUpdateTimestamp = localStorage.getItem(`component_display_prefs_updated_${componentId}`);

    // Add a cache-busting parameter to force a fresh request
    const cacheBuster = lastUpdateTimestamp || Date.now();

    try {
      // Fetch component fields with their display preferences
      const response = await componentFieldsApi.getByComponentId(componentId, { params: { _cb: cacheBuster } });

      console.log('API response for component fields:', response.data);

      if (response.data && Array.isArray(response.data)) {
        // Process fields from the API response
        const processedFields: Field[] = [];

        response.data.forEach(field => {
          console.log('Processing field:', field);
          // Ensure display preference is a number and in the correct format (multiple of 10)
          let displayPref = field.displayPreference;
          if (typeof displayPref !== 'number' || displayPref % 10 !== 0) {
            // If not a multiple of 10, round to the nearest multiple of 10
            displayPref = Math.round(displayPref / 10) * 10;
            if (displayPref === 0) displayPref = 10; // Ensure minimum value is 10
          }

          processedFields.push({
            id: field.id.toString(),
            name: (() => {
              // Try to extract name from additionalInformation
              if (field.additionalInformation) {
                try {
                  const metadata = JSON.parse(field.additionalInformation);
                  console.log('Parsed metadata:', metadata);
                  if (metadata.name) {
                    console.log('Found name in metadata:', metadata.name);
                    return metadata.name;
                  }
                } catch (e) {
                  console.error('Error parsing additionalInformation:', e);
                }
              }
              // Try to generate a descriptive name based on field type
              let fallbackName;
              if (field.fieldType && field.fieldType.displayName) {
                fallbackName = field.fieldType.displayName;
              } else if (field.fieldType && field.fieldType.fieldTypeName) {
                // Format the field type name to be more readable
                fallbackName = field.fieldType.fieldTypeName
                  .split('_')
                  .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                  .join(' ');
              } else {
                // Last resort - use a generic name with the field type if available
                const typeStr = field.fieldType?.name || 'Field';
                fallbackName = `${typeStr.charAt(0).toUpperCase() + typeStr.slice(1).toLowerCase()} Field`;
              }

              console.log('Using generated name:', fallbackName);
              return fallbackName;
            })(),
            type: field.fieldType?.name as FieldTypeEnum || FieldTypeEnum.TEXT,
            fieldTypeId: field.fieldType?.id,
            displayPreference: displayPref,
            apiId: field.apiId || '',
            description: field.description || '',
            required: field.required || false,
            unique: field.unique || false,
            attributes: field.attributes || {},
            validations: field.validations || {},
          });
        });

        // Sort fields by display preference
        processedFields.sort((a, b) => {
          const prefA = typeof a.displayPreference === 'number' ? a.displayPreference : 999;
          const prefB = typeof b.displayPreference === 'number' ? b.displayPreference : 999;
          return prefA - prefB;
        });

        setOrderedFields(processedFields);

        // Fetch display names for the fields
        await fetchFieldDisplayNames(processedFields);
      }
    } catch (error) {
      console.error('Error fetching component fields:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch component fields. Please try again.',
        variant: 'destructive',
      });
    }
  }, [componentId, toast]);

  // Load data when the dialog opens
  useEffect(() => {
    if (open) {
      fetchLatestData();
    }
  }, [open, fetchLatestData]);

  // Initialize with the current fields when the component mounts or fields change
  useEffect(() => {
    if (fields.length > 0 && orderedFields.length === 0) {
      // Sort fields by display preference
      const sortedFields = [...fields].sort((a, b) => {
        const prefA = typeof a.displayPreference === 'number' ? a.displayPreference : 999;
        const prefB = typeof b.displayPreference === 'number' ? b.displayPreference : 999;
        return prefA - prefB;
      });

      setOrderedFields(sortedFields);

      // Fetch display names for the fields
      fetchFieldDisplayNames(sortedFields);
    }
  }, [fields, orderedFields.length]);

  // Note: Up/down movement functions removed as we're using direct display preference input

  // Handle manual input of display preference
  const handleDisplayPreferenceChange = (index: number, value: string) => {
    const numValue = parseInt(value, 10);
    if (isNaN(numValue)) return;

    const newOrderedFields = [...orderedFields];
    newOrderedFields[index] = {
      ...newOrderedFields[index],
      displayPreference: numValue,
    };

    setOrderedFields(newOrderedFields);
  };

  // Save the updated display preferences
  const saveDisplayPreferences = async () => {
    if (!componentId) {
      toast({
        title: 'Error',
        description: 'Component ID is missing. Cannot save display preferences.',
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);

    try {
      // Sort by display preference before extracting IDs
      const sortedFields = [...orderedFields].sort((a, b) => {
        // Use 999 as default for undefined display preferences to push them to the end
        const prefA = typeof a.displayPreference === 'number' ? a.displayPreference : 999;
        const prefB = typeof b.displayPreference === 'number' ? b.displayPreference : 999;
        return prefA - prefB;
      });

      // Always normalize display preferences to the 10, 20, 30 format
      // This ensures consistent display order values
      const normalizedFields = sortedFields.map((field, index) => {
        // Create a new object with updated display preference
        // Ensure display preference is a multiple of 10
        const displayPref = (index + 1) * 10; // This guarantees the 10, 20, 30 sequence

        return {
          ...field,
          displayPreference: displayPref
        };
      });

      // Prepare update promises for each field
      const updatePromises = normalizedFields.map(field => {
        if (!field.id) {
          console.warn('Field is missing ID:', field);
          return null;
        }

        // Extract the numeric ID
        let fieldId: string;
        if (typeof field.id === 'string' && field.id.startsWith('component_')) {
          fieldId = field.id.replace('component_', '');
        } else {
          fieldId = field.id.toString();
        }

        // Create the update payload
        const updatePayload = {
          id: parseInt(fieldId),
          displayPreference: field.displayPreference,
          // Include required fields for the API
          component: { id: parseInt(componentId) },
          fieldType: { id: field.fieldTypeId || 1 }, // Default to text if not specified
          // Preserve any other properties if needed
          additionalInformation: field.additionalInformation || null
        };

        console.log(`Updating field ${fieldId} (${field.name}) with display preference: ${field.displayPreference}`, updatePayload);

        // Return the update promise
        return componentFieldsApi.update(fieldId, updatePayload)
          .then(response => {
            console.log(`Successfully updated field ${fieldId}:`, response.data);
            return response;
          })
          .catch(error => {
            console.error(`Error updating field ${fieldId}:`, error);
            throw error;
          });
      }).filter(promise => promise !== null);

      // Wait for all updates to complete
      const results = await Promise.allSettled(updatePromises as Promise<any>[]);

      // Check for any failures
      const failures = results.filter(result => result.status === 'rejected');
      if (failures.length > 0) {
        console.error(`${failures.length} field updates failed:`, failures);
        throw new Error(`Failed to update ${failures.length} fields`);
      }

      console.log('All field display preferences updated successfully');

      // Fetch the updated fields to get the latest data
      const response = await componentFieldsApi.getByComponentId(componentId);
      console.log('Updated fields from API:', response.data);

      // Add a timestamp to localStorage to force refresh when dialog reopens
      localStorage.setItem(`component_display_prefs_updated_${componentId}`, Date.now().toString());

      // Notify parent component that fields have been reordered
      onFieldsReordered();

      // Show success toast with details
      toast({
        title: 'Success',
        description: `Display preferences updated successfully for ${updatePromises.length} fields`,
        variant: 'default',
      });

      // Update the local state with the response data
      if (response.data && Array.isArray(response.data)) {
        const updatedFields = response.data.map((field: any) => ({
          id: field.id.toString(),
          name: (() => {
            // Try to extract name from additionalInformation
            if (field.additionalInformation) {
              try {
                const metadata = JSON.parse(field.additionalInformation);
                if (metadata.name) {
                  return metadata.name;
                }
              } catch (e) {
                console.error('Error parsing additionalInformation:', e);
              }
            }

            // Try to generate a descriptive name based on field type
            let fallbackName;
            if (field.fieldType && field.fieldType.displayName) {
              fallbackName = field.fieldType.displayName;
            } else if (field.fieldType && field.fieldType.fieldTypeName) {
              // Format the field type name to be more readable
              fallbackName = field.fieldType.fieldTypeName
                .split('_')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                .join(' ');
            } else {
              // Last resort - use a generic name with the field type if available
              const typeStr = field.fieldType?.name || 'Field';
              fallbackName = `${typeStr.charAt(0).toUpperCase() + typeStr.slice(1).toLowerCase()} Field`;
            }

            return fallbackName;
          })(),
          displayPreference: field.displayPreference,
          type: field.fieldType?.fieldTypeName as FieldTypeEnum || FieldTypeEnum.TEXT,
          fieldTypeId: field.fieldType?.id,
          additionalInformation: field.additionalInformation,
        }));

        console.log('Updated fields from response:', updatedFields);
        setOrderedFields(updatedFields);
      } else {
        console.warn('No fields returned from API after update');
      }

      // Close the dialog
      onOpenChange(false);
    } catch (error) {
      console.error('Error saving display preferences:', error);
      toast({
        title: 'Error',
        description: 'Failed to save display preferences. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Edit Display Preferences</DialogTitle>
          <DialogDescription>
            Change the order of fields by adjusting their display preferences. Lower numbers appear first.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <div className="grid grid-cols-12 gap-4 mb-2 text-sm font-medium text-muted-foreground">
            <div className="col-span-6">Field Name</div>
            <div className="col-span-3">Type</div>
            <div className="col-span-3 text-center">Display Order</div>
          </div>

          <div className="space-y-2 max-h-[400px] overflow-y-auto pr-2">
            {orderedFields
              .sort((a, b) => (a.displayPreference || 0) - (b.displayPreference || 0))
              .map((field, index) => (
              <div key={field.id} className="grid grid-cols-12 gap-4 items-center p-2 rounded-md bg-muted/20 border">
                <div className="col-span-6 font-medium truncate" title={fieldDisplayNames[field.id] || field.name}>
                  {fieldDisplayNames[field.id] || field.name}
                </div>
                <div className="col-span-3">
                  {(() => {
                    // Map fieldTypeId to correct type name for display
                    if (field.fieldTypeId) {
                      switch (field.fieldTypeId) {
                        case 1: return 'Text';
                        case 2: return 'Number';
                        case 3: return 'Date';
                        case 4: return 'Image';
                        case 5: return 'Rich Text';
                        case 6: return 'Masked';
                        case 8: return 'Editor';
                        case 9: return 'Password';
                        case 10: return 'Autocomplete';
                        case 11: return 'Cascade Select';
                        case 12: return 'Dropdown';
                        case 13: return 'File';
                        case 14: return 'Multi-State Checkbox';
                        case 15: return 'Multi-Select';
                        case 16: return 'Multi-Select';
                        case 17: return 'Mention';
                        case 18: return 'Text Area Extended';
                        case 19: return 'OTP';
                        case 20: return 'Multi Checkbox';
                        case 21: return 'Radio Button';
                        case 22: return 'Input Switch';
                        case 23: return 'Dummy';
                        case 24: return 'API Details';
                        case 25: return 'Enumeration';
                        default: return field.type || 'Unknown';
                      }
                    }
                    return field.type || 'Unknown';
                  })()}
                </div>
                <div className="col-span-3">
                  <Input
                    type="number"
                    value={field.displayPreference || (index + 1) * 10}
                    onChange={(e) => handleDisplayPreferenceChange(index, e.target.value)}
                    className="h-8"
                    min="10"
                    step="10"
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={saveDisplayPreferences} disabled={loading}>
            {loading ? 'Saving...' : 'Save Changes'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
