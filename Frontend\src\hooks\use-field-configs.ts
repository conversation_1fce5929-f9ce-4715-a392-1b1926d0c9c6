import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { fieldConfigsApi } from '@/lib/api';
import { useAuthStore } from '@/lib/store';
import { useToast } from '@/hooks/use-toast';

// Define the field config structure
export interface FieldConfig {
  id?: number;
  configName: string;
  valueType: string;
  fieldType?: {
    id: number;
    fieldTypeName: string;
  };
  configType?: {
    id: number;
    configTypeName: string;
  };
  isActive: boolean;
}

// Define the simplified field config structure returned by the API
export interface SimpleFieldConfig {
  id: number;
  configName: string;
  valueType: string;
}

export const useFieldConfigs = (fieldTypeId?: string) => {
  const [fieldConfigs, setFieldConfigs] = useState<FieldConfig[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();
  const navigate = useNavigate();
  const { isAuthenticated, token, logout } = useAuthStore();

  // Check authentication before making API calls
  const checkAuthentication = () => {
    if (!isAuthenticated || !token) {
      console.log('Not authenticated, redirecting to login');
      logout(); // Clear any invalid tokens
      navigate('/login');
      return false;
    }
    return true;
  };

  // Fetch field configs from the backend
  const fetchFieldConfigs = async () => {
    // Check authentication first
    if (!checkAuthentication()) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      let response;

      if (fieldTypeId) {
        console.log(`Fetching field configs for field type ID: ${fieldTypeId}`);
        response = await fieldConfigsApi.getByFieldType(fieldTypeId);
      } else {
        console.log('Fetching all active field configs');
        response = await fieldConfigsApi.getActive();
      }

      console.log('Field configs API response:', response.data);

      if (!response.data || !Array.isArray(response.data)) {
        console.error('Invalid response data format:', response.data);
        setError('Invalid response data format');
        return;
      }

      // If we're fetching by field type ID, we'll get the simplified format
      if (fieldTypeId) {
        // Convert simplified format to full format
        const simplifiedConfigs = response.data as SimpleFieldConfig[];
        const convertedConfigs: FieldConfig[] = simplifiedConfigs.map(config => ({
          id: config.id, // Include the ID field
          configName: config.configName,
          valueType: config.valueType,
          isActive: true // Since we're only getting active configs
        }));
        setFieldConfigs(convertedConfigs);
        console.log(`Loaded ${convertedConfigs.length} simplified field configs with IDs`);
      } else {
        // For other endpoints, we get the full format
        setFieldConfigs(response.data);
        console.log(`Loaded ${response.data.length} field configs`);
      }
    } catch (error: any) {
      console.error('Error fetching field configs:', error);

      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      }

      // Handle authentication errors
      if (error.response && error.response.status === 401) {
        const errorMsg = 'Authentication required. Please log in again.';
        setError(errorMsg);
        toast({
          title: 'Authentication Error',
          description: 'Your session has expired. Please log in again.',
          variant: 'destructive',
        });

        // Redirect to login
        logout();
        navigate('/login');
      } else {
        const errorMsg = `Failed to load field configs: ${error.message}`;
        setError(errorMsg);
        toast({
          title: 'Error',
          description: 'Failed to load field configurations',
          variant: 'destructive',
        });
      }
    } finally {
      setLoading(false);
    }
  };

  // Fetch field configs when the component mounts or fieldTypeId changes
  useEffect(() => {
    if (isAuthenticated && token) {
      fetchFieldConfigs();
    }
  }, [isAuthenticated, token, fieldTypeId]);

  return {
    fieldConfigs,
    loading,
    error,
    refetch: fetchFieldConfigs
  };
};
