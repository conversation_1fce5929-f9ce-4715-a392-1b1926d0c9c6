import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, Loader2, LogIn, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { FieldTypeEnum, useAuthStore, useCollectionStore } from '@/lib/store';
import { useFieldTypes, FieldTypeOption } from '@/hooks/use-field-types';

interface SimpleFieldTypeSelectorProps {
  open: boolean;
  onClose: () => void;
  onSelect: (type: FieldTypeEnum, fieldTypeId?: number) => void;
}

export default function SimpleFieldTypeSelector({ open, onClose, onSelect }: SimpleFieldTypeSelectorProps) {
  // Create a portal for the dialog
  const [mounted, setMounted] = useState(false);

  // Mount the component when it's opened
  useEffect(() => {
    if (open && !mounted) {
      setMounted(true);
      console.log('SimpleFieldTypeSelector mounted');
    } else if (!open && mounted) {
      // Delay unmounting to allow for animations
      setTimeout(() => {
        setMounted(false);
        console.log('SimpleFieldTypeSelector unmounted');
      }, 300);
    }
  }, [open, mounted]);
  console.log('SimpleFieldTypeSelector rendering, open:', open);
  const [searchQuery, setSearchQuery] = useState('');
  const { fieldTypeOptions, loading, error, refetch } = useFieldTypes();
  const { isAuthenticated } = useAuthStore();
  const { selectedCollection } = useCollectionStore();
  const navigate = useNavigate();

  // Debug component state
  console.log('SimpleFieldTypeSelector state:', {
    open,
    fieldTypeOptionsLength: fieldTypeOptions.length,
    loading,
    error,
    isAuthenticated,
    searchQuery
  });

  // Refresh field types when component mounts
  useEffect(() => {
    console.log('SimpleFieldTypeSelector mounted, fetching field types...');
    refetch();
  }, [refetch]);

  // Force refresh when dialog opens
  useEffect(() => {
    if (open) {
      console.log('Dialog opened, refreshing field types');
      setTimeout(() => {
        if (fieldTypeOptions.length === 0 && !loading) {
          console.log('No field types loaded, forcing refresh');
          refetch();
        }
      }, 500);
    }
  }, [open, fieldTypeOptions.length, loading, refetch]);

  // Field type descriptions based on API response
  const getFieldDescription = (type: FieldTypeEnum): string => {
    switch (type) {
      case FieldTypeEnum.TEXT:
        return 'Simple text field';
      case FieldTypeEnum.NUMBER:
        return 'Numeric field';
      case FieldTypeEnum.DATE:
        return 'Date field';
      case FieldTypeEnum.IMAGE:
        return 'Image upload field';
      case FieldTypeEnum.RICH_TEXT:
        return 'Rich text editor';
      case FieldTypeEnum.MASK:
        return 'Masked input field';
      case FieldTypeEnum.CALENDAR:
        return 'Calendar date picker';
      case FieldTypeEnum.EDITOR:
        return 'Rich text editor';
      case FieldTypeEnum.PASSWORD:
        return 'Password input field';
      case FieldTypeEnum.AUTOCOMPLETE:
        return 'Autocomplete suggestions';
      case FieldTypeEnum.CASCADE_SELECT:
        return 'Cascade selection field';
      case FieldTypeEnum.DROPDOWN:
        return 'Dropdown selection';
      case FieldTypeEnum.FILE:
        return 'File upload field';
      case FieldTypeEnum.MULTI_STATE_CHECKBOX:
        return 'Multi-state checkbox';
      case FieldTypeEnum.MULTI_SELECT:
        return 'Multi-select field';
      case FieldTypeEnum.MENTION:
        return 'Mention users or tags';
      case FieldTypeEnum.TEXTAREA:
        return 'Multi-line text input';
      case FieldTypeEnum.OTP:
        return 'One-time password input';
      default:
        return 'Field type';
    }
  };

  // Filter field types based on search
  const filteredFieldTypes = fieldTypeOptions.length > 0
    ? fieldTypeOptions.filter((option) =>
        option.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        getFieldDescription(option.type).toLowerCase().includes(searchQuery.toLowerCase())
      )
    : [];

  // If dialog is not open and not mounted, don't render anything
  if (!open && !mounted) {
    return null;
  }

  console.log('SimpleFieldTypeSelector rendering dialog content, mounted:', mounted);

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-[720px] max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-2">
            {selectedCollection && (
              <div className="flex items-center justify-center w-8 h-8 bg-indigo-600 text-white rounded font-bold">
                {selectedCollection.name.substring(0, 2).toUpperCase()}
              </div>
            )}
            <h2 className="text-lg font-semibold">Select a field for your collection type</h2>
          </div>
          <Button variant="ghost" size="icon" onClick={onClose} className="rounded-full">
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className="p-6">
          <div className="mb-4">
            <div className="flex space-x-2 border-b mb-4">
              <button className="px-4 py-2 border-b-2 border-primary font-medium">DEFAULT</button>
              <button className="px-4 py-2 text-gray-500">CUSTOM</button>
            </div>
          </div>

          <div className="relative mb-4">
            <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search field types..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          {loading ? (
            <div className="flex items-center justify-center h-32">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading field types...</span>
            </div>
          ) : error ? (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription className="flex flex-col gap-4">
                <div>{error}</div>
                {error.includes('Authentication') ? (
                  <Button
                    variant="outline"
                    onClick={() => navigate('/login')}
                    className="flex items-center gap-2"
                  >
                    <LogIn className="h-4 w-4" />
                    Go to Login
                  </Button>
                ) : (
                  <button
                    className="ml-2 underline"
                    onClick={refetch}
                  >
                    Try again
                  </button>
                )}
              </AlertDescription>
            </Alert>
          ) : filteredFieldTypes.length === 0 ? (
            <div className="flex items-center justify-center h-32 border rounded-md">
              <p className="text-gray-500">No field types found matching your search</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredFieldTypes.map((option) => (
                <div
                  key={`${option.type}-${option.id}`}
                  className="flex p-4 border rounded-md cursor-pointer hover:border-primary hover:bg-primary/5 transition-colors"
                  onClick={() => {
                    console.log('Selected field type:', option.type, 'ID:', option.id);
                    onSelect(option.type, option.id);
                    onClose();
                  }}
                >
                  <div className="mr-4 flex-shrink-0">
                    <div className={`w-8 h-8 ${option.iconBg} ${option.iconColor} flex items-center justify-center rounded`}>
                      {option.iconText}
                    </div>
                  </div>
                  <div>
                    <h3 className="font-medium">{option.title}</h3>
                    <p className="text-sm text-gray-500">{option.description || getFieldDescription(option.type)}</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
