import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Edit, Trash2, ArrowLeft, Plus, ChevronLeft, ChevronRight, Search, AlertTriangle, Folder, MoreVertical } from 'lucide-react';
import { categoriesApi, collectionsApi } from '@/lib/api';
import { useToast } from '@/components/ui/use-toast';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import BasicCollectionDialog from '@/components/content-type/BasicCollectionDialog';

const CategoryCollections = () => {
  const { categoryId } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [loading, setLoading] = useState(true);
  const [category, setCategory] = useState<any>(null);
  const [collections, setCollections] = useState<any[]>([]);
  const [filteredCollections, setFilteredCollections] = useState<any[]>([]);
  const [createCollectionDialogOpen, setCreateCollectionDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [collectionToDelete, setCollectionToDelete] = useState<any>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch category details
        if (categoryId) {
          const categoryResponse = await categoriesApi.getById(categoryId);
          setCategory(categoryResponse.data);
          console.log('Category details:', categoryResponse.data);

          // Fetch all collections
          const collectionsResponse = await collectionsApi.getByCategoryId(categoryId);
          console.log('All collections:', collectionsResponse.data);

          // Filter collections by category
          const filteredCollections = collectionsResponse.data.filter((collection: any) => {
            if (!collection || !collection.category) return false;

            let collectionCategoryId = null;
            if (typeof collection.category === 'object' && collection.category !== null) {
              collectionCategoryId = collection.category.id;
            } else if (typeof collection.category === 'number') {
              collectionCategoryId = collection.category;
            } else if (typeof collection.category === 'string') {
              collectionCategoryId = parseInt(collection.category);
            }

            return collectionCategoryId === parseInt(categoryId);
          });

          console.log('Filtered collections:', filteredCollections);
          setCollections(collectionsResponse.data);
          setFilteredCollections(collectionsResponse.data);
          setTotalPages(Math.ceil(filteredCollections.length / itemsPerPage));
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        // Removed error toast notification
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [categoryId, toast, itemsPerPage]);

  const handleEditCollection = (collectionId: string) => {
    navigate(`/content-types/edit/${collectionId}`);
  };

  // Handle delete collection
  const handleDeleteClick = (e: React.MouseEvent, collection: any) => {
    e.stopPropagation(); // Prevent navigation to edit page
    setCollectionToDelete(collection);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!collectionToDelete) return;

    setIsDeleting(true);
    try {
      await collectionsApi.delete(collectionToDelete.id);

      // Remove the deleted collection from the state
      const updatedCollections = collections.filter(c => c.id !== collectionToDelete.id);
      setCollections(updatedCollections);
      setFilteredCollections(updatedCollections);
      setTotalPages(Math.ceil(updatedCollections.length / itemsPerPage));

      // Show success message
      toast({
        title: 'Collection deleted',
        description: `${collectionToDelete.collectionName} has been deleted successfully`,
      });
    } catch (error) {
      console.error('Error deleting collection:', error);
      // Removed error toast notification
    } finally {
      setIsDeleting(false);
      setDeleteDialogOpen(false);
      setCollectionToDelete(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setCollectionToDelete(null);
  };

  // Pagination handlers
  const handlePageChange = (newPage: number) => {
    if (newPage < 1 || newPage > totalPages) return;
    setCurrentPage(newPage);
  };

  const handleItemsPerPageChange = (value: string) => {
    const newItemsPerPage = parseInt(value);
    setItemsPerPage(newItemsPerPage);
    setTotalPages(Math.ceil(collections.length / newItemsPerPage));
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value.toLowerCase();
    setSearchQuery(query);

    if (!query.trim()) {
      setFilteredCollections(collections);
    } else {
      const filtered = collections.filter(collection => {
        const name = collection.collectionName?.toLowerCase() || '';
        const apiId = collection.collectionApiId?.toLowerCase() || '';
        return name.includes(query) || apiId.includes(query);
      });
      setFilteredCollections(filtered);
    }

    // Reset to first page when searching
    setCurrentPage(1);
    // Update total pages based on filtered results or all collections
    const count = query.trim() ? filtered?.length : collections.length;
    setTotalPages(Math.ceil(count / itemsPerPage));
  };

  // Get current page items
  const getCurrentPageItems = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredCollections.slice(startIndex, endIndex);
  };

  const handleCreateCollection = () => {
    setCreateCollectionDialogOpen(true);
  };

  const handleCloseCollectionDialog = () => {
    setCreateCollectionDialogOpen(false);
  };

  const handleSaveCollection = async (data: any) => {
    try {
      console.log('Handling save collection with data:', data);

      // Create the collection data
      const collectionData = {
        id: null, // Let the backend assign the ID
        collectionName: data.name,
        collectionDesc: `${data.name} collection`,
        collectionApiId: data.apiIdSingular,
        // Store additional properties in the description field as JSON
        additionalInformation: JSON.stringify({
          apiIdPlural: data.apiIdPlural,
          draftAndPublish: data.draftAndPublish,
          isInternationally: data.isInternationally
        }),
        fields: [],
      };

      // Add the current category ID
      if (categoryId) {
        console.log(`Adding category ID ${categoryId} to collection`);
        collectionData.category = { id: parseInt(categoryId) };
      }

      console.log('Creating collection with data:', collectionData);
      try {
        const response = await collectionsApi.create(collectionData);
        console.log('Collection created successfully:', response.data);

        // Close the dialog
        setCreateCollectionDialogOpen(false);

        // Refresh the collections list
        const collectionsResponse = await collectionsApi.getAll();
        const filteredCollections = collectionsResponse.data.filter((collection: any) => {
          if (!collection || !collection.category) return false;

          let collectionCategoryId = null;
          if (typeof collection.category === 'object' && collection.category !== null) {
            collectionCategoryId = collection.category.id;
          } else if (typeof collection.category === 'number') {
            collectionCategoryId = collection.category;
          } else if (typeof collection.category === 'string') {
            collectionCategoryId = parseInt(collection.category);
          }

          return collectionCategoryId === parseInt(categoryId);
        });

        // Update the collections and filtered collections
        setCollections(filteredCollections);
        setFilteredCollections(filteredCollections);
        setTotalPages(Math.ceil(filteredCollections.length / itemsPerPage));

        // Show success toast with notification that the page has been refreshed
        toast({
          title: 'Collection created',
          description: 'Your collection has been created successfully and the page has been refreshed',
        });
      } catch (apiError: any) {
        console.error('API error creating collection:', apiError);
        // Removed error toast notification
      }
    } catch (error: any) {
      console.error('Error creating collection:', error);
      // Removed error toast notification
    }
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate('/content-types')}
              className="mr-2"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>

            <h1 className="text-3xl font-bold">
              {loading ? 'Loading...' : `Collections in ${category?.categoryName || 'Category'}`}
            </h1>
          </div>
          <div className="flex items-center gap-2">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search collections..."
                className="pl-8 w-64"
                value={searchQuery}
                onChange={handleSearchChange}
              />
            </div>
            <Button onClick={handleCreateCollection}>
              <Plus className="mr-2 h-4 w-4" />
              Create Collection
            </Button>
          </div>
        </div>



      </div>

      {loading ? (
        <div className="flex justify-center p-8">
          <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full"></div>
        </div>
      ) : collections.length === 0 ? (
        <div className="border rounded-md p-8 text-center">
          <h3 className="text-lg font-medium mb-2">No collections found</h3>
          <p className="text-sm text-muted-foreground mb-4">
            There are no collections in this category yet.
          </p>
          <Button variant="outline" onClick={() => navigate('/content-types')}>
            View All Collections
          </Button>
        </div>
      ) : filteredCollections.length === 0 && searchQuery ? (
        <div className="border rounded-md p-8 text-center">
          <h3 className="text-lg font-medium mb-2">No matching collections</h3>
          <p className="text-sm text-muted-foreground mb-4">
            No collections match your search query "{searchQuery}" in this category.
          </p>
          <Button variant="outline" onClick={() => setSearchQuery('')}>
            Clear search
          </Button>
        </div>
      ) : (
        <>
        <div className="grid grid-cols-1 gap-4">
          {getCurrentPageItems().map((collection, index) => (
            <div
              key={collection.id}
              className="group relative overflow-hidden bg-card dark:bg-card border-2 rounded-md shadow-sm hover:shadow-lg hover:shadow-blue-500/25 dark:hover:shadow-purple-500/25 transition-all duration-300 hover:-translate-y-1 hover:scale-105 cursor-pointer hover:border-blue-300/50 dark:hover:border-purple-400/50 bg-gradient-to-br from-white to-gray-50/50 dark:from-card dark:to-card/80"
              onClick={() => handleEditCollection(collection.id)}
            >
              {/* Animated background gradient */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

              {/* Shimmer effect */}
              <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>

              <div className="p-4 relative z-10">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-10 h-10 rounded-full bg-primary/10 text-primary flex items-center justify-center mr-4 group-hover:scale-110 group-hover:rotate-12 transition-all duration-300 shadow-lg group-hover:shadow-xl group-hover:bg-blue-100 group-hover:text-blue-600">
                      {(collection.collectionName || 'U')[0].toUpperCase()}
                    </div>
                    <div>
                      <h3 className="font-medium group-hover:text-blue-700 transition-colors duration-300">{collection.collectionName || 'Unnamed Collection'}</h3>
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-xs text-muted-foreground">API ID:</span>
                        <span className="text-xs font-mono font-semibold text-foreground bg-muted/50 dark:bg-muted/40 px-2 py-1 rounded border border-border dark:border-purple-500/30 group-hover:text-blue-600 dark:group-hover:text-purple-400 transition-colors duration-300">
                          {collection.collectionApiId}
                        </span>
                      </div>

                      {/* Animated dots */}
                      <div className="flex space-x-1 mt-1 opacity-0 group-hover:opacity-100 transition-opacity duration-500 delay-200">
                        <div className="w-1 h-1 bg-blue-400 rounded-full animate-bounce"></div>
                        <div className="w-1 h-1 bg-purple-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                        <div className="w-1 h-1 bg-pink-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs group-hover:border-blue-300 group-hover:text-blue-700 transition-colors duration-300">
                      {collection.fields?.length || 0} fields
                    </Badge>
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 hover:bg-blue-100 hover:text-blue-600 transition-all duration-300 hover:scale-110"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditCollection(collection.id);
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10 transition-all duration-300 hover:scale-110"
                        onClick={(e) => handleDeleteClick(e, collection)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Corner accent */}
              <div className="absolute top-0 right-0 w-0 h-0 border-l-[20px] border-l-transparent border-t-[20px] border-t-blue-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
          ))}
        </div>

        {/* Pagination controls */}
        <div className="flex items-center justify-between mt-4">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-muted-foreground">Items per page:</span>
            <Select
              value={itemsPerPage.toString()}
              onValueChange={handleItemsPerPageChange}
            >
              <SelectTrigger className="w-[80px]">
                <SelectValue placeholder="10" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="15">15</SelectItem>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="30">30</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-sm text-muted-foreground">
              Page {currentPage} of {totalPages}
            </span>
            <div className="flex items-center space-x-1">
              <Button
                variant="outline"
                size="icon"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
        </>
      )}

      {/* Create collection dialog */}
      <BasicCollectionDialog
        isOpen={createCollectionDialogOpen}
        onClose={handleCloseCollectionDialog}
        onSave={handleSaveCollection}
        initialCategoryId={categoryId}
      />

      {/* Delete confirmation dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              Confirm Deletion
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the collection "{collectionToDelete?.collectionName}"?
              This action cannot be undone and will delete all fields and data associated with this collection.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={handleDeleteCancel} disabled={isDeleting}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={isDeleting}
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CategoryCollections;
