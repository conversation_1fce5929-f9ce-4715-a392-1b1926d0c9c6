using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.Requests;
using CMS.WebApi.Models.Responses;

namespace CMS.WebApi.Services.Interfaces;

public interface IUserService
{
    Task<User?> GetUserByIdAsync(long id);
    Task<User?> GetUserByUsernameAsync(string username);
    Task<User?> GetUserByEmailAsync(string email);
    Task<IEnumerable<User>> GetAllUsersAsync();
    Task<User> CreateUserAsync(User user, string password);
    Task<User> UpdateUserAsync(long id, User user);
    Task DeleteUserAsync(long id);
    Task<bool> ValidatePasswordAsync(User user, string password);
    Task<bool> UserExistsAsync(string username);
    Task<bool> EmailExistsAsync(string email);
    Task AddUserToTenantAsync(string username, string tenantSchema);
    Task<IEnumerable<User>> GetUsersInTenantAsync(string tenantSchema);
}
