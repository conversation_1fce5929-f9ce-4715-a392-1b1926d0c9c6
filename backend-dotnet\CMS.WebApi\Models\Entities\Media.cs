using System.ComponentModel.DataAnnotations;

namespace CMS.WebApi.Models.Entities;

public class Media : BaseEntity
{
    public int Id { get; set; }

    [Required(ErrorMessage = "File name is required")]
    [StringLength(255)]
    public string FileName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Original file name is required")]
    [StringLength(255)]
    public string OriginalFileName { get; set; } = string.Empty;

    [Required(ErrorMessage = "File path is required")]
    public string FilePath { get; set; } = string.Empty;

    [Required(ErrorMessage = "File type is required")]
    [StringLength(100)]
    public string FileType { get; set; } = string.Empty;

    [Required(ErrorMessage = "File size is required")]
    public long FileSize { get; set; }

    public int? Width { get; set; }
    public int? Height { get; set; }
    public int? Duration { get; set; }
    public string? AltText { get; set; }
    public string? Description { get; set; }
    public string? PublicUrl { get; set; }
    public string? FileUrl { get; set; }
    public string? ShareToken { get; set; }
    public bool IsPublic { get; set; } = false;

    public int? FolderId { get; set; }
    public MediaFolder? Folder { get; set; }

    public long? UserId { get; set; }
    public User? User { get; set; }
}
