import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import axios from 'axios';

interface MediaAsset {
  id: number;
  fileName: string;
  originalFileName: string;
  fileType: string;
  fileSize: number;
  publicUrl: string;
  uploadedByUsername?: string;
  createdAt: string;
}

export default function MediaImageTest() {
  const [assets, setAssets] = useState<MediaAsset[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAssets = async () => {
      try {
        setLoading(true);
        const response = await axios.get('/api/media/assets');
        console.log('API Response:', response.data);
        setAssets(response.data || []);
        setError(null);
      } catch (err) {
        console.error('Error fetching assets:', err);
        setError('Failed to load media assets');
      } finally {
        setLoading(false);
      }
    };

    fetchAssets();
  }, []);

  return (
    <div className="container py-8">
      <h1 className="text-3xl font-bold mb-6">Media Image Test</h1>
      
      {loading ? (
        <div className="flex justify-center items-center p-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      ) : error ? (
        <div className="p-8 space-y-4 border border-destructive/50 rounded-lg bg-destructive/10">
          <h2 className="text-xl font-bold text-destructive">Error</h2>
          <p>{error}</p>
          <Button onClick={() => window.location.reload()} variant="outline">
            Reload Page
          </Button>
        </div>
      ) : (
        <div className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {assets.map(asset => (
              <Card key={asset.id} className="overflow-hidden">
                <CardHeader className="p-4">
                  <CardTitle className="text-base truncate">{asset.originalFileName}</CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  {asset.fileType.startsWith('image/') && (
                    <div className="space-y-4">
                      <div className="p-4 border-t">
                        <h3 className="text-sm font-medium mb-2">1. Direct URL from API:</h3>
                        <div className="bg-black/5 p-2 rounded">
                          <img 
                            src={asset.publicUrl} 
                            alt={asset.originalFileName}
                            className="max-h-32 mx-auto"
                          />
                        </div>
                        <div className="mt-1 text-xs text-muted-foreground break-all">
                          URL: {asset.publicUrl}
                        </div>
                      </div>
                      
                      <div className="p-4 border-t">
                        <h3 className="text-sm font-medium mb-2">2. With Origin Prefix:</h3>
                        <div className="bg-black/5 p-2 rounded">
                          <img 
                            src={`${window.location.origin}${asset.publicUrl}`} 
                            alt={asset.originalFileName}
                            className="max-h-32 mx-auto"
                          />
                        </div>
                        <div className="mt-1 text-xs text-muted-foreground break-all">
                          URL: {`${window.location.origin}${asset.publicUrl}`}
                        </div>
                      </div>
                      
                      <div className="p-4 border-t">
                        <h3 className="text-sm font-medium mb-2">3. With API Path:</h3>
                        <div className="bg-black/5 p-2 rounded">
                          <img 
                            src={`/api/media/assets/${asset.id}/content`} 
                            alt={asset.originalFileName}
                            className="max-h-32 mx-auto"
                          />
                        </div>
                        <div className="mt-1 text-xs text-muted-foreground break-all">
                          URL: {`/api/media/assets/${asset.id}/content`}
                        </div>
                      </div>
                      
                      <div className="p-4 border-t">
                        <h3 className="text-sm font-medium mb-2">4. With File Name:</h3>
                        <div className="bg-black/5 p-2 rounded">
                          <img 
                            src={`/uploads/${asset.fileName}`} 
                            alt={asset.originalFileName}
                            className="max-h-32 mx-auto"
                          />
                        </div>
                        <div className="mt-1 text-xs text-muted-foreground break-all">
                          URL: {`/uploads/${asset.fileName}`}
                        </div>
                      </div>
                    </div>
                  )}
                  
                  <div className="p-4 border-t">
                    <h3 className="text-sm font-medium mb-2">Asset Details:</h3>
                    <pre className="text-xs bg-muted p-2 rounded overflow-auto">
                      {JSON.stringify(asset, null, 2)}
                    </pre>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          
          {assets.length === 0 && (
            <div className="text-center p-12 border border-dashed rounded-lg">
              <p className="text-muted-foreground">No media assets found</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
