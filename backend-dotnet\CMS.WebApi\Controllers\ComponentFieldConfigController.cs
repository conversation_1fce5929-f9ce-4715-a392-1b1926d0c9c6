using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.Responses;
using CMS.WebApi.Services.Interfaces;
using CMS.WebApi.Security;

namespace CMS.WebApi.Controllers;

[ApiController]
[Route("api/component-field-configs")]
[Authorize(Policy = AuthorizationPolicies.JwtOrApiToken)]
[Tags("Component Field Config Management")]
public class ComponentFieldConfigController : ControllerBase
{
    private readonly IComponentFieldConfigService _componentFieldConfigService;
    private readonly ILogger<ComponentFieldConfigController> _logger;

    public ComponentFieldConfigController(
        IComponentFieldConfigService componentFieldConfigService,
        ILogger<ComponentFieldConfigController> logger)
    {
        _componentFieldConfigService = componentFieldConfigService;
        _logger = logger;
    }

    /// <summary>
    /// Get component field configs by field ID
    /// </summary>
    /// <param name="componentFieldId">Component field ID</param>
    /// <returns>List of component field configs</returns>
    [HttpGet("getByFieldId/{componentFieldId}")]
    [ProducesResponseType(typeof(IEnumerable<ComponentFieldConfigResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    public async Task<ActionResult<IEnumerable<ComponentFieldConfigResponse>>> GetByComponentFieldId(int componentFieldId)
    {
        try
        {
            _logger.LogInformation("Component field configs requested for field ID: {FieldId}", componentFieldId);

            var configs = await _componentFieldConfigService.GetComponentFieldConfigsByComponentFieldIdAsync(componentFieldId);

            if (!configs.Any())
            {
                return NoContent();
            }

            var response = configs.Select(c => new ComponentFieldConfigResponse
            {
                Id = c.Id,
                ComponentFieldId = c.ComponentFieldId,
                FieldConfigId = c.FieldConfigId,
                ConfigName = c.FieldConfig?.ConfigName ?? "",
                ConfigValue = c.ConfigValue,
                IsActive = c.IsActive,
                ValueType = c.FieldConfig?.ValueType,
                CreatedAt = c.CreatedAt,
                CreatedBy = c.CreatedBy
            });

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving component field configs for field ID: {FieldId}", componentFieldId);
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving component field configs",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Create a new component field config
    /// </summary>
    /// <param name="config">Component field config details</param>
    /// <returns>Created component field config</returns>
    [HttpPost("create")]
    [ProducesResponseType(typeof(ComponentFieldConfigResponse), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ComponentFieldConfigResponse>> CreateComponentFieldConfig([FromBody] ComponentFieldConfig config)
    {
        try
        {
            _logger.LogInformation("Component field config creation requested for field: {ComponentFieldId}", config.ComponentFieldId);

            var createdConfig = await _componentFieldConfigService.CreateComponentFieldConfigAsync(config);

            var response = new ComponentFieldConfigResponse
            {
                Id = createdConfig.Id,
                ComponentFieldId = createdConfig.ComponentFieldId,
                FieldConfigId = createdConfig.FieldConfigId,
                ConfigName = createdConfig.FieldConfig?.ConfigName ?? "",
                ConfigValue = createdConfig.ConfigValue,
                IsActive = createdConfig.IsActive,
                ValueType = createdConfig.FieldConfig?.ValueType,
                CreatedAt = createdConfig.CreatedAt,
                CreatedBy = createdConfig.CreatedBy
            };

            return CreatedAtAction(nameof(GetByComponentFieldId), new { componentFieldId = createdConfig.ComponentFieldId }, response);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid request for creating component field config");
            return BadRequest(new ErrorResponse
            {
                Status = 400,
                Error = "Bad Request",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating component field config");
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while creating the component field config",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Update an existing component field config
    /// </summary>
    /// <param name="id">Component field config ID</param>
    /// <param name="config">Updated component field config details</param>
    /// <returns>Updated component field config</returns>
    [HttpPut("update/{id}")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status501NotImplemented)]
    public async Task<ActionResult<object>> UpdateComponentFieldConfig(int id, [FromBody] object config)
    {
        try
        {
            _logger.LogInformation("Component field config update requested for ID: {ConfigId}", id);
            
            return StatusCode(StatusCodes.Status501NotImplemented, new ErrorResponse
            {
                Status = 501,
                Error = "Not Implemented",
                Message = "Component field config update is not yet implemented",
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating component field config: {ConfigId}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while updating the component field config",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Delete a component field config
    /// </summary>
    /// <param name="id">Component field config ID</param>
    /// <returns>No content</returns>
    [HttpDelete("deleteById/{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status501NotImplemented)]
    public async Task<ActionResult> DeleteComponentFieldConfig(int id)
    {
        try
        {
            _logger.LogInformation("Component field config deletion requested for ID: {ConfigId}", id);
            
            return StatusCode(StatusCodes.Status501NotImplemented, new ErrorResponse
            {
                Status = 501,
                Error = "Not Implemented",
                Message = "Component field config deletion is not yet implemented",
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting component field config: {ConfigId}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while deleting the component field config",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Bulk create component field configs
    /// </summary>
    /// <param name="configs">List of component field configs to create</param>
    /// <returns>Created component field configs</returns>
    [HttpPost("createBulk")]
    [ProducesResponseType(typeof(IEnumerable<object>), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status501NotImplemented)]
    public async Task<ActionResult<IEnumerable<object>>> BulkCreateComponentFieldConfigs([FromBody] IEnumerable<object> configs)
    {
        try
        {
            _logger.LogInformation("Bulk component field config creation requested for {Count} configs", configs.Count());
            
            return StatusCode(StatusCodes.Status501NotImplemented, new ErrorResponse
            {
                Status = 501,
                Error = "Not Implemented",
                Message = "Bulk component field config creation is not yet implemented",
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error bulk creating component field configs");
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while bulk creating component field configs",
                Path = Request.Path
            });
        }
    }
}
