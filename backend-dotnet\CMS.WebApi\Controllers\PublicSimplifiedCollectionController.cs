using Microsoft.AspNetCore.Mvc;
using CMS.WebApi.Models.DTOs;
using CMS.WebApi.Models.Responses;
using CMS.WebApi.Services.Interfaces;
using CMS.WebApi.Services.Implementations;

namespace CMS.WebApi.Controllers;

[ApiController]
[Route("api/public/simplified-collections")]
[Tags("Public Simplified Collection Management")]
public class PublicSimplifiedCollectionController : ControllerBase
{
    private readonly ICollectionService _collectionService;
    private readonly SimplifiedCollectionMapper _mapper;
    private readonly ILogger<PublicSimplifiedCollectionController> _logger;

    public PublicSimplifiedCollectionController(
        ICollectionService collectionService,
        SimplifiedCollectionMapper mapper,
        ILogger<PublicSimplifiedCollectionController> logger)
    {
        _collectionService = collectionService;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// Get all collections in simplified format (public endpoint)
    /// </summary>
    /// <returns>List of all collections in simplified format</returns>
    [HttpGet]
    [ProducesResponseType(typeof(List<SimplifiedCollectionDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<List<SimplifiedCollectionDto>>> GetAllSimplifiedCollections()
    {
        try
        {
            _logger.LogInformation("PUBLIC API - Getting all simplified collections");

            var collections = await _collectionService.GetAllCollectionsWithDetailsAsync();

            if (!collections.Any())
            {
                _logger.LogInformation("PUBLIC API - No collections found");
                return Ok(new List<SimplifiedCollectionDto>());
            }

            _logger.LogInformation("PUBLIC API - Found {Count} collections from database", collections.Count());

            // Log details about each collection for debugging
            foreach (var collection in collections)
            {
                _logger.LogInformation("Collection ID {Id}: {Name}, Components: {ComponentCount}, Fields: {FieldCount}",
                    collection.Id, collection.CollectionName,
                    collection.Components?.Count ?? 0,
                    collection.Fields?.Count ?? 0);

                if (collection.Components != null)
                {
                    foreach (var component in collection.Components)
                    {
                        _logger.LogInformation("  Component ID {ComponentId}: {ComponentName}, Fields: {FieldCount}",
                            component.ComponentId, component.Component?.ComponentName ?? "Unknown",
                            component.Component?.Fields?.Count ?? 0);

                        if (component.Component?.Fields != null)
                        {
                            foreach (var field in component.Component.Fields)
                            {
                                _logger.LogInformation("    ComponentField ID {FieldId}: Configs: {ConfigCount}",
                                    field.Id, field.Configs?.Count ?? 0);
                            }
                        }
                    }
                }

                if (collection.Fields != null)
                {
                    foreach (var field in collection.Fields)
                    {
                        _logger.LogInformation("  CollectionField ID {FieldId}: Configs: {ConfigCount}",
                            field.Id, field.Configs?.Count ?? 0);
                    }
                }
            }

            var simplifiedCollections = _mapper.ToDtoList(collections.ToList());

            _logger.LogInformation("PUBLIC API - Mapped to {Count} simplified collections", simplifiedCollections.Count);
            return Ok(simplifiedCollections);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "PUBLIC API - Failed to retrieve simplified collections");
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving simplified collections",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get simplified collection by ID (public endpoint)
    /// </summary>
    /// <param name="id">Collection ID</param>
    /// <returns>Simplified collection details</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(SimplifiedCollectionDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<SimplifiedCollectionDto>> GetSimplifiedCollectionById(int id)
    {
        try
        {
            _logger.LogInformation("PUBLIC API - Getting simplified collection with ID: {CollectionId}", id);

            var collection = await _collectionService.GetCollectionByIdWithDetailsAsync(id);
            if (collection == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Collection with ID {id} not found",
                    Path = Request.Path
                });
            }

            var simplifiedCollection = _mapper.ToDto(collection);
            
            _logger.LogInformation("PUBLIC API - Found collection: {CollectionName}", collection.CollectionName);
            return Ok(simplifiedCollection);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "PUBLIC API - Failed to retrieve simplified collection with ID: {CollectionId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving the simplified collection",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get simplified collection by API ID (public endpoint)
    /// </summary>
    /// <param name="apiId">Collection API ID</param>
    /// <returns>Simplified collection details</returns>
    [HttpGet("api/{apiId}")]
    [ProducesResponseType(typeof(SimplifiedCollectionDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<SimplifiedCollectionDto>> GetSimplifiedCollectionByApiId(string apiId)
    {
        try
        {
            _logger.LogInformation("PUBLIC API - Getting simplified collection with API ID: {ApiId}", apiId);

            var collection = await _collectionService.GetCollectionByApiIdWithDetailsAsync(apiId);
            if (collection == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Collection with API ID '{apiId}' not found",
                    Path = Request.Path
                });
            }

            var simplifiedCollection = _mapper.ToDto(collection);
            
            _logger.LogInformation("PUBLIC API - Found collection: {CollectionName}", collection.CollectionName);
            return Ok(simplifiedCollection);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "PUBLIC API - Failed to retrieve simplified collection with API ID: {ApiId}", apiId);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving the simplified collection",
                Path = Request.Path
            });
        }
    }
}
