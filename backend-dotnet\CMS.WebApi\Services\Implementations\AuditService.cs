using System.Security.Claims;
using CMS.WebApi.Services.Interfaces;

namespace CMS.WebApi.Services.Implementations;

public class AuditService : IAuditService
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ILogger<AuditService> _logger;

    public AuditService(IHttpContextAccessor httpContextAccessor, ILogger<AuditService> logger)
    {
        _httpContextAccessor = httpContextAccessor;
        _logger = logger;
    }

    public string GetCurrentUser()
    {
        try
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.User?.Identity?.IsAuthenticated == true)
            {
                // Try to get username from claims
                var username = httpContext.User.FindFirst(ClaimTypes.Name)?.Value;
                if (!string.IsNullOrEmpty(username))
                {
                    _logger.LogDebug("Current user for audit: {Username}", username);
                    return username;
                }

                // Fallback to user ID if username is not available
                var userId = httpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!string.IsNullOrEmpty(userId))
                {
                    _logger.LogDebug("Current user ID for audit: {UserId}", userId);
                    return $"user_{userId}";
                }
            }

            _logger.LogDebug("No authenticated user found, using 'system' for audit");
            return "system";
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting current user for audit, using 'system'");
            return "system";
        }
    }

    public long? GetCurrentUserId()
    {
        try
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.User?.Identity?.IsAuthenticated == true)
            {
                var userIdClaim = httpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (long.TryParse(userIdClaim, out var userId))
                {
                    return userId;
                }
            }
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting current user ID");
            return null;
        }
    }

    public string? GetCurrentUsername()
    {
        try
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.User?.Identity?.IsAuthenticated == true)
            {
                return httpContext.User.FindFirst(ClaimTypes.Name)?.Value;
            }
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting current username");
            return null;
        }
    }
}
