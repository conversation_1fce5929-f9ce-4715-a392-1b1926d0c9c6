using CMS.WebApi.Data;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace CMS.WebApi.Services.Implementations;

public class MediaService : IMediaService
{
    private readonly CmsDbContext _context;
    private readonly IConfiguration _configuration;
    private readonly ILogger<MediaService> _logger;

    public MediaService(CmsDbContext context, IConfiguration configuration, ILogger<MediaService> logger)
    {
        _context = context;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task<IEnumerable<Media>> GetAllMediaAsync()
    {
        return await _context.Media.ToListAsync();
    }

    public async Task<IEnumerable<Media>> GetMediaByFolderAsync(int? folderId)
    {
        return await _context.Media
            .Where(m => m.FolderId == folderId)
            .OrderByDescending(m => m.CreatedAt)
            .ToListAsync();
    }

    public async Task<Media?> GetMediaByIdAsync(int id)
    {
        return await _context.Media.FindAsync(id);
    }

    public async Task<Media> CreateMediaAsync(Media media)
    {
        media.CreatedAt = DateTime.UtcNow;
        _context.Media.Add(media);
        await _context.SaveChangesAsync();
        return media;
    }

    public async Task<Media> UpdateMediaAsync(int id, Media media)
    {
        var existingMedia = await _context.Media.FindAsync(id);
        if (existingMedia == null)
            throw new ArgumentException($"Media with ID {id} not found");

        existingMedia.FileName = media.FileName;
        existingMedia.OriginalFileName = media.OriginalFileName;
        existingMedia.FileType = media.FileType;
        existingMedia.FileSize = media.FileSize;
        existingMedia.AltText = media.AltText;
        existingMedia.Description = media.Description;
        existingMedia.ModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return existingMedia;
    }

    public async Task DeleteMediaAsync(int id)
    {
        var media = await _context.Media.FindAsync(id);
        if (media != null)
        {
            // Delete physical file
            if (File.Exists(media.FilePath))
            {
                File.Delete(media.FilePath);
            }

            _context.Media.Remove(media);
            await _context.SaveChangesAsync();
        }
    }

    public async Task<Media> UploadFileAsync(IFormFile file, int? folderId = null)
    {
        if (file == null || file.Length == 0)
            throw new ArgumentException("File is required");

        // Validate file type and size
        ValidateFile(file);

        // Create upload directory structure
        var (fullUploadPath, relativePath) = CreateUploadDirectory();

        // Generate unique filename
        var fileName = GenerateUniqueFileName(file.FileName);
        var filePath = Path.Combine(fullUploadPath, fileName);

        // Save file to disk
        await SaveFileAsync(file, filePath);

        // Generate file URL
        var baseUrl = _configuration["FileUpload:BaseUrl"] ?? "http://localhost:5000";
        var fileUrl = $"{baseUrl}/api/media/files/{relativePath}/{fileName}";

        var media = new Media
        {
            FileName = fileName,
            OriginalFileName = file.FileName,
            FilePath = filePath,
            FileUrl = fileUrl,
            PublicUrl = fileUrl, // Set PublicUrl to the same as FileUrl for frontend compatibility
            FileType = file.ContentType,
            FileSize = file.Length,
            FolderId = folderId,
            IsPublic = false,
            ShareToken = GenerateShareToken(),
            CreatedAt = DateTime.UtcNow
        };

        return await CreateMediaAsync(media);
    }

    public async Task<byte[]> GetFileContentAsync(int mediaId)
    {
        var media = await GetMediaByIdAsync(mediaId);
        if (media == null || !File.Exists(media.FilePath))
            throw new FileNotFoundException("Media file not found");

        return await File.ReadAllBytesAsync(media.FilePath);
    }

    public async Task<int> UpdatePublicUrlsAsync()
    {
        var baseUrl = _configuration["FileUpload:BaseUrl"] ?? "http://localhost:5000";
        var mediaRecords = await _context.Media
            .Where(m => string.IsNullOrEmpty(m.PublicUrl))
            .ToListAsync();

        int updatedCount = 0;
        foreach (var media in mediaRecords)
        {
            if (!string.IsNullOrEmpty(media.FileUrl))
            {
                media.PublicUrl = media.FileUrl;
                updatedCount++;
            }
            else if (!string.IsNullOrEmpty(media.FileName))
            {
                // Try to reconstruct the URL from the filename
                var fileName = media.FileName;
                var createdDate = media.CreatedAt;
                var year = createdDate.Year.ToString();
                var month = createdDate.Month.ToString("00");

                var fileUrl = $"{baseUrl}/api/media/files/{year}/{month}/{fileName}";
                media.FileUrl = fileUrl;
                media.PublicUrl = fileUrl;
                updatedCount++;
            }
        }

        if (updatedCount > 0)
        {
            await _context.SaveChangesAsync();
        }

        return updatedCount;
    }

    public async Task<string> GetFilePathAsync(int mediaId)
    {
        var media = await GetMediaByIdAsync(mediaId);
        if (media == null)
            throw new ArgumentException($"Media with ID {mediaId} not found");

        return media.FilePath;
    }

    public async Task<Media?> GetMediaByShareTokenAsync(string shareToken)
    {
        return await _context.Media
            .FirstOrDefaultAsync(m => m.ShareToken == shareToken);
    }

    public async Task<Media> ReplaceFileAsync(int mediaId, IFormFile newFile)
    {
        // Get the existing media record
        var existingMedia = await GetMediaByIdAsync(mediaId);
        if (existingMedia == null)
        {
            throw new ArgumentException($"Media with ID {mediaId} not found");
        }

        // Validate the new file
        ValidateFile(newFile);

        // Delete the old file from disk if it exists
        if (File.Exists(existingMedia.FilePath))
        {
            File.Delete(existingMedia.FilePath);
        }

        // Save the new file to the same location (keeping the same filename)
        await SaveFileAsync(newFile, existingMedia.FilePath);

        // Update the media record with new file information
        existingMedia.OriginalFileName = newFile.FileName;
        existingMedia.FileType = newFile.ContentType;
        existingMedia.FileSize = newFile.Length;
        existingMedia.ModifiedAt = DateTime.UtcNow;

        // Keep the same URLs, ShareToken, and other metadata
        // This ensures that all existing links continue to work

        await _context.SaveChangesAsync();

        return existingMedia;
    }

    private void ValidateFile(IFormFile file)
    {
        // Check file size (default 10MB limit)
        var maxFileSize = _configuration.GetValue<long>("FileUpload:MaxFileSize", 10 * 1024 * 1024);
        if (file.Length > maxFileSize)
        {
            throw new ArgumentException($"File size exceeds the maximum allowed size of {maxFileSize / 1024 / 1024}MB");
        }

        // Check allowed file types
        var allowedExtensions = _configuration.GetSection("FileUpload:AllowedExtensions").Get<string[]>()
            ?? new[] { ".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx", ".txt" };

        var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
        if (!allowedExtensions.Contains(fileExtension))
        {
            throw new ArgumentException($"File type '{fileExtension}' is not allowed. Allowed types: {string.Join(", ", allowedExtensions)}");
        }
    }

    private (string fullPath, string relativePath) CreateUploadDirectory()
    {
        var uploadDir = _configuration["FileUpload:UploadDirectory"] ?? "media-uploads";
        var currentDate = DateTime.UtcNow;
        var yearMonth = Path.Combine(currentDate.Year.ToString(), currentDate.Month.ToString("00"));

        var fullUploadPath = Path.Combine(Directory.GetCurrentDirectory(), uploadDir, yearMonth);

        if (!Directory.Exists(fullUploadPath))
            Directory.CreateDirectory(fullUploadPath);

        return (fullUploadPath, yearMonth);
    }

    private static string GenerateUniqueFileName(string originalFileName)
    {
        var fileExtension = Path.GetExtension(originalFileName);
        var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(originalFileName);

        // Sanitize filename
        var sanitizedName = string.Join("_", fileNameWithoutExtension.Split(Path.GetInvalidFileNameChars()));

        return $"{DateTime.UtcNow:yyyyMMdd_HHmmss}_{Guid.NewGuid():N}_{sanitizedName}{fileExtension}";
    }

    private static async Task SaveFileAsync(IFormFile file, string filePath)
    {
        using var stream = new FileStream(filePath, FileMode.Create);
        await file.CopyToAsync(stream);
    }

    private static string GenerateShareToken()
    {
        return Guid.NewGuid().ToString("N")[..16]; // 16 character token
    }
}
