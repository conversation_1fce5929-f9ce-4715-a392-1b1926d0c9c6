using Microsoft.EntityFrameworkCore;
using CMS.WebApi.Data;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.Requests;
using CMS.WebApi.Services.Interfaces;

namespace CMS.WebApi.Services.Implementations;

public class CollectionFieldService : ICollectionFieldService
{
    private readonly CmsDbContext _context;
    private readonly ICollectionOrderingService _collectionOrderingService;
    private readonly ILogger<CollectionFieldService> _logger;

    public CollectionFieldService(
        CmsDbContext context,
        ICollectionOrderingService collectionOrderingService,
        ILogger<CollectionFieldService> logger)
    {
        _context = context;
        _collectionOrderingService = collectionOrderingService;
        _logger = logger;
    }

    public async Task<CollectionField?> GetCollectionFieldByIdAsync(int id)
    {
        return await _context.CollectionFields
            .Include(cf => cf.Collection)
            .Include(cf => cf.FieldType)
            .Include(cf => cf.Configs)
            .ThenInclude(c => c.FieldConfig)
            .FirstOrDefaultAsync(cf => cf.Id == id);
    }

    public async Task<IEnumerable<CollectionField>> GetAllCollectionFieldsAsync()
    {
        return await _context.CollectionFields
            .Include(cf => cf.Collection)
            .Include(cf => cf.FieldType)
            .Include(cf => cf.Configs)
            .ThenInclude(c => c.FieldConfig)
            .OrderBy(cf => cf.CollectionId)
            .ThenBy(cf => cf.DisplayPreference)
            .ToListAsync();
    }

    public async Task<IEnumerable<CollectionField>> GetCollectionFieldsByCollectionIdAsync(int collectionId)
    {
        // Verify collection exists
        var collectionExists = await _context.CollectionListings
            .AnyAsync(c => c.Id == collectionId);

        if (!collectionExists)
        {
            throw new ArgumentException($"Collection not found with id: {collectionId}");
        }

        return await _context.CollectionFields
            .Include(cf => cf.FieldType)
            .Include(cf => cf.Configs)
            .ThenInclude(c => c.FieldConfig)
            .Where(cf => cf.CollectionId == collectionId)
            .OrderBy(cf => cf.DisplayPreference ?? int.MaxValue)
            .ToListAsync();
    }

    public async Task<CollectionField> CreateCollectionFieldAsync(CollectionField collectionField)
    {
        // Validate field type
        if (collectionField.FieldTypeId <= 0)
        {
            throw new ArgumentException("Field type is required");
        }

        // Verify field type exists
        var fieldType = await _context.FieldTypes
            .FirstOrDefaultAsync(ft => ft.Id == collectionField.FieldTypeId);

        if (fieldType == null)
        {
            throw new ArgumentException($"Field type with ID {collectionField.FieldTypeId} not found");
        }

        // Verify collection exists
        if (collectionField.CollectionId <= 0)
        {
            throw new ArgumentException("Collection is required");
        }

        var collection = await _context.CollectionListings
            .FirstOrDefaultAsync(c => c.Id == collectionField.CollectionId);

        if (collection == null)
        {
            throw new ArgumentException($"Collection with ID {collectionField.CollectionId} not found");
        }

        // Set display preference to be the max value for this collection + 10
        var nextDisplayPreference = await _collectionOrderingService.GetNextDisplayPreferenceAsync(collectionField.CollectionId);
        collectionField.DisplayPreference = nextDisplayPreference;

        _logger.LogDebug("Setting display preference to {DisplayPreference} for collection {CollectionId}",
            collectionField.DisplayPreference, collectionField.CollectionId);

        // Set audit fields
        collectionField.CreatedAt = DateTime.UtcNow;

        _context.CollectionFields.Add(collectionField);
        await _context.SaveChangesAsync();

        _logger.LogInformation("Collection field created successfully: ID {Id}, Collection {CollectionId}, FieldType {FieldTypeId}",
            collectionField.Id, collectionField.CollectionId, collectionField.FieldTypeId);

        return collectionField;
    }

    public async Task<CollectionField> CreateCollectionFieldWithConfigsAsync(CreateCollectionFieldWithConfigsRequest request)
    {
        _logger.LogInformation("Creating collection field with {ConfigCount} configurations for collection ID: {CollectionId}",
            request.Configurations.Count, request.CollectionId);

        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            // 1. Validate and create the collection field
            var collectionField = new CollectionField
            {
                CollectionId = request.CollectionId,
                FieldTypeId = request.FieldTypeId,
                DisplayPreference = request.DisplayPreference,
                DependentOnId = request.DependentOnId,
                AdditionalInformation = request.AdditionalInformation,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "system" // TODO: Get from current user context
            };

            // Validate field type exists
            var fieldType = await _context.FieldTypes
                .FirstOrDefaultAsync(ft => ft.Id == request.FieldTypeId);
            if (fieldType == null)
            {
                throw new ArgumentException($"Field type with ID {request.FieldTypeId} not found");
            }

            // Validate collection exists
            var collection = await _context.CollectionListings
                .FirstOrDefaultAsync(c => c.Id == request.CollectionId);
            if (collection == null)
            {
                throw new ArgumentException($"Collection with ID {request.CollectionId} not found");
            }

            // Set display preference if not provided
            if (collectionField.DisplayPreference == null)
            {
                var maxDisplayPreference = await _context.CollectionFields
                    .Where(cf => cf.CollectionId == request.CollectionId)
                    .MaxAsync(cf => (int?)cf.DisplayPreference);
                collectionField.DisplayPreference = (maxDisplayPreference ?? 0) + 10;
            }

            // Save the collection field
            _context.CollectionFields.Add(collectionField);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Collection field created with ID: {FieldId}", collectionField.Id);

            // 2. Create field configurations if provided
            if (request.Configurations.Any())
            {
                _logger.LogInformation("Creating {ConfigCount} field configurations for field ID: {FieldId}",
                    request.Configurations.Count, collectionField.Id);

                var fieldConfigs = request.Configurations.Select(config => new CollectionFieldConfig
                {
                    CollectionFieldId = collectionField.Id,
                    FieldConfigId = config.FieldConfigId,
                    ConfigValue = config.ConfigValue,
                    IsActive = config.IsActive,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = "system" // TODO: Get from current user context
                }).ToList();

                // Validate that all field configs exist
                var fieldConfigIds = request.Configurations.Select(c => c.FieldConfigId).ToList();
                var existingFieldConfigs = await _context.FieldConfigs
                    .Where(fc => fieldConfigIds.Contains(fc.Id))
                    .Select(fc => fc.Id)
                    .ToListAsync();

                var missingFieldConfigs = fieldConfigIds.Except(existingFieldConfigs).ToList();
                if (missingFieldConfigs.Any())
                {
                    throw new ArgumentException($"Field configs not found: {string.Join(", ", missingFieldConfigs)}");
                }

                foreach (var config in fieldConfigs)
                {
                    _logger.LogInformation("Creating config: CollectionFieldId={CollectionFieldId}, FieldConfigId={FieldConfigId}, ConfigValue={ConfigValue}",
                        config.CollectionFieldId, config.FieldConfigId, config.ConfigValue);
                }

                _context.CollectionFieldConfigs.AddRange(fieldConfigs);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Successfully created {ConfigCount} field configurations", fieldConfigs.Count);
            }

            await transaction.CommitAsync();

            // Return the created field with configurations loaded
            var createdField = await _context.CollectionFields
                .Include(cf => cf.Collection)
                .Include(cf => cf.FieldType)
                .Include(cf => cf.Configs)
                    .ThenInclude(c => c.FieldConfig)
                .FirstOrDefaultAsync(cf => cf.Id == collectionField.Id);

            _logger.LogInformation("Collection field with configurations created successfully: ID {FieldId}", collectionField.Id);

            return createdField ?? throw new InvalidOperationException("Failed to retrieve created collection field");
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, "Error creating collection field with configurations for collection ID: {CollectionId}", request.CollectionId);
            throw;
        }
    }

    public async Task<CollectionField> UpdateCollectionFieldAsync(int id, CollectionField collectionField)
    {
        var existingField = await _context.CollectionFields
            .FirstOrDefaultAsync(cf => cf.Id == id);

        if (existingField == null)
        {
            throw new ArgumentException($"Collection field with ID {id} not found");
        }

        // Update properties
        existingField.FieldTypeId = collectionField.FieldTypeId;
        existingField.DisplayPreference = collectionField.DisplayPreference;
        existingField.DependentOnId = collectionField.DependentOnId;
        existingField.AdditionalInformation = collectionField.AdditionalInformation;
        existingField.ModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        _logger.LogInformation("Collection field updated successfully: ID {Id}", id);

        return existingField;
    }

    public async Task<CollectionField> UpdateCollectionFieldWithConfigsAsync(int id, UpdateCollectionFieldRequest request)
    {
        _logger.LogInformation("Updating collection field with configurations: ID {FieldId}", id);

        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            // 1. Update the collection field
            var existingField = await _context.CollectionFields
                .Include(cf => cf.Configs)
                .FirstOrDefaultAsync(cf => cf.Id == id);

            if (existingField == null)
            {
                throw new ArgumentException($"Collection field with ID {id} not found");
            }

            // Update basic properties
            existingField.CollectionId = request.CollectionId > 0 ? request.CollectionId : request.Collection?.Id ?? existingField.CollectionId;
            existingField.FieldTypeId = request.FieldTypeId > 0 ? request.FieldTypeId : request.FieldType?.Id ?? existingField.FieldTypeId;
            existingField.DisplayPreference = request.DisplayPreference ?? existingField.DisplayPreference;
            existingField.DependentOnId = request.DependentOnId;
            existingField.AdditionalInformation = request.AdditionalInformation ?? existingField.AdditionalInformation;
            existingField.ModifiedAt = DateTime.UtcNow;

            // 2. Update configurations if provided
            if (request.Configs.Any())
            {
                _logger.LogInformation("Updating {ConfigCount} configurations for field ID: {FieldId}",
                    request.Configs.Count, id);

                // Remove existing configurations
                var existingConfigs = existingField.Configs.ToList();
                _context.CollectionFieldConfigs.RemoveRange(existingConfigs);

                // Add new configurations
                var newConfigs = request.Configs.Select(config => new CollectionFieldConfig
                {
                    CollectionFieldId = id,
                    FieldConfigId = config.FieldConfigId,
                    ConfigValue = config.ConfigValue,
                    IsActive = config.IsActive,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = "system" // TODO: Get from current user context
                }).ToList();

                _logger.LogInformation("Creating {NewConfigCount} new configurations for field ID: {FieldId}",
                    newConfigs.Count, id);

                foreach (var config in newConfigs)
                {
                    _logger.LogDebug("Adding config: FieldConfigId={FieldConfigId}, Value='{Value}', IsActive={IsActive}",
                        config.FieldConfigId, config.ConfigValue, config.IsActive);
                }

                _context.CollectionFieldConfigs.AddRange(newConfigs);
                existingField.Configs = newConfigs;
            }

            await _context.SaveChangesAsync();
            await transaction.CommitAsync();

            _logger.LogInformation("Collection field with configurations updated successfully: ID {FieldId}", id);

            // Return the updated field with all related data
            return await _context.CollectionFields
                .Include(cf => cf.Collection)
                .Include(cf => cf.FieldType)
                .Include(cf => cf.Configs)
                    .ThenInclude(c => c.FieldConfig)
                .FirstOrDefaultAsync(cf => cf.Id == id) ?? existingField;
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, "Error updating collection field with configurations: ID {FieldId}", id);
            throw;
        }
    }

    public async Task<CollectionField> UpdateDisplayPreferenceAsync(int id, int displayPreference)
    {
        var field = await _context.CollectionFields
            .FirstOrDefaultAsync(cf => cf.Id == id);

        if (field == null)
        {
            throw new ArgumentException($"Collection field with ID {id} not found");
        }

        field.DisplayPreference = displayPreference;
        field.ModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        _logger.LogInformation("Display preference updated for collection field {Id}: {DisplayPreference}", id, displayPreference);

        return field;
    }

    public async Task DeleteCollectionFieldAsync(int id)
    {
        var field = await _context.CollectionFields
            .FirstOrDefaultAsync(cf => cf.Id == id);

        if (field == null)
        {
            throw new ArgumentException($"Collection field with ID {id} not found");
        }

        _context.CollectionFields.Remove(field);
        await _context.SaveChangesAsync();

        _logger.LogInformation("Collection field deleted successfully: ID {Id}", id);
    }

    public async Task<int> GetNextAvailableIdAsync()
    {
        // Get the maximum ID currently in use
        var maxId = await _context.CollectionFields
            .MaxAsync(cf => (int?)cf.Id);

        // If no records exist, start with ID 1, otherwise use max + 1
        return maxId.HasValue ? maxId.Value + 1 : 1;
    }
}
