import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Settings, Database, Wrench } from 'lucide-react';
import FieldValidationFixer from '@/components/admin/FieldValidationFixer';

export default function AdminTools() {
  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-8 pb-4 border-b">
        <div className="flex items-center">
          <Settings className="h-8 w-8 text-primary mr-3" />
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Admin Tools</h1>
            <p className="text-muted-foreground mt-1">
              Administrative utilities and maintenance tools
            </p>
          </div>
        </div>
      </div>

      <Tabs defaultValue="database" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="database" className="flex items-center space-x-2">
            <Database className="h-4 w-4" />
            <span>Database Tools</span>
          </TabsTrigger>
          <TabsTrigger value="maintenance" className="flex items-center space-x-2">
            <Wrench className="h-4 w-4" />
            <span>Maintenance</span>
          </TabsTrigger>
          <TabsTrigger value="system" className="flex items-center space-x-2">
            <Settings className="h-4 w-4" />
            <span>System</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="database" className="space-y-6">
          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Database className="h-5 w-5" />
                  <span>Database Maintenance</span>
                </CardTitle>
                <CardDescription>
                  Tools for maintaining and fixing database inconsistencies
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FieldValidationFixer />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="maintenance" className="space-y-6">
          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Wrench className="h-5 w-5" />
                  <span>System Maintenance</span>
                </CardTitle>
                <CardDescription>
                  General system maintenance and cleanup tools
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Additional maintenance tools will be added here as needed.
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="system" className="space-y-6">
          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Settings className="h-5 w-5" />
                  <span>System Configuration</span>
                </CardTitle>
                <CardDescription>
                  System-wide configuration and settings
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  System configuration tools will be added here as needed.
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
