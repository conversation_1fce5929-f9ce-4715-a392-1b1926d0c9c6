/**
 * Debug utility to check collection fields directly
 */

import { collectionFieldsApi, collectionsApi } from '@/lib/api';

interface CollectionFieldDebugInfo {
  id: number;
  collectionId: number;
  fieldTypeId: number;
  displayPreference: number;
  additionalInformation: string;
  createdAt?: string;
  modifiedAt?: string;
}

interface CollectionDebugInfo {
  id: number;
  collectionName: string;
  collectionApiId: string;
  categoryId: number;
}

/**
 * Debug collection fields for a specific collection
 */
export async function debugCollectionFields(collectionId: number): Promise<void> {
  console.log(`🔍 DEBUG: Checking collection fields for collection ID: ${collectionId}`);
  
  try {
    // First, verify the collection exists
    console.log('📋 Step 1: Verifying collection exists...');
    const collectionsResponse = await collectionsApi.getAll();
    const collections = collectionsResponse.data || [];
    const targetCollection = collections.find((c: CollectionDebugInfo) => c.id === collectionId);
    
    if (!targetCollection) {
      console.error(`❌ Collection with ID ${collectionId} not found!`);
      console.log('📋 Available collections:', collections.map((c: CollectionDebugInfo) => ({
        id: c.id,
        name: c.collectionName,
        apiId: c.collectionApiId
      })));
      return;
    }
    
    console.log(`✅ Collection found: ${targetCollection.collectionName} (API ID: ${targetCollection.collectionApiId})`);
    
    // Get fields for this collection
    console.log('📋 Step 2: Getting fields for this collection...');
    const fieldsResponse = await collectionFieldsApi.getByCollectionId(collectionId);
    const fields = fieldsResponse.data || [];
    
    console.log(`📊 Found ${fields.length} fields for collection ${collectionId}`);
    
    if (fields.length === 0) {
      console.warn('⚠️ No fields found for this collection');
      return;
    }
    
    // Log detailed information about each field
    console.log('🔍 Field Details:');
    fields.forEach((field: CollectionFieldDebugInfo, index: number) => {
      console.log(`\n📝 Field ${index + 1}:`);
      console.log(`   ID: ${field.id}`);
      console.log(`   Collection ID: ${field.collectionId}`);
      console.log(`   Field Type ID: ${field.fieldTypeId}`);
      console.log(`   Display Preference: ${field.displayPreference}`);
      console.log(`   Created At: ${field.createdAt || 'N/A'}`);
      console.log(`   Modified At: ${field.modifiedAt || 'N/A'}`);
      
      // Parse additional information
      if (field.additionalInformation) {
        try {
          const additionalInfo = JSON.parse(field.additionalInformation);
          console.log(`   Field Name: ${additionalInfo.name || 'N/A'}`);
          console.log(`   API ID: ${additionalInfo.apiId || 'N/A'}`);
          console.log(`   Type: ${additionalInfo.type || 'N/A'}`);
          console.log(`   Required: ${additionalInfo.required || false}`);
          console.log(`   Validations:`, additionalInfo.validations || {});
        } catch (error) {
          console.error(`   ❌ Failed to parse additionalInformation:`, error);
          console.log(`   Raw additionalInformation:`, field.additionalInformation);
        }
      } else {
        console.log(`   ❌ No additionalInformation found`);
      }
    });
    
  } catch (error) {
    console.error('❌ Error during debug:', error);
    console.log('🔍 Error details:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data
    });
  }
}

/**
 * Debug all collection fields in the system
 */
export async function debugAllCollectionFields(): Promise<void> {
  console.log('🔍 DEBUG: Checking all collection fields in the system');
  
  try {
    // Get all collections
    console.log('📋 Step 1: Getting all collections...');
    const collectionsResponse = await collectionsApi.getAll();
    const collections = collectionsResponse.data || [];
    
    console.log(`📊 Found ${collections.length} collections`);
    
    if (collections.length === 0) {
      console.warn('⚠️ No collections found in the system');
      return;
    }
    
    // Check fields for each collection
    for (const collection of collections) {
      console.log(`\n🔍 Checking fields for collection: ${collection.collectionName} (ID: ${collection.id})`);
      
      try {
        const fieldsResponse = await collectionFieldsApi.getByCollectionId(collection.id);
        const fields = fieldsResponse.data || [];
        
        console.log(`   📊 Found ${fields.length} fields`);
        
        if (fields.length > 0) {
          fields.forEach((field: CollectionFieldDebugInfo, index: number) => {
            let fieldName = 'Unknown';
            if (field.additionalInformation) {
              try {
                const additionalInfo = JSON.parse(field.additionalInformation);
                fieldName = additionalInfo.name || `Field ${field.id}`;
              } catch (error) {
                fieldName = `Field ${field.id}`;
              }
            }
            console.log(`   📝 Field ${index + 1}: ${fieldName} (ID: ${field.id})`);
          });
        }
        
      } catch (error) {
        console.error(`   ❌ Error getting fields for collection ${collection.id}:`, error);
      }
    }
    
  } catch (error) {
    console.error('❌ Error during debug:', error);
  }
}

/**
 * Check if there are any fields with mismatched collection IDs
 */
export async function debugFieldCollectionMismatch(): Promise<void> {
  console.log('🔍 DEBUG: Checking for field collection ID mismatches');
  
  try {
    // Get all fields
    console.log('📋 Getting all collection fields...');
    const allFieldsResponse = await collectionFieldsApi.getAll();
    const allFields = allFieldsResponse.data || [];
    
    console.log(`📊 Found ${allFields.length} total collection fields`);
    
    // Get all collections
    const collectionsResponse = await collectionsApi.getAll();
    const collections = collectionsResponse.data || [];
    const collectionIds = new Set(collections.map((c: CollectionDebugInfo) => c.id));
    
    console.log(`📊 Found ${collections.length} total collections`);
    
    // Check for orphaned fields
    const orphanedFields = allFields.filter((field: CollectionFieldDebugInfo) => 
      !collectionIds.has(field.collectionId)
    );
    
    if (orphanedFields.length > 0) {
      console.warn(`⚠️ Found ${orphanedFields.length} orphaned fields (collection doesn't exist):`);
      orphanedFields.forEach((field: CollectionFieldDebugInfo) => {
        console.log(`   📝 Field ${field.id} references non-existent collection ${field.collectionId}`);
      });
    } else {
      console.log('✅ No orphaned fields found');
    }
    
    // Group fields by collection
    const fieldsByCollection = allFields.reduce((acc: Record<number, CollectionFieldDebugInfo[]>, field: CollectionFieldDebugInfo) => {
      if (!acc[field.collectionId]) {
        acc[field.collectionId] = [];
      }
      acc[field.collectionId].push(field);
      return acc;
    }, {});
    
    console.log('\n📊 Fields by collection:');
    Object.entries(fieldsByCollection).forEach(([collectionId, fields]) => {
      const collection = collections.find((c: CollectionDebugInfo) => c.id === parseInt(collectionId));
      const collectionName = collection ? collection.collectionName : 'UNKNOWN';
      console.log(`   Collection ${collectionId} (${collectionName}): ${fields.length} fields`);
    });
    
  } catch (error) {
    console.error('❌ Error during debug:', error);
  }
}

/**
 * Run all debug checks
 */
export async function runAllFieldDebugChecks(collectionId?: number): Promise<void> {
  console.log('🚀 Running comprehensive field debug checks...');
  
  try {
    await debugAllCollectionFields();
    await debugFieldCollectionMismatch();
    
    if (collectionId) {
      await debugCollectionFields(collectionId);
    }
    
    console.log('🎉 Debug checks complete!');
  } catch (error) {
    console.error('❌ Error during debug checks:', error);
  }
}
