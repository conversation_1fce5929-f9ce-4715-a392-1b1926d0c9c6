using CMS.WebApi.Models.Entities;

namespace CMS.WebApi.Services.Interfaces;

public interface IFieldTypeService
{
    Task<IEnumerable<FieldType>> GetAllFieldTypesAsync();
    Task<IEnumerable<FieldType>> GetActiveFieldTypesAsync();
    Task<FieldType?> GetFieldTypeByIdAsync(int id);
    Task<FieldType?> GetFieldTypeByNameAsync(string fieldTypeName);
    Task<FieldType> CreateFieldTypeAsync(FieldType fieldType);
    Task<FieldType> UpdateFieldTypeAsync(int id, FieldType fieldType);
    Task DeleteFieldTypeAsync(int id);
    Task<bool> FieldTypeExistsAsync(string fieldTypeName);
}
