import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { Wrench, Eye, Play, CheckCircle, AlertCircle } from 'lucide-react';
import {
  fixAllFieldValidations,
  previewFieldValidationFixes,
  fixCollectionFieldValidations,
  fixComponentFieldValidations
} from '@/utils/fixFieldValidations';
import {
  debugCollectionFields,
  debugAllCollectionFields,
  runAllFieldDebugChecks
} from '@/utils/debugCollectionFields';

export default function FieldValidationFixer() {
  const { toast } = useToast();
  const [isRunning, setIsRunning] = useState(false);
  const [isPreviewing, setIsPreviewing] = useState(false);
  const [isDebugging, setIsDebugging] = useState(false);
  const [lastRunResult, setLastRunResult] = useState<string | null>(null);

  const handlePreview = async () => {
    setIsPreviewing(true);
    setLastRunResult(null);

    try {
      console.log('🔍 Starting preview...');
      await previewFieldValidationFixes();

      toast({
        title: 'Preview Complete',
        description: 'Check the browser console for detailed preview results',
        variant: 'default',
      });

      setLastRunResult('preview');
    } catch (error) {
      console.error('Preview failed:', error);
      toast({
        title: 'Preview Failed',
        description: 'Failed to preview field validation fixes. Check console for details.',
        variant: 'destructive',
      });
    } finally {
      setIsPreviewing(false);
    }
  };

  const handleFixAll = async () => {
    setIsRunning(true);
    setLastRunResult(null);

    try {
      console.log('🚀 Starting comprehensive fix...');
      await fixAllFieldValidations();

      toast({
        title: 'Fix Complete',
        description: 'All field validation objects have been fixed successfully',
        variant: 'default',
      });

      setLastRunResult('success');
    } catch (error) {
      console.error('Fix failed:', error);
      toast({
        title: 'Fix Failed',
        description: 'Failed to fix field validation objects. Check console for details.',
        variant: 'destructive',
      });

      setLastRunResult('error');
    } finally {
      setIsRunning(false);
    }
  };

  const handleFixCollectionFields = async () => {
    setIsRunning(true);
    setLastRunResult(null);

    try {
      console.log('🔧 Starting collection field fix...');
      await fixCollectionFieldValidations();

      toast({
        title: 'Collection Fields Fixed',
        description: 'Collection field validation objects have been fixed successfully',
        variant: 'default',
      });

      setLastRunResult('success');
    } catch (error) {
      console.error('Collection field fix failed:', error);
      toast({
        title: 'Fix Failed',
        description: 'Failed to fix collection field validation objects. Check console for details.',
        variant: 'destructive',
      });

      setLastRunResult('error');
    } finally {
      setIsRunning(false);
    }
  };

  const handleFixComponentFields = async () => {
    setIsRunning(true);
    setLastRunResult(null);

    try {
      console.log('🔧 Starting component field fix...');
      await fixComponentFieldValidations();

      toast({
        title: 'Component Fields Fixed',
        description: 'Component field validation objects have been fixed successfully',
        variant: 'default',
      });

      setLastRunResult('success');
    } catch (error) {
      console.error('Component field fix failed:', error);
      toast({
        title: 'Fix Failed',
        description: 'Failed to fix component field validation objects. Check console for details.',
        variant: 'destructive',
      });

      setLastRunResult('error');
    } finally {
      setIsRunning(false);
    }
  };

  const handleDebugAllFields = async () => {
    setIsDebugging(true);

    try {
      console.log('🔍 Starting comprehensive field debug...');
      await runAllFieldDebugChecks();

      toast({
        title: 'Debug Complete',
        description: 'Check the browser console for detailed debug information',
        variant: 'default',
      });

    } catch (error) {
      console.error('Debug failed:', error);
      toast({
        title: 'Debug Failed',
        description: 'Failed to run field debug checks. Check console for details.',
        variant: 'destructive',
      });
    } finally {
      setIsDebugging(false);
    }
  };

  const handleDebugSpecificCollection = async () => {
    const collectionIdStr = prompt('Enter Collection ID to debug:');
    if (!collectionIdStr) return;

    const collectionId = parseInt(collectionIdStr);
    if (isNaN(collectionId)) {
      toast({
        title: 'Invalid Input',
        description: 'Please enter a valid collection ID number',
        variant: 'destructive',
      });
      return;
    }

    setIsDebugging(true);

    try {
      console.log(`🔍 Starting debug for collection ${collectionId}...`);
      await debugCollectionFields(collectionId);

      toast({
        title: 'Debug Complete',
        description: `Check the browser console for debug information about collection ${collectionId}`,
        variant: 'default',
      });

    } catch (error) {
      console.error('Debug failed:', error);
      toast({
        title: 'Debug Failed',
        description: 'Failed to debug collection. Check console for details.',
        variant: 'destructive',
      });
    } finally {
      setIsDebugging(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex items-center space-x-2">
          <Wrench className="h-6 w-6 text-primary" />
          <CardTitle>Field Validation Fixer</CardTitle>
        </div>
        <CardDescription>
          Fix inconsistent validation objects in field data. This addresses the issue where some fields
          have <code>validations: {}</code> and others have <code>validations: {"{required: false}"}</code>.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Status Badge */}
        {lastRunResult && (
          <div className="flex items-center space-x-2">
            {lastRunResult === 'success' && (
              <>
                <CheckCircle className="h-5 w-5 text-green-500" />
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                  Last run: Successful
                </Badge>
              </>
            )}
            {lastRunResult === 'error' && (
              <>
                <AlertCircle className="h-5 w-5 text-red-500" />
                <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                  Last run: Failed
                </Badge>
              </>
            )}
            {lastRunResult === 'preview' && (
              <>
                <Eye className="h-5 w-5 text-blue-500" />
                <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                  Preview completed
                </Badge>
              </>
            )}
          </div>
        )}

        {/* Problem Description */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h4 className="font-medium text-yellow-800 mb-2">Problem Description</h4>
          <p className="text-sm text-yellow-700 mb-2">
            Some fields in the database have inconsistent validation object structures:
          </p>
          <div className="space-y-1 text-sm font-mono">
            <div className="text-green-700">✅ Correct: <code>{`"validations": {"required": false}`}</code></div>
            <div className="text-red-700">❌ Incorrect: <code>{`"validations": {}`}</code></div>
          </div>
          <p className="text-sm text-yellow-700 mt-2">
            This causes issues when the frontend tries to read field properties and can result in fields
            not appearing in the Field Order dialog.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Preview Button */}
          <Button
            variant="outline"
            onClick={handlePreview}
            disabled={isPreviewing || isRunning || isDebugging}
            className="h-auto p-4 flex flex-col items-start space-y-2"
          >
            <div className="flex items-center space-x-2">
              <Eye className="h-4 w-4" />
              <span className="font-medium">Preview Changes</span>
            </div>
            <span className="text-sm text-muted-foreground text-left">
              See what fields would be fixed without making changes
            </span>
          </Button>

          {/* Fix All Button */}
          <Button
            onClick={handleFixAll}
            disabled={isRunning || isPreviewing || isDebugging}
            className="h-auto p-4 flex flex-col items-start space-y-2 bg-primary hover:bg-primary/90"
          >
            <div className="flex items-center space-x-2">
              <Play className="h-4 w-4" />
              <span className="font-medium">Fix All Fields</span>
            </div>
            <span className="text-sm text-primary-foreground/80 text-left">
              Fix validation objects in both collection and component fields
            </span>
          </Button>

          {/* Fix Collection Fields Only */}
          <Button
            variant="secondary"
            onClick={handleFixCollectionFields}
            disabled={isRunning || isPreviewing || isDebugging}
            className="h-auto p-4 flex flex-col items-start space-y-2"
          >
            <div className="flex items-center space-x-2">
              <Wrench className="h-4 w-4" />
              <span className="font-medium">Fix Collection Fields</span>
            </div>
            <span className="text-sm text-muted-foreground text-left">
              Fix validation objects in collection fields only
            </span>
          </Button>

          {/* Fix Component Fields Only */}
          <Button
            variant="secondary"
            onClick={handleFixComponentFields}
            disabled={isRunning || isPreviewing || isDebugging}
            className="h-auto p-4 flex flex-col items-start space-y-2"
          >
            <div className="flex items-center space-x-2">
              <Wrench className="h-4 w-4" />
              <span className="font-medium">Fix Component Fields</span>
            </div>
            <span className="text-sm text-muted-foreground text-left">
              Fix validation objects in component fields only
            </span>
          </Button>
          {/* Debug All Fields Button */}
          <Button
            variant="outline"
            onClick={handleDebugAllFields}
            disabled={isRunning || isPreviewing || isDebugging}
            className="h-auto p-4 flex flex-col items-start space-y-2 border-blue-200 hover:bg-blue-50"
          >
            <div className="flex items-center space-x-2">
              <Eye className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-600">Debug All Fields</span>
            </div>
            <span className="text-sm text-blue-600/80 text-left">
              Debug all collection fields and check for issues
            </span>
          </Button>

          {/* Debug Specific Collection Button */}
          <Button
            variant="outline"
            onClick={handleDebugSpecificCollection}
            disabled={isRunning || isPreviewing || isDebugging}
            className="h-auto p-4 flex flex-col items-start space-y-2 border-blue-200 hover:bg-blue-50"
          >
            <div className="flex items-center space-x-2">
              <Eye className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-600">Debug Collection</span>
            </div>
            <span className="text-sm text-blue-600/80 text-left">
              Debug fields for a specific collection ID
            </span>
          </Button>
        </div>

        {/* Loading State */}
        {(isRunning || isPreviewing || isDebugging) && (
          <div className="flex items-center justify-center p-4 bg-blue-50 rounded-lg">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mr-3"></div>
            <span className="text-blue-700">
              {isPreviewing ? 'Previewing changes...' :
               isDebugging ? 'Running debug checks...' :
               'Fixing field validation objects...'}
            </span>
          </div>
        )}

        {/* Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-medium text-blue-800 mb-2">Instructions</h4>
          <ol className="text-sm text-blue-700 space-y-1 list-decimal list-inside">
            <li>Click "Preview Changes" to see what fields would be affected</li>
            <li>Check the browser console for detailed information</li>
            <li>Click "Fix All Fields" to apply the fixes to all fields</li>
            <li>Alternatively, use the specific buttons to fix only collection or component fields</li>
          </ol>
        </div>
      </CardContent>
    </Card>
  );
}
