namespace CMS.WebApi.Models.DTOs;

public class SimplifiedComponentDto
{
    public int Id { get; set; }
    public SimplifiedComponentDetailsDto? Component { get; set; }
    public int? DisplayPreference { get; set; }
    public bool? IsRepeatable { get; set; }
    public int? MinRepeatOccurrences { get; set; }
    public int? MaxRepeatOccurrences { get; set; }
    public bool? IsActive { get; set; }
    public string? Name { get; set; }
    public string? DisplayName { get; set; }
    public string? AdditionalInfo { get; set; }
    public string? AdditionalInfoImage { get; set; }
}
