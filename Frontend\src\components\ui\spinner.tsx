import * as React from "react"
import { Loader2 } from "lucide-react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const spinnerVariants = cva(
  "animate-spin",
  {
    variants: {
      size: {
        sm: "h-4 w-4",
        default: "h-6 w-6", 
        lg: "h-8 w-8",
        xl: "h-12 w-12",
      },
      variant: {
        default: "text-primary",
        muted: "text-muted-foreground",
        destructive: "text-destructive",
        success: "text-green-500",
        warning: "text-yellow-500",
      }
    },
    defaultVariants: {
      size: "default",
      variant: "default",
    },
  }
)

export interface SpinnerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof spinnerVariants> {
  text?: string
  centered?: boolean
}

const Spinner = React.forwardRef<HTMLDivElement, SpinnerProps>(
  ({ className, size, variant, text, centered = false, ...props }, ref) => {
    const spinnerContent = (
      <>
        <Loader2 className={cn(spinnerVariants({ size, variant }))} />
        {text && (
          <span className="ml-2 text-sm text-muted-foreground animate-pulse">
            {text}
          </span>
        )}
      </>
    )

    if (centered) {
      return (
        <div
          ref={ref}
          className={cn("flex items-center justify-center p-8", className)}
          {...props}
        >
          <div className="flex items-center">
            {spinnerContent}
          </div>
        </div>
      )
    }

    return (
      <div
        ref={ref}
        className={cn("flex items-center", className)}
        {...props}
      >
        {spinnerContent}
      </div>
    )
  }
)
Spinner.displayName = "Spinner"

// Page-level loading spinner
export const PageSpinner = ({ text = "Loading..." }: { text?: string }) => (
  <div className="flex items-center justify-center min-h-[400px]">
    <div className="flex flex-col items-center space-y-4">
      <div className="relative">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <div className="absolute inset-0 h-12 w-12 animate-ping rounded-full bg-primary/20"></div>
      </div>
      <p className="text-lg font-medium text-muted-foreground animate-pulse">
        {text}
      </p>
    </div>
  </div>
)

// Inline loading spinner for buttons
export const ButtonSpinner = ({ size = "sm" }: { size?: "sm" | "default" }) => (
  <Loader2 className={cn(
    "animate-spin",
    size === "sm" ? "h-4 w-4" : "h-5 w-5"
  )} />
)

// Card loading skeleton with spinner
export const CardSpinner = ({ title, description }: { title?: string; description?: string }) => (
  <div className="flex items-center justify-center p-8 border rounded-lg bg-muted/10">
    <div className="flex flex-col items-center space-y-3">
      <div className="relative">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <div className="absolute inset-0 h-8 w-8 animate-pulse rounded-full bg-primary/10"></div>
      </div>
      {title && (
        <h3 className="text-sm font-medium text-muted-foreground">
          {title}
        </h3>
      )}
      {description && (
        <p className="text-xs text-muted-foreground/80">
          {description}
        </p>
      )}
    </div>
  </div>
)

// Table row loading spinner
export const TableRowSpinner = ({ columns = 3 }: { columns?: number }) => (
  <tr>
    <td colSpan={columns} className="p-8">
      <div className="flex items-center justify-center">
        <Spinner text="Loading data..." />
      </div>
    </td>
  </tr>
)

// Overlay spinner for full-screen loading
export const OverlaySpinner = ({ text = "Loading..." }: { text?: string }) => (
  <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
    <div className="flex flex-col items-center space-y-4 bg-card p-8 rounded-lg shadow-lg border">
      <div className="relative">
        <Loader2 className="h-16 w-16 animate-spin text-primary" />
        <div className="absolute inset-0 h-16 w-16 animate-ping rounded-full bg-primary/20"></div>
      </div>
      <p className="text-xl font-medium text-foreground">
        {text}
      </p>
    </div>
  </div>
)

export { Spinner, spinnerVariants }
