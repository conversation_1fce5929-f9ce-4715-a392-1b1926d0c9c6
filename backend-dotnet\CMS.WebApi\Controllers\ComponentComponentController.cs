using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CMS.WebApi.Models.Responses;
using CMS.WebApi.Models.Requests;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Services.Interfaces;
using CMS.WebApi.Security;

namespace CMS.WebApi.Controllers;

[ApiController]
[Route("api/component-components")]
[Authorize(Policy = AuthorizationPolicies.JwtOrApiToken)]
[Tags("Component Component Management")]
public class ComponentComponentController : ControllerBase
{
    private readonly ILogger<ComponentComponentController> _logger;
    private readonly IComponentComponentService _componentComponentService;

    public ComponentComponentController(
        ILogger<ComponentComponentController> logger,
        IComponentComponentService componentComponentService)
    {
        _logger = logger;
        _componentComponentService = componentComponentService;
    }

    /// <summary>
    /// Get all component components
    /// </summary>
    /// <returns>List of all component components</returns>
    [HttpGet("getAll")]
    [ProducesResponseType(typeof(IEnumerable<ComponentComponent>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    public async Task<ActionResult<IEnumerable<ComponentComponent>>> GetAllComponentComponents()
    {
        try
        {
            _logger.LogInformation("All component components requested");

            var components = await _componentComponentService.GetAllComponentComponentsAsync();

            if (!components.Any())
            {
                return NoContent();
            }

            return Ok(components);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all component components");
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving component components",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get component component by ID
    /// </summary>
    /// <param name="id">Component component ID</param>
    /// <returns>Component component details</returns>
    [HttpGet("getById/{id}")]
    [ProducesResponseType(typeof(ComponentComponent), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ComponentComponent>> GetComponentComponentById(int id)
    {
        try
        {
            _logger.LogInformation("Component component requested for ID: {ComponentId}", id);

            var componentComponent = await _componentComponentService.GetComponentComponentByIdAsync(id);

            if (componentComponent == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Component component with ID {id} not found",
                    Path = Request.Path
                });
            }

            return Ok(componentComponent);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving component component: {ComponentId}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving the component component",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get component components by parent ID
    /// </summary>
    /// <param name="parentId">Parent component ID</param>
    /// <returns>List of child component components</returns>
    [HttpGet("getByParentId/{parentId}")]
    [ProducesResponseType(typeof(IEnumerable<ComponentComponent>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    public async Task<ActionResult<IEnumerable<ComponentComponent>>> GetComponentComponentsByParentId(int parentId)
    {
        try
        {
            _logger.LogInformation("Component components requested for parent ID: {ParentId}", parentId);

            var components = await _componentComponentService.GetComponentComponentsByParentIdAsync(parentId);

            if (!components.Any())
            {
                return NoContent();
            }

            return Ok(components);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving component components for parent ID: {ParentId}", parentId);
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving component components",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Create a new component component
    /// </summary>
    /// <param name="request">Component component details</param>
    /// <returns>Created component component</returns>
    [HttpPost("create")]
    [ProducesResponseType(typeof(ComponentComponent), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ComponentComponent>> CreateComponentComponent([FromBody] CreateComponentComponentRequest request)
    {
        try
        {
            _logger.LogInformation("Component component creation requested: Parent={ParentId}, Child={ChildId}",
                request.ParentComponentId, request.ChildComponentId);

            var createdComponent = await _componentComponentService.CreateComponentComponentAsync(request);

            return CreatedAtAction(
                nameof(GetComponentComponentById),
                new { id = createdComponent.Id },
                createdComponent);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument for component component creation");
            return BadRequest(new ErrorResponse
            {
                Status = 400,
                Error = "Bad Request",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation for component component creation");
            return BadRequest(new ErrorResponse
            {
                Status = 400,
                Error = "Bad Request",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating component component");
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while creating the component component",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Update an existing component component
    /// </summary>
    /// <param name="id">Component component ID</param>
    /// <param name="request">Updated component component details</param>
    /// <returns>Updated component component</returns>
    [HttpPut("update/{id}")]
    [ProducesResponseType(typeof(ComponentComponent), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ComponentComponent>> UpdateComponentComponent(int id, [FromBody] UpdateComponentComponentRequest request)
    {
        try
        {
            _logger.LogInformation("Component component update requested for ID: {ComponentId}", id);

            var updatedComponent = await _componentComponentService.UpdateComponentComponentAsync(id, request);

            return Ok(updatedComponent);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument for component component update: {ComponentId}", id);

            if (ex.Message.Contains("not found"))
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = ex.Message,
                    Path = Request.Path
                });
            }

            return BadRequest(new ErrorResponse
            {
                Status = 400,
                Error = "Bad Request",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation for component component update: {ComponentId}", id);
            return BadRequest(new ErrorResponse
            {
                Status = 400,
                Error = "Bad Request",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating component component: {ComponentId}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while updating the component component",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Delete a component component
    /// </summary>
    /// <param name="id">Component component ID</param>
    /// <returns>No content</returns>
    [HttpDelete("delete/{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult> DeleteComponentComponent(int id)
    {
        try
        {
            _logger.LogInformation("Component component deletion requested for ID: {ComponentId}", id);

            await _componentComponentService.DeleteComponentComponentAsync(id);

            return NoContent();
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Component component not found for deletion: {ComponentId}", id);
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting component component: {ComponentId}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while deleting the component component",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Reorder component components
    /// </summary>
    /// <param name="parentId">Parent component ID</param>
    /// <param name="request">Component IDs in new order</param>
    /// <returns>Updated component components</returns>
    [HttpPost("reorder/{parentId}")]
    [ProducesResponseType(typeof(IEnumerable<ComponentComponent>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<IEnumerable<ComponentComponent>>> ReorderComponentComponents(int parentId, [FromBody] ReorderComponentComponentsRequest request)
    {
        try
        {
            _logger.LogInformation("Component component reordering requested for parent ID: {ParentId}", parentId);

            await _componentComponentService.ReorderComponentComponentsAsync(parentId, request.ComponentIds);

            var reorderedComponents = await _componentComponentService.GetComponentComponentsByParentIdAsync(parentId);

            return Ok(reorderedComponents);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reordering component components for parent ID: {ParentId}", parentId);
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while reordering component components",
                Path = Request.Path
            });
        }
    }
}
