import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface ConfigComponentProps {
  configName: string;
  value: any;
  onChange: (value: any) => void;
}

// Text input component
export const TextConfigComponent: React.FC<ConfigComponentProps> = ({ configName, value, onChange }) => {
  return (
    <div className="mb-4">
      <Label htmlFor={`config-${configName}`}>{configName}</Label>
      <Input
        id={`config-${configName}`}
        placeholder={`Enter ${configName}`}
        className="mt-2"
        value={value || ''}
        onChange={(e) => onChange(e.target.value)}
      />
      <p className="text-xs text-muted-foreground mt-1">
        Text configuration for {configName}
      </p>
    </div>
  );
};

// Number input component
export const NumberConfigComponent: React.FC<ConfigComponentProps> = ({ configName, value, onChange }) => {
  return (
    <div className="mb-4">
      <Label htmlFor={`config-${configName}`}>{configName}</Label>
      <Input
        id={`config-${configName}`}
        type="number"
        placeholder={`Enter ${configName}`}
        className="mt-2"
        value={value || ''}
        onChange={(e) => onChange(parseFloat(e.target.value))}
      />
      <p className="text-xs text-muted-foreground mt-1">
        Numeric configuration for {configName}
      </p>
    </div>
  );
};

// Boolean switch component
export const BooleanConfigComponent: React.FC<ConfigComponentProps> = ({ configName, value, onChange }) => {
  return (
    <div className="flex items-center justify-between mb-4">
      <div>
        <Label htmlFor={`config-${configName}`} className="font-medium">{configName}</Label>
        <p className="text-xs text-muted-foreground">{configName} setting</p>
      </div>
      <Switch
        id={`config-${configName}`}
        checked={value === true}
        onCheckedChange={(checked) => onChange(checked)}
      />
    </div>
  );
};

// URL input component
export const UrlConfigComponent: React.FC<ConfigComponentProps> = ({ configName, value, onChange }) => {
  return (
    <div className="mb-4">
      <Label htmlFor={`config-${configName}`}>{configName}</Label>
      <Input
        id={`config-${configName}`}
        type="url"
        placeholder={`Enter ${configName} URL`}
        className="mt-2"
        value={value || ''}
        onChange={(e) => onChange(e.target.value)}
      />
      <p className="text-xs text-muted-foreground mt-1">
        URL configuration for {configName}
      </p>
    </div>
  );
};

// Regex input component
export const RegexConfigComponent: React.FC<ConfigComponentProps> = ({ configName, value, onChange }) => {
  return (
    <div className="mb-4">
      <Label htmlFor={`config-${configName}`}>{configName}</Label>
      <Input
        id={`config-${configName}`}
        placeholder={`Enter ${configName} pattern`}
        className="mt-2"
        value={value || ''}
        onChange={(e) => onChange(e.target.value)}
      />
      <p className="text-xs text-muted-foreground mt-1">
        Regular expression pattern for {configName}
      </p>
    </div>
  );
};

// Options select component
export const OptionsConfigComponent: React.FC<ConfigComponentProps & { options?: string[] }> = ({ 
  configName, 
  value, 
  onChange,
  options = ['Filled', 'Outlined', 'Text'] // Default options
}) => {
  return (
    <div className="mb-4">
      <Label htmlFor={`config-${configName}`}>{configName}</Label>
      <Select 
        value={value || ''} 
        onValueChange={onChange}
      >
        <SelectTrigger id={`config-${configName}`} className="mt-2">
          <SelectValue placeholder={`Select ${configName}`} />
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem key={option} value={option}>{option}</SelectItem>
          ))}
        </SelectContent>
      </Select>
      <p className="text-xs text-muted-foreground mt-1">
        Select an option for {configName}
      </p>
    </div>
  );
};

// Icon input component
export const IconConfigComponent: React.FC<ConfigComponentProps> = ({ configName, value, onChange }) => {
  return (
    <div className="mb-4">
      <Label htmlFor={`config-${configName}`}>{configName}</Label>
      <Input
        id={`config-${configName}`}
        placeholder={`Enter icon name`}
        className="mt-2"
        value={value || ''}
        onChange={(e) => onChange(e.target.value)}
      />
      <p className="text-xs text-muted-foreground mt-1">
        Icon name for {configName} (e.g., 'search', 'calendar')
      </p>
    </div>
  );
};

// Textarea component
export const TextareaConfigComponent: React.FC<ConfigComponentProps> = ({ configName, value, onChange }) => {
  return (
    <div className="mb-4">
      <Label htmlFor={`config-${configName}`}>{configName}</Label>
      <Textarea
        id={`config-${configName}`}
        placeholder={`Enter ${configName}`}
        className="mt-2"
        value={value || ''}
        onChange={(e) => onChange(e.target.value)}
      />
      <p className="text-xs text-muted-foreground mt-1">
        Multi-line text configuration for {configName}
      </p>
    </div>
  );
};

// Factory function to get the appropriate component based on valueType
export const getConfigComponent = (valueType: string): React.FC<ConfigComponentProps> => {
  switch (valueType) {
    case 'boolean':
      return BooleanConfigComponent;
    case 'number':
      return NumberConfigComponent;
    case 'url':
      return UrlConfigComponent;
    case 'regex':
      return RegexConfigComponent;
    case 'options':
      return OptionsConfigComponent;
    case 'icon':
      return IconConfigComponent;
    case 'textarea':
      return TextareaConfigComponent;
    case 'text':
    default:
      return TextConfigComponent;
  }
};
