using CMS.WebApi.Models.Entities;

namespace CMS.WebApi.Services.Interfaces;

public interface IComponentFieldConfigService
{
    Task<IEnumerable<ComponentFieldConfig>> GetAllComponentFieldConfigsAsync();
    Task<IEnumerable<ComponentFieldConfig>> GetComponentFieldConfigsByComponentFieldIdAsync(int componentFieldId);
    Task<ComponentFieldConfig?> GetComponentFieldConfigByIdAsync(int id);
    Task<ComponentFieldConfig> CreateComponentFieldConfigAsync(ComponentFieldConfig componentFieldConfig);
    Task<IEnumerable<ComponentFieldConfig>> CreateComponentFieldConfigsAsync(IEnumerable<ComponentFieldConfig> componentFieldConfigs);
    Task<ComponentFieldConfig> UpdateComponentFieldConfigAsync(int id, ComponentFieldConfig componentFieldConfig);
    Task DeleteComponentFieldConfigAsync(int id);
}
