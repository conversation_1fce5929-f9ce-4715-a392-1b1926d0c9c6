@import './styles/scrollbar.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 252 59% 48%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 252 59% 96%;
    --accent-foreground: 252 59% 48%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 252 59% 48%;

    --radius: 0.5rem;

    --sidebar-background: 252 59% 48%;
      /* --sidebar-background: linear-gradient(135deg, hsl(252, 59%, 48%), hsl(280, 60%, 60%)); */

    --sidebar-foreground: 0 0% 100%;
    --sidebar-primary: 0 0% 100%;
    --sidebar-primary-foreground: 252 59% 48%;
    --sidebar-accent: 252 30% 56%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 252 30% 56%;
    --sidebar-ring: 0 0% 100%;
  }

  .dark {
    /* Enhanced dark theme with better visibility and contrast */
    --background: 220 13% 9%;
    --foreground: 220 9% 95%;

    --card: 220 13% 11%;
    --card-foreground: 220 9% 95%;

    --popover: 220 13% 11%;
    --popover-foreground: 220 9% 95%;

    --primary: 263 70% 65%;
    --primary-foreground: 220 13% 9%;

    --secondary: 220 13% 16%;
    --secondary-foreground: 220 9% 90%;

    --muted: 220 13% 16%;
    --muted-foreground: 220 9% 70%;

    --accent: 220 13% 18%;
    --accent-foreground: 220 9% 95%;

    --destructive: 0 75% 60%;
    --destructive-foreground: 220 9% 95%;

    --border: 220 13% 20%;
    --input: 220 13% 16%;
    --ring: 263 70% 65%;

    /* Enhanced sidebar colors for better visibility */
    --sidebar-background: 220 13% 8%;
    --sidebar-foreground: 220 9% 95%;
    --sidebar-primary: 263 70% 65%;
    --sidebar-primary-foreground: 220 13% 9%;
    --sidebar-accent: 220 13% 15%;
    --sidebar-accent-foreground: 220 9% 90%;
    --sidebar-border: 220 13% 18%;
    --sidebar-ring: 263 70% 65%;

    /* Additional dark theme variables for enhanced interactivity */
    --hover-overlay: 220 9% 95% / 0.05;
    --active-overlay: 220 9% 95% / 0.1;
    --focus-ring: 263 70% 65% / 0.3;
    --glass-bg: 220 13% 11% / 0.8;
    --glass-border: 220 9% 95% / 0.1;
  }
}

@layer base {
  * {
    @apply border-border;
    /* Ensure crisp text rendering for all elements */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    @apply bg-background text-foreground;
    /* Beautiful gradient background matching the header */
    background: linear-gradient(135deg,
      #f8faff 0%,
      #f0f4ff 20%,
      #e8f0ff 40%,
      #f0f4ff 60%,
      #f8f0ff 80%,
      #faf8ff 100%
    );
    background-attachment: fixed;
    min-height: 100vh;
    transition: background 0.3s ease;
    /* Fix font rendering for crisp text */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    font-feature-settings: "liga", "kern";
  }

  /* Enhanced dark theme body background */
  .dark body {
    background: linear-gradient(135deg,
      hsl(220, 13%, 8%) 0%,
      hsl(220, 13%, 10%) 20%,
      hsl(220, 13%, 9%) 40%,
      hsl(220, 13%, 11%) 60%,
      hsl(220, 13%, 9%) 80%,
      hsl(220, 13%, 8%) 100%
    );
    background-attachment: fixed;
    /* Ensure crisp font rendering in dark mode */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Enhanced Button Animations */
@layer components {
  /* Ripple effect for buttons */
  .button-ripple {
    position: relative;
    overflow: hidden;
  }

  .button-ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
  }

  .button-ripple:active::before {
    width: 300px;
    height: 300px;
  }

  /* Floating animation for scroll-to-top buttons and particles */
  @keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-5px) rotate(1deg); }
    50% { transform: translateY(-10px) rotate(0deg); }
    75% { transform: translateY(-5px) rotate(-1deg); }
  }

  .float-animation {
    animation: float 3s ease-in-out infinite;
  }

  .animate-float {
    animation: float 15s ease-in-out infinite;
  }

  /* Pulse animation for loading states */
  @keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 5px rgba(var(--primary), 0.5); }
    50% { box-shadow: 0 0 20px rgba(var(--primary), 0.8), 0 0 30px rgba(var(--primary), 0.6); }
  }

  .pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  /* Enhanced hover effects for interactive elements */
  .interactive-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .interactive-hover:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }

  /* Gradient border animation */
  @keyframes gradient-border {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  .gradient-border {
    background: linear-gradient(45deg, hsl(var(--primary)), hsl(var(--secondary)), hsl(var(--accent)));
    background-size: 200% 200%;
    animation: gradient-border 3s ease infinite;
  }

  /* Enhanced card backgrounds to work with gradient */
  .card-on-gradient {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  /* Subtle pattern overlay for depth */
  .gradient-pattern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
    pointer-events: none;
  }

  /* Enhanced dark theme card styles */
  .dark .card-on-gradient {
    background: hsl(var(--glass-bg));
    backdrop-filter: blur(16px);
    border: 1px solid hsl(var(--glass-border));
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      0 0 0 1px hsl(var(--border)),
      inset 0 1px 0 hsl(var(--glass-border));
  }

  /* Dark theme interactive elements */
  .dark .interactive-hover:hover {
    background: hsl(var(--hover-overlay));
    box-shadow:
      0 10px 25px rgba(0, 0, 0, 0.4),
      0 0 0 1px hsl(var(--border)),
      0 0 20px hsl(var(--primary) / 0.1);
  }

  /* Enhanced focus states for dark theme */
  .dark *:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
    box-shadow: 0 0 0 4px hsl(var(--focus-ring));
  }

  /* Dark theme glow effects */
  .dark-glow {
    box-shadow:
      0 0 20px hsl(var(--primary) / 0.3),
      0 0 40px hsl(var(--primary) / 0.1);
  }

  /* Enhanced button styles for dark theme */
  .dark .button-ripple::before {
    background: rgba(255, 255, 255, 0.1);
  }

  /* Dark theme scrollbar enhancements */
  .dark ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .dark ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
    border-radius: 4px;
  }

  .dark ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 4px;
    transition: background 0.2s ease;
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }

  /* Dark theme selection styles */
  .dark ::selection {
    background: hsl(var(--primary) / 0.3);
    color: hsl(var(--foreground));
  }

  /* Enhanced dark theme animations */
  @keyframes dark-pulse {
    0%, 100% {
      box-shadow: 0 0 5px hsl(var(--primary) / 0.3);
    }
    50% {
      box-shadow:
        0 0 20px hsl(var(--primary) / 0.5),
        0 0 30px hsl(var(--primary) / 0.3);
    }
  }

  .dark .pulse-glow {
    animation: dark-pulse 2s ease-in-out infinite;
  }

  /* Dark theme glass morphism effects */
  .dark-glass {
    background: hsl(var(--glass-bg));
    backdrop-filter: blur(16px) saturate(180%);
    border: 1px solid hsl(var(--glass-border));
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 hsl(var(--glass-border));
  }

  /* Enhanced dark theme hover states */
  .dark-hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .dark .dark-hover-lift:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.4),
      0 0 0 1px hsl(var(--border)),
      0 0 30px hsl(var(--primary) / 0.2);
  }

  /* Dark theme text glow for important elements */
  .dark-text-glow {
    text-shadow: 0 0 10px hsl(var(--primary) / 0.5);
  }

  /* Dark theme border glow */
  .dark-border-glow {
    border: 1px solid hsl(var(--primary) / 0.3);
    box-shadow:
      0 0 0 1px hsl(var(--primary) / 0.1),
      inset 0 0 0 1px hsl(var(--primary) / 0.1);
  }

  /* Dark theme spinner enhancement */
  .dark-spinner {
    border-color: hsl(var(--primary) / 0.3);
    border-top-color: hsl(var(--primary));
    box-shadow: 0 0 20px hsl(var(--primary) / 0.3);
  }

  /* Enhanced input glow effects */
  .dark-input-glow {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .dark .dark-input-glow:focus {
    box-shadow:
      0 0 0 2px hsl(var(--ring)),
      0 0 20px hsl(var(--primary) / 0.3),
      0 4px 12px rgba(0, 0, 0, 0.15);
  }

  /* Prevent autocomplete from changing input styling */
  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  input:-webkit-autofill:active {
    -webkit-box-shadow: inset 0 0 0 1000px rgba(30, 41, 59, 0.9) !important;
    -webkit-text-fill-color: rgb(241, 245, 249) !important;
    caret-color: rgb(241, 245, 249) !important;
    transition: background-color 5000s ease-in-out 0s !important;
  }

  /* Light mode autocomplete */
  .light input:-webkit-autofill,
  .light input:-webkit-autofill:hover,
  .light input:-webkit-autofill:focus,
  .light input:-webkit-autofill:active {
    -webkit-box-shadow: inset 0 0 0 1000px rgba(255, 255, 255, 0.8) !important;
    -webkit-text-fill-color: rgb(15, 23, 42) !important;
    caret-color: rgb(15, 23, 42) !important;
  }

  /* Shimmer animation for buttons */
  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }

  .shimmer {
    position: relative;
    overflow: hidden;
  }

  .shimmer::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    animation: shimmer 2s infinite;
  }

  /* Particle floating animation */
  @keyframes particle-float {
    0%, 100% {
      transform: translateY(0px) translateX(0px) rotate(0deg);
      opacity: 0.2;
    }
    25% {
      transform: translateY(-20px) translateX(10px) rotate(90deg);
      opacity: 0.4;
    }
    50% {
      transform: translateY(-40px) translateX(-5px) rotate(180deg);
      opacity: 0.6;
    }
    75% {
      transform: translateY(-20px) translateX(-10px) rotate(270deg);
      opacity: 0.4;
    }
  }

  .particle-float {
    animation: particle-float 20s ease-in-out infinite;
  }

  /* Enhanced gradient animations */
  @keyframes gradient-shift {
    0%, 100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  .gradient-shift {
    background-size: 200% 200%;
    animation: gradient-shift 4s ease infinite;
  }
}