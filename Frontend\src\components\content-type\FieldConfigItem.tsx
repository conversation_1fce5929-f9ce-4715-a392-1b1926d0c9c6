import React from 'react';
import { FieldConfig } from '../../types';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface FieldConfigItemProps {
  config: FieldConfig;
  fieldData: Record<string, any>;
  onChange: (configName: string, value: any) => void;
  isLoading?: boolean;
}

const FieldConfigItem: React.FC<FieldConfigItemProps> = ({ config, fieldData, onChange, isLoading = false }) => {
  const { configName, valueType } = config;

  // Format the display name
  const getDisplayName = (name: string) => {
    return name.charAt(0).toUpperCase() + name.slice(1).replace(/-/g, ' ');
  };

  const displayName = getDisplayName(configName);

  // Helper function to get field value
  const getFieldValue = (name: string) => {
    // Check if the value exists in fieldData
    if (fieldData && fieldData[name] !== undefined) {
      console.log(`Found value for ${name}:`, fieldData[name]);
      return fieldData[name];
    }

    // Provide sensible defaults based on valueType
    let defaultValue = '';
    switch (valueType) {
      case 'boolean':
        defaultValue = false;
        break;
      case 'number':
        defaultValue = '';
        break;
      case 'options':
        // For options, return the first option if available
        defaultValue = config.options && config.options.length > 0 ? config.options[0] : '';
        break;
      default:
        defaultValue = '';
    }

    console.log(`Using default value for ${name}:`, defaultValue);
    return defaultValue;
  };

  // Get description based on config name
  const getDescription = () => {
    switch (configName) {
      case 'required':
        return "You won't be able to create an entry if this field is empty";
      case 'unique-id':
      case 'unique':
        return "You won't be able to create an entry if there is an existing entry with identical content";
      case 'regex':
        return "Pattern for validation (e.g., ^[a-zA-Z0-9]+$)";
      case 'icon':
        return "Enter icon name (e.g., pi-user, pi-calendar)";
      case 'minLength':
        return "Minimum number of characters";
      case 'maxLength':
        return "Maximum number of characters";
      case 'minValue':
        return "Minimum value allowed";
      case 'maxValue':
        return "Maximum value allowed";
      case 'minFractionDigits':
        return "Minimum number of decimal places";
      case 'maxFractionDigits':
        return "Maximum number of decimal places";
      case 'api-url':
        return "URL for API validation";
      case 'placeholder':
        return "Text displayed when the field is empty";
      case 'helpText':
        return "Help text displayed below the field";
      case 'FloatLabel':
        return "Label that floats when the field is focused";
      case 'is-visible':
        return "Whether the field is visible in the form";
      case 'disabled':
        return "Whether the field is disabled";
      case 'autoClear':
        return "Whether to clear the field after submission";
      case 'dateFormat':
        return "Format for displaying dates (e.g., MM/dd/yyyy)";
      case 'showIcon':
        return "Whether to show a calendar icon";
      case 'readOnlyInput':
        return "Whether the input is read-only";
      case 'showTime':
        return "Whether to show time selection";
      case 'hourFormat':
        return "12 or 24 hour format";
      default:
        return "";
    }
  };

  // Render based on valueType
  switch (valueType) {
    case 'boolean':
      return (
        <div className="flex items-center justify-between mb-4">
          <div>
            <Label htmlFor={`field-${configName}`} className="font-medium">{displayName}</Label>
            <p className="text-xs text-muted-foreground">
              {getDescription()}
            </p>
          </div>
          <Switch
            id={`field-${configName}`}
            checked={!!getFieldValue(configName)}
            onCheckedChange={(checked) => onChange(configName, checked)}
          />
        </div>
      );

    case 'number':
      return (
        <div className="mb-4">
          <Label htmlFor={`field-${configName}`}>{displayName}</Label>
          <Input
            id={`field-${configName}`}
            type="number"
            value={getFieldValue(configName)}
            onChange={(e) => onChange(configName, e.target.value ? parseFloat(e.target.value) : '')}
            placeholder={`Enter ${displayName.toLowerCase()}`}
            className="mt-2"
          />
          {getDescription() && (
            <p className="text-xs text-muted-foreground mt-1">{getDescription()}</p>
          )}
        </div>
      );

    case 'url':
      return (
        <div className="mb-4">
          <Label htmlFor={`field-${configName}`}>{displayName}</Label>
          <Input
            id={`field-${configName}`}
            type="url"
            value={getFieldValue(configName)}
            onChange={(e) => onChange(configName, e.target.value)}
            placeholder={`Enter ${displayName.toLowerCase()}`}
            className="mt-2"
          />
          {getDescription() && (
            <p className="text-xs text-muted-foreground mt-1">{getDescription()}</p>
          )}
        </div>
      );

    case 'regex':
      return (
        <div className="mb-4">
          <Label htmlFor={`field-${configName}`}>{displayName}</Label>
          <Input
            id={`field-${configName}`}
            type="text"
            value={getFieldValue(configName)}
            onChange={(e) => onChange(configName, e.target.value)}
            placeholder={`Enter ${displayName.toLowerCase()}`}
            className="mt-2"
          />
          {getDescription() && (
            <p className="text-xs text-muted-foreground mt-1">{getDescription()}</p>
          )}
        </div>
      );

    case 'options':
      // For options like variant, we could provide a dropdown
      return (
        <div className="mb-4">
          <Label htmlFor={`field-${configName}`}>{displayName}</Label>
          <Select
            value={getFieldValue(configName) || ''}
            onValueChange={(value) => onChange(configName, value)}
          >
            <SelectTrigger id={`field-${configName}`} className="mt-2">
              <SelectValue placeholder={`Select ${displayName.toLowerCase()}`} />
            </SelectTrigger>
            <SelectContent>
              {configName === 'variant' ? (
                <>
                  <SelectItem value="filled">Filled</SelectItem>
                  <SelectItem value="outlined">Outlined</SelectItem>
                  <SelectItem value="standard">Standard</SelectItem>
                </>
              ) : configName === 'hourFormat' ? (
                <>
                  <SelectItem value="12">12-hour</SelectItem>
                  <SelectItem value="24">24-hour</SelectItem>
                </>
              ) : configName === 'selectionMode' ? (
                <>
                  <SelectItem value="single">Single</SelectItem>
                  <SelectItem value="multiple">Multiple</SelectItem>
                  <SelectItem value="range">Range</SelectItem>
                </>
              ) : configName === 'view' ? (
                <>
                  <SelectItem value="date">Date</SelectItem>
                  <SelectItem value="month">Month</SelectItem>
                  <SelectItem value="year">Year</SelectItem>
                </>
              ) : (
                config.options?.map((option) => (
                  <SelectItem key={option} value={option}>{option}</SelectItem>
                )) || (
                  <SelectItem value="default">Default</SelectItem>
                )
              )}
            </SelectContent>
          </Select>
          {getDescription() && (
            <p className="text-xs text-muted-foreground mt-1">{getDescription()}</p>
          )}
        </div>
      );

    case 'icon':
      // For icon selection, we could provide common icons
      return (
        <div className="mb-4">
          <Label htmlFor={`field-${configName}`}>{displayName}</Label>
          <Input
            id={`field-${configName}`}
            type="text"
            value={getFieldValue(configName)}
            onChange={(e) => onChange(configName, e.target.value)}
            placeholder="Enter icon name (e.g., pi-user)"
            className="mt-2"
          />
          {getDescription() && (
            <p className="text-xs text-muted-foreground mt-1">{getDescription()}</p>
          )}
        </div>
      );

    case 'date':
      return (
        <div className="mb-4">
          <Label htmlFor={`field-${configName}`}>{displayName}</Label>
          <Input
            id={`field-${configName}`}
            type="date"
            value={getFieldValue(configName)}
            onChange={(e) => onChange(configName, e.target.value)}
            className="mt-2"
          />
          {getDescription() && (
            <p className="text-xs text-muted-foreground mt-1">{getDescription()}</p>
          )}
        </div>
      );

    case 'password':
      return (
        <div className="mb-4">
          <Label htmlFor={`field-${configName}`}>{displayName}</Label>
          <Input
            id={`field-${configName}`}
            type="password"
            value={getFieldValue(configName)}
            onChange={(e) => onChange(configName, e.target.value)}
            placeholder={`Enter ${displayName.toLowerCase()}`}
            className="mt-2"
          />
          {getDescription() && (
            <p className="text-xs text-muted-foreground mt-1">{getDescription()}</p>
          )}
        </div>
      );

    case 'code':
      return (
        <div className="mb-4">
          <Label htmlFor={`field-${configName}`}>{displayName}</Label>
          <Textarea
            id={`field-${configName}`}
            value={getFieldValue(configName)}
            onChange={(e) => onChange(configName, e.target.value)}
            placeholder={`Enter ${displayName.toLowerCase()}`}
            className="mt-2 font-mono"
            rows={5}
          />
          {getDescription() && (
            <p className="text-xs text-muted-foreground mt-1">{getDescription()}</p>
          )}
        </div>
      );

    case 'upload':
      return (
        <div className="mb-4">
          <Label htmlFor={`field-${configName}`}>{displayName}</Label>
          <div className="mt-2 border-2 border-dashed border-gray-300 rounded-md p-4 text-center">
            <p className="text-sm text-gray-500">Upload functionality not available in this view</p>
          </div>
          {getDescription() && (
            <p className="text-xs text-muted-foreground mt-1">{getDescription()}</p>
          )}
        </div>
      );

    case 'text':
    default:
      return (
        <div className="mb-4">
          <Label htmlFor={`field-${configName}`}>{displayName}</Label>
          <Input
            id={`field-${configName}`}
            type="text"
            value={getFieldValue(configName)}
            onChange={(e) => onChange(configName, e.target.value)}
            placeholder={`Enter ${displayName.toLowerCase()}`}
            className="mt-2"
            disabled={false}
          />
          {isLoading && (
            <div className="flex items-center mt-1">
              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-primary mr-2"></div>
              <span className="text-xs text-muted-foreground">Saving...</span>
            </div>
          )}
          {getDescription() && (
            <p className="text-xs text-muted-foreground mt-1">{getDescription()}</p>
          )}
        </div>
      );
  }
};

export default FieldConfigItem;
