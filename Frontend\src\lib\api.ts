import axios from "axios";
import { getTenantFromToken } from "./jwt";

// Determine the API base URL
export const getBaseUrl = () => {
  // Check for URL parameters that might override the API URL (highest priority)
  const urlParams = new URLSearchParams(window.location.search);
  const apiUrlParam = urlParams.get("apiUrl");
  if (apiUrlParam) {
    console.log("Using API URL from URL parameter:", apiUrlParam);
    return apiUrlParam;
  }

  // Use environment variable from .env files (second priority)
  if (import.meta.env.VITE_API_URL) {
    // If we're in development mode, log the source
    if (import.meta.env.VITE_DEBUG === "true") {
      console.log(
        "Using API URL from environment:",
        import.meta.env.VITE_API_URL
      );
    }
    return import.meta.env.VITE_API_URL;
  }

  // Dynamic detection based on current hostname (third priority)
  const isLocalhost =
    window.location.hostname === "localhost" ||
    window.location.hostname === "127.0.0.1";

  // Check if accessing via IP address (for development on other devices)
  const isLocalNetwork =
    /^192\.168\.\d+\.\d+$/.test(window.location.hostname) ||
    /^10\.\d+\.\d+\.\d+$/.test(window.location.hostname) ||
    /^172\.(1[6-9]|2\d|3[0-1])\.\d+\.\d+$/.test(window.location.hostname);

  let baseUrl;
  // If running locally or on local network, use the backend server's address
  // Otherwise, assume API is available at the same domain but with /api path
  if (isLocalhost) {
    // Use .NET Web API default ports (5000 for HTTP, 7000 for HTTPS)
    try {
      // Default to .NET Web API port 5000 for HTTP
      baseUrl = "http://localhost:5000/api";
      console.log("Using .NET API URL:", baseUrl);
    } catch (error) {
      console.log("Error connecting to port 5000, falling back to HTTPS port 7000");
      baseUrl = "https://localhost:7000/api";
    }
  } else if (isLocalNetwork) {
    // When accessing via IP, use the same IP for the backend with .NET port
    baseUrl = `http://${window.location.hostname}:5000/api`;
    console.log("Using network .NET API URL:", baseUrl);
  } else {
    baseUrl = `${window.location.origin}/api`;
  }

  // Log the base URL for debugging
  if (import.meta.env.VITE_DEBUG === "true") {
    console.log("API Base URL (dynamically determined):", baseUrl);
  }
  return baseUrl;
};

// Utility function to clear all authentication data and reset axios
export const clearAuthenticationData = () => {
  console.log('Clearing all authentication data and resetting axios');

  // Clear localStorage
  localStorage.removeItem('cms_token');
  localStorage.removeItem('auth-storage');

  // Clear sessionStorage
  sessionStorage.clear();

  // Clear any auth-related keys from localStorage including tenant data
  const keysToRemove = [];
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && (key.includes('auth') || key.includes('token') || key.includes('user') || key.includes('tenant'))) {
      keysToRemove.push(key);
    }
  }
  keysToRemove.forEach(key => localStorage.removeItem(key));

  console.log('Authentication and tenant data cleared');
};

// Create axios instance with default config
const api = axios.create({
  baseURL: getBaseUrl(),
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
    "Cache-Control": "no-cache",
  },
  // Disable withCredentials since we're using JWT tokens in Authorization header
  // This avoids CORS preflight issues with credentials
  withCredentials: false,
  // Add timeout to prevent hanging requests - increased for large responses
  timeout: 60000, // 60 seconds for large data sets
});

// Add request interceptor for debugging
api.interceptors.request.use(
  (config) => {
    console.log(
      `API Request: ${config.method?.toUpperCase()} ${config.baseURL}${
        config.url
      }`,
      {
        params: config.params,
        data: config.data,
        headers: config.headers,
      }
    );
    return config;
  },
  (error) => {
    console.error("API Request Error:", error);
    return Promise.reject(error);
  }
);

// Add response interceptor for debugging
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`, {
      data: response.data,
      headers: response.headers,
    });

    // Handle 204 No Content responses by returning empty array for list endpoints
    if (response.status === 204 && response.config.url?.includes('/getAll')) {
      console.log('Converting 204 No Content to empty array for list endpoint');
      response.data = [];
    }

    return response;
  },
  (error) => {
    console.error(`API Error: ${error.config?.url}`, {
      status: error.response?.status,
      data: error.response?.data,
      message: error.message,
      request: {
        method: error.config?.method,
        params: error.config?.params,
        data: error.config?.data,
      },
    });

    // Handle 204 No Content as success for list endpoints
    if (error.response?.status === 204 && error.config?.url?.includes('/getAll')) {
      console.log('Converting 204 No Content error to empty array for list endpoint');
      return {
        ...error.response,
        data: [],
        status: 200,
        statusText: 'OK'
      };
    }

    return Promise.reject(error);
  }
);

// Add interceptor to attach JWT token and tenant information to requests
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("cms_token");
    console.log("API Request to:", config.url);
    console.log("Token available for request:", token ? "Yes" : "No");

    if (token) {
      // Add Authorization header with JWT token
      config.headers.Authorization = `Bearer ${token}`;
      console.log(
        "Authorization header set:",
        `Bearer ${token.substring(0, 15)}...`
      );

      // Extract tenant from token and add X-TenantID header
      const tenant = getTenantFromToken(token);
      if (tenant && tenant !== "public") {
        config.headers["X-TenantID"] = tenant;
        console.log("X-TenantID header set:", tenant);
      } else {
        console.log("No tenant found in token or using default tenant");
      }
    } else {
      console.log("No token available, request will be unauthenticated");
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor to handle authentication errors
api.interceptors.response.use(
  (response) => {
    console.log("API Response success from:", response.config.url);
    return response;
  },
  (error) => {
    console.log("API Response error from:", error.config?.url);
    console.error("Error details:", error.message);

    // Log response details if available
    if (error.response) {
      console.error("Response status:", error.response.status);
      console.error("Response data:", error.response.data);
    }

    // Handle 401 Unauthorized errors
    if (error.response && error.response.status === 401) {
      console.error("Authentication error - clearing all auth data");

      // Clear all authentication data
      clearAuthenticationData();

      // If not on login page, redirect to login
      if (window.location.pathname !== "/login") {
        console.log("Redirecting to login due to authentication error");
        window.location.href = "/login";
      }
    }
    return Promise.reject(error);
  }
);



// API services
export const authApi = {
  login: (username: string, password: string) =>
    api.post("/auth/login", { username, password }),

  register: (username: string, email: string, password: string) =>
    api.post("/auth/register", { username, email, password }),

  logout: () => api.post("/auth/logout"),
};

export const collectionsApi = {
  getAll: () => api.get("/collections"),
  getDetails: () => api.get("/collections"),
  getById: (id: string) => api.get(`/collections/${id}`),
  getByIdWithDetails: (id: string) => api.get(`/collections/${id}`),
  getByApiId: (apiId: string) => api.get(`/collections/api/${apiId}`),
  create: (data: any) => api.post("/collections", data),
  update: (id: string, data: any) => api.put(`/collections/${id}`, data),
  delete: (id: string) => api.delete(`/collections/${id}`),
  getByCategoryId: (categoryId: string) =>
    api.get(`/collections/category/${categoryId}`),
};


export const clientsApi = {
  getAll: () => api.get('/clients'),
  create: (data: { name: string }) => api.post('/clients', data),
  getById: (id: string) => api.get(`/clients/${id}`),
  update: (id: string, data: { name: string }) => api.put(`/clients/${id}`, data),
  delete: (id: string) => api.delete(`/clients/${id}`)
};


export const componentsApi = {
  getAll: () => api.get("/components"),
  getActive: () => api.get("/components/active"),
  getById: (id: string) => api.get(`/components/${id}`),
  getByIdWithDetails: (id: string) => api.get(`/components/${id}`),
  getByApiId: (apiId: string) => api.get(`/components/api/${apiId}`),
  create: (data: any) => api.post("/components", data),
  update: (id: string, data: any) => api.put(`/components/${id}`, data),
  delete: (id: string) => api.delete(`/components/${id}`),
};

export const componentFieldsApi = {
  getAll: () => api.get("/component-fields/getAll"),
  getById: (id: string) => api.get(`/component-fields/getById/${id}`),
  getByComponentId: (componentId: string, config?: any) =>
    api.get(`/component-fields/getByComponentId/${componentId}`, config),
  create: (data: any) => api.post("/component-fields/create", data),
  createWithConfigs: (data: any) => api.post("/component-fields/create-with-configs", data),
  update: (id: string, data: any) =>
    api.put(`/component-fields/update/${id}`, data),
  updateWithConfigs: (id: string, data: any) =>
    api.put(`/component-fields/update/${id}`, data),
  delete: (id: string) => api.delete(`/component-fields/deleteById/${id}`),
  getNextId: () => api.get("/component-fields/getNextId"),
  reorderFields: (componentId: string, fieldIds: number[]) =>
    api.post(`/component-fields/reorderFields/${componentId}`, { fieldIds }),
};

export const componentFieldConfigsApi = {
  getByComponentFieldId: (componentFieldId: string) =>
    api.get(`/component-field-configs/getByFieldId/${componentFieldId}`),
  create: (data: any) => api.post("/component-field-configs/create", data),
  update: (id: string, data: any) =>
    api.put(`/component-field-configs/update/${id}`, data),
  delete: (id: string) =>
    api.delete(`/component-field-configs/deleteById/${id}`),
  bulkCreate: (data: any[]) =>
    api.post("/component-field-configs/createBulk", data),
};

export const fieldTypesApi = {
  getAll: () => api.get("/field-types"),
  getActive: () => api.get("/field-types/active"),
  getById: (id: string) => api.get(`/field-types/${id}`),
  create: (data: any) => api.post("/field-types", data),
  update: (id: string, data: any) => api.put(`/field-types/${id}`, data),
  delete: (id: string) => api.delete(`/field-types/${id}`),
  getActiveWithRetry: () =>
    api.get("/field-types/active", {
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        "Cache-Control": "no-cache",
      },
    }),
};

export const fieldConfigsApi = {
  getAll: () => api.get("/field-configs"),
  getActive: () => api.get("/field-configs/active"),
  getByFieldType: (fieldTypeId: string) =>
    api.get(`/field-configs/by-field-type/${fieldTypeId}`),
  getById: (id: string) => api.get(`/field-configs/${id}`),
  create: (data: any) => api.post("/field-configs", data),
  update: (id: string, data: any) => api.put(`/field-configs/${id}`, data),
  delete: (id: string) => api.delete(`/field-configs/${id}`),
};

export const configTypesApi = {
  getAll: () => api.get("/config-types"),
  getActive: () => api.get("/config-types/active"),
  getById: (id: string) => api.get(`/config-types/${id}`),
  create: (data: any) => api.post("/config-types", data),
  update: (id: string, data: any) => api.put(`/config-types/${id}`, data),
  delete: (id: string) => api.delete(`/config-types/${id}`),
};


export const parentCategoriesApi = {
  getByClientId: (id: string) => api.get(`/categories/client/${id}`),
  getById: (id: string) => api.get(`/categories/${id}`),
  create: (data: any) => api.post("/categories", data),
  update: (id: string, data: any) => api.put(`/categories/${id}`, data),
  delete: (id: string) => api.delete(`/categories/${id}`),
}

export const collectionFieldsApi = {
  getAll: () => api.get("/collection-fields/getAll"),
  getById: (id: string) => api.get(`/collection-fields/getById/${id}`),
  getByCollectionId: (collectionId: string) =>
    api.get(`/collection-fields/getByCollectionId/${collectionId}`),
  create: (data: any) => api.post("/collection-fields/create", data),
  createWithConfigs: (data: any) => api.post("/collection-fields/create-with-configs", data),
  update: (id: string, data: any) =>
    api.put(`/collection-fields/update/${id}`, data),
  updateDisplayPreference: (id: string, displayPreference: number) =>
    api.put(`/collection-fields/updateDisplayPreference/${id}`, { displayPreference }),
  delete: (id: string) => api.delete(`/collection-fields/deleteById/${id}`),
  getNextId: () => api.get("/collection-fields/getNextId"),
};

export const collectionFieldConfigsApi = {
  getByCollectionFieldId: (collectionFieldId: string) =>
    api.get(`/collection-field-configs/getByFieldId/${collectionFieldId}`),
  create: (data: any) => api.post("/collection-field-configs/create", data),
  update: (id: string, data: any) =>
    api.put(`/collection-field-configs/update/${id}`, data),
  delete: (id: string) =>
    api.delete(`/collection-field-configs/deleteById/${id}`),
  bulkCreate: (data: any[]) =>
    api.post("/collection-field-configs/createBulk", data),
};

export const collectionComponentsApi = {
  getAll: () => api.get("/collection-components/getAll"),
  getById: (id: string) => api.get(`/collection-components/getById/${id}`),
  getByCollectionId: (collectionId: string) =>
    api.get(`/collection-components/getByCollectionId/${collectionId}`),
  create: (data: any) => api.post("/collection-components/create", data),
  update: (id: string, data: any) =>
    api.put(`/collection-components/update/${id}`, data),
  updateDisplayPreference: (id: string, displayPreference: number) =>
    api.put(`/collection-components/updateDisplayPreference/${id}`, { displayPreference }),
  delete: (id: string) => api.delete(`/collection-components/deleteById/${id}`),
  getNextId: () => api.get("/collection-components/getNextId"),
};

export const componentComponentsApi = {
  getAll: () => api.get("/component-components/getAll"),
  getById: (id: string) => api.get(`/component-components/getById/${id}`),
  getByParentId: (parentId: string) =>
    api.get(`/component-components/getByParentId/${parentId}`),
  create: (data: any) => api.post("/component-components/create", data),
  update: (id: string, data: any) =>
    api.put(`/component-components/update/${id}`, data),
  delete: (id: string) => api.delete(`/component-components/delete/${id}`),
  reorder: (parentId: string, componentIds: number[]) =>
    api.post(`/component-components/reorder/${parentId}`, componentIds),
};

export const collectionOrderingApi = {
  getOrderedItems: (collectionId: string) =>
    api.get(`/collections/${collectionId}/ordering`),
  reorderItems: (
    collectionId: string,
    data: { componentIds: number[]; fieldIds: number[] }
  ) => api.put(`/collections/${collectionId}/ordering`, data),
};

export const contentEntriesApi = {
  getAll: () => api.get("/content-entries"),
  getById: (id: string) => api.get(`/content-entries/${id}`),
  getByCollection: (collectionId: string) =>
    api.get(`/content-entries/collection/${collectionId}`),
  create: (data: any) => api.post("/content-entries", data),
  update: (id: string, data: any) => api.put(`/content-entries/${id}`, data),
  delete: (id: string) => api.delete(`/content-entries/${id}`),
  search: (query: string, collectionId?: string) => {
    const params = new URLSearchParams({ query });
    if (collectionId) params.append('collectionId', collectionId);
    return api.get(`/content-entries/search?${params.toString()}`);
  },
};

export const categoriesApi = {
  getAll: () => api.get("/categories"),
  getById: (id: string) => api.get(`/categories/${id}`),
  getByName: (name: string) => api.get(`/categories/name/${name}`),
  create: (data: any) => api.post("/categories", data),
  update: (id: string, data: any) => api.put(`/categories/${id}`, data),
  delete: (id: string) => api.delete(`/categories/${id}`),
  getByClientId: (id: string) => api.get(`/categories/client/${id}`),
  getByParentCategoryId: (id: string) => api.get(`/categories/parent/${id}`),
  getHierarchical: () => api.get("/categories/hierarchical"),
};

export const publicApi = {
  getCollections: () => api.get("/public/collections"),
  getCollectionById: (id: string) => api.get(`/public/collections/${id}`),
  getCollectionByApiId: (apiId: string) => api.get(`/public/collections/api/${apiId}`),
  getContentEntriesByCollection: (collectionId: string) =>
    api.get(`/public/collections/${collectionId}/entries`),
  getContentEntryById: (id: string) => api.get(`/public/content-entries/${id}`),
  searchContentEntries: (query: string, collectionId?: string) => {
    const params = new URLSearchParams({ query });
    if (collectionId) params.append('collectionId', collectionId);
    return api.get(`/public/search?${params.toString()}`);
  },
  getSimplifiedCollections: () => api.get("/public/simplified-collections"),
};

export const simplifiedCollectionsApi = {
  getAll: () => api.get("/simplified-collections"),
  getById: (id: string) => api.get(`/simplified-collections/${id}`),
  getByApiId: (apiId: string) => api.get(`/simplified-collections/api/${apiId}`),
  getActive: () => api.get("/simplified-collections/active"),
};

// Media API endpoints
export const mediaApi = {
  // Assets
  getAllAssets: () => api.get("/media/assets"),
  getAssetById: (id: string) => api.get(`/media/assets/${id}`),
  getAssetsByFolder: (folderId: string) => api.get(`/media/assets/folder/${folderId}`),
  uploadAsset: (formData: FormData) =>
    api.post("/media/upload", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }),
  updateAsset: (id: string, data: any) => api.put(`/media/assets/${id}`, data),
  renameAsset: (id: string, newFileName: string) =>
    api.put(`/media/assets/${id}/rename`, { fileName: newFileName }),
  deleteAsset: (id: string) => api.delete(`/media/assets/${id}`),
  searchAssets: (query: string) =>
    api.get(`/media/assets/search?query=${encodeURIComponent(query)}`),
  generateShareLink: (id: string) => api.post(`/media/assets/${id}/share`),
  replaceFile: (id: string, formData: FormData) =>
    api.post(`/media/assets/${id}/replace`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }),
  getAssetContent: (id: string) =>
    api.get(`/media/assets/${id}/content`, {
      responseType: "blob",
    }),
  downloadAsset: (id: string) =>
    api.get(`/media/assets/${id}/download`, {
      responseType: "blob",
    }),
  downloadFile: (year: string, month: string, fileName: string) =>
    api.get(`/media/download/${year}/${month}/${fileName}`, {
      responseType: "blob",
    }),

  // Folders
  getAllFolders: () => api.get("/media-folders"),
  getFolderById: (id: string) => api.get(`/media-folders/${id}`),
  getSubfolders: (parentId: number) =>
    api.get(`/media-folders/by-parent/${parentId}`),
  getRootFolders: () => api.get("/media-folders/by-parent"),
  createFolder: (data: {
    folderName: string;
    description?: string;
    parentId?: number;
  }) => api.post("/media-folders", data),
  updateFolder: (id: string, data: any) => api.put(`/media-folders/${id}`, data),
  deleteFolder: (id: string) => api.delete(`/media-folders/${id}`),
  searchFolders: (query: string) =>
    api.get(`/media-folders/search?query=${encodeURIComponent(query)}`),
};

// API Tokens API
export const apiTokensApi = {
  getAll: () => api.get("/api-tokens"),
  getById: (id: string) => api.get(`/api-tokens/${id}`),
  create: (data: { name: string; description?: string; expiresAt: string }) =>
    api.post("/api-tokens", data),
  update: (id: string, data: { name: string; description?: string; isActive: boolean }) =>
    api.put(`/api-tokens/${id}`, data),
  delete: (id: string) => api.delete(`/api-tokens/${id}`),
  revoke: (id: string) => api.post(`/api-tokens/${id}/revoke`),
};

// Users API
export const usersApi = {
  getAll: () => api.get("/users"),
  getProfile: () => api.get("/users/profile"),
  updateProfile: (data: any) => api.put("/users/profile", data),
  changePassword: (data: { currentPassword: string; newPassword: string }) =>
    api.post("/users/change-password", data),
};

export default api;
