import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Plus, ArrowLeft, Search, ChevronLeft, ChevronRight } from 'lucide-react';
import { categoriesApi, clientsApi, collectionsApi } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import BasicCollectionDialog from '@/components/content-type/BasicCollectionDialog';

interface Client {
  id: number;
  name: string;
}

interface Collection {
  id: string;
  collectionName: string;
  apiId: string;
  fields: any[];
}

export default function ClientCollections() {
  const navigate = useNavigate();
  const { clientId } = useParams<{ clientId: string }>();
  const { toast } = useToast();

  const [client, setClient] = useState<Client | null>(null);
  const [collections, setCollections] = useState<Collection[]>([]);
  const [filteredCollections, setFilteredCollections] = useState<Collection[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [createCollectionDialogOpen, setCreateCollectionDialogOpen] = useState(false);

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const itemsPerPage = 10;

  useEffect(() => {
    const fetchData = async () => {
      if (!clientId) return;

      setLoading(true);
      try {
        // Fetch client details
        const clientResponse = await clientsApi.getById(clientId);
        setClient(clientResponse.data);

        // Fetch collections for this client
        // Note: This is a placeholder. You'll need to implement the actual API endpoint
        // that filters collections by clientId
        const collectionsResponse = await categoriesApi.getByClientId(clientId);

        // Filter collections by clientId (this would ideally be done on the server)
        // This is just a placeholder implementation
        const clientCollections = collectionsResponse.data.filter(
          (collection: any) => collection.clientId === clientId
        );

        setCollections(clientCollections);
        setFilteredCollections(clientCollections);
        setTotalPages(Math.ceil(clientCollections.length / itemsPerPage));
      } catch (error) {
        console.error('Error fetching data:', error);
        // Removed error toast notification
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [clientId, toast, itemsPerPage]);

  // Handle search
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredCollections(collections);
    } else {
      const filtered = collections.filter(collection =>
        collection.collectionName.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredCollections(filtered);
    }
    setCurrentPage(1);
    setTotalPages(Math.ceil(filteredCollections.length / itemsPerPage));
  }, [searchQuery, collections]);

  const handleEditCollection = (collectionId: string) => {
    navigate(`/content-types/edit/${collectionId}`);
  };

  const handleCreateCollection = () => {
    setCreateCollectionDialogOpen(true);
  };

  const handleCloseCollectionDialog = () => {
    setCreateCollectionDialogOpen(false);
  };

  const handleSaveCollection = async (collectionData: any) => {
    try {
      // Add clientId to the collection data
      collectionData.clientId = clientId;

      const response = await collectionsApi.create(collectionData);
      console.log('Collection created successfully:', response.data);

      toast({
        title: 'Collection created',
        description: 'Your collection has been created successfully',
      });

      // Close the dialog
      setCreateCollectionDialogOpen(false);

      // Navigate to the edit page
      navigate(`/content-types/edit/${response.data.id}`);
    } catch (apiError: any) {
      console.error('API error creating collection:', apiError);
      // Removed error toast notification
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const getCurrentPageItems = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredCollections.slice(startIndex, endIndex);
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate('/clients')}
              className="mr-2 dark-hover-lift dark-border-glow transition-all duration-300 hover:shadow-md hover:shadow-purple-500/25 dark:hover:shadow-purple-500/40 hover:border-purple-300/50 dark:hover:border-purple-400/50"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>

            <h1 className="text-3xl font-bold dark-text-glow bg-gradient-to-r from-purple-600 to-pink-600 dark:from-purple-400 dark:to-pink-400 bg-clip-text text-transparent">
              {loading ? 'Loading...' : `Collections in ${client?.name || 'Client'}`}
            </h1>
          </div>
          <div>
            <Button onClick={handleCreateCollection} className="button-ripple dark-hover-lift dark-border-glow transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/25 dark:hover:shadow-purple-500/40">
              <Plus className="mr-2 h-4 w-4" />
              Create Collection
            </Button>
          </div>
        </div>

        <div className="relative max-w-sm">
          <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground dark:text-purple-400" />
          <Input
            placeholder="Search collections..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 dark-glass dark:border-purple-500/30 transition-all duration-300 focus:border-purple-500/50 dark:focus:border-purple-400/50"
          />
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center p-8">
          <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full dark-spinner"></div>
        </div>
      ) : filteredCollections.length === 0 ? (
        <div className="flex flex-col items-center justify-center p-12 bg-gradient-to-br from-gray-50/50 to-purple-50/30 dark:from-slate-800/50 dark:to-purple-900/20 border border-dashed border-border dark:border-purple-500/30 rounded-lg dark-glass transition-all duration-300 hover:border-purple-500/50 dark:hover:border-purple-400/50 hover:shadow-lg hover:shadow-purple-500/25 dark:hover:shadow-purple-500/40">
          <div className="w-16 h-16 rounded-full bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900/50 dark:to-pink-900/50 flex items-center justify-center mb-6 border-2 border-purple-200/50 dark:border-purple-500/30 shadow-lg dark:shadow-purple-500/20">
            <Plus className="h-8 w-8 text-purple-600 dark:text-purple-400 transition-colors duration-300" />
          </div>
          <h3 className="text-xl font-semibold mb-3 dark-text-glow text-gray-900 dark:text-gray-100">
            {searchQuery ? 'No collections found' : 'No collections yet'}
          </h3>
          <p className="text-sm text-muted-foreground mb-6 max-w-md text-center leading-relaxed">
            {searchQuery
              ? `No collections match your search query "${searchQuery}" for ${client?.name || 'this client'}.`
              : `${client?.name || 'This client'} doesn't have any collections yet. Create your first collection to start building your API.`
            }
          </p>
          <div className="flex flex-col sm:flex-row gap-3">
            {searchQuery ? (
              <Button
                variant="outline"
                onClick={() => setSearchQuery('')}
                className="dark-hover-lift dark-border-glow transition-all duration-300 hover:shadow-md hover:shadow-purple-500/25 dark:hover:shadow-purple-500/40"
              >
                Clear search
              </Button>
            ) : (
              <Button
                onClick={handleCreateCollection}
                className="button-ripple dark-hover-lift dark-border-glow transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/25 dark:hover:shadow-purple-500/40 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0"
              >
                <Plus className="mr-2 h-4 w-4" />
                Create First Collection
              </Button>
            )}
          </div>
        </div>
      ) : (
        <>
        <div className="border rounded-md overflow-hidden dark:border-purple-500/20 dark-glass bg-card dark:bg-card">
          {getCurrentPageItems().map((collection, index) => (
            <div
              key={collection.id}
              className={`group relative overflow-hidden p-4 bg-card dark:bg-card ${index !== collections.length - 1 ? 'border-b dark:border-purple-500/20' : ''} hover:bg-gradient-to-r hover:from-purple-50/50 hover:to-pink-50/50 dark:hover:from-purple-900/20 dark:hover:to-pink-900/20 cursor-pointer transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/25 dark:hover:shadow-purple-500/40 hover:-translate-y-1 hover:scale-105 border-2 hover:border-purple-300/50 dark:hover:border-purple-400/50 dark-hover-lift`}
              onClick={() => handleEditCollection(collection.id)}
            >
              {/* Shimmer effect */}
              <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 bg-gradient-to-r from-transparent via-white/10 dark:via-purple-400/10 to-transparent"></div>

              <div className="flex items-center justify-between relative z-10">
                <div className="flex items-center">
                  <div className="w-8 h-8 rounded-full bg-primary dark:bg-purple-500/80 text-primary-foreground flex items-center justify-center mr-3 border border-border dark:border-purple-400/50 group-hover:scale-110 group-hover:rotate-12 transition-all duration-300 group-hover:shadow-xl shadow-lg">
                    <span className="group-hover:animate-pulse">{(collection.collectionName || 'U')[0].toUpperCase()}</span>
                  </div>
                  <div>
                    <h3 className="font-medium group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors duration-300 group-hover:scale-105 transform origin-left">{collection.collectionName}</h3>
                    <div className="flex items-center gap-1 mt-1">
                      <span className="text-xs text-muted-foreground">API ID:</span>
                      <span className="text-xs font-mono font-semibold text-foreground bg-muted/40 dark:bg-muted/30 px-2 py-1 rounded border dark:border-purple-500/30 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors duration-300">
                        {collection.apiId}
                      </span>
                    </div>

                    {/* Animated dots */}
                    <div className="flex space-x-1 mt-1 opacity-0 group-hover:opacity-100 transition-opacity duration-500 delay-200">
                      <div className="w-1 h-1 bg-blue-400 rounded-full animate-bounce"></div>
                      <div className="w-1 h-1 bg-purple-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                      <div className="w-1 h-1 bg-pink-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="bg-primary/10 dark:bg-purple-500/20 text-primary dark:text-purple-400 text-xs font-medium px-2 py-1 rounded-full group-hover:bg-purple-100 dark:group-hover:bg-purple-500/30 group-hover:text-purple-600 dark:group-hover:text-purple-300 transition-colors duration-300">
                    {collection.fields?.length || 0} fields
                  </div>
                </div>
              </div>

              {/* Corner accent */}
              <div className="absolute top-0 right-0 w-0 h-0 border-l-[20px] border-l-transparent border-t-[20px] border-t-purple-500/20 dark:border-t-purple-400/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
          ))}
        </div>

        {totalPages > 1 && (
          <div className="flex items-center justify-between mt-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">
                Page {currentPage} of {totalPages}
              </span>
              <div className="flex items-center space-x-1">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="dark-hover-lift transition-all duration-300"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="dark-hover-lift transition-all duration-300"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        )}
        </>
      )}

      {/* Create collection dialog */}
<BasicCollectionDialog
  isOpen={createCollectionDialogOpen}
  onClose={handleCloseCollectionDialog}
  onSave={handleSaveCollection}
  clientId={clientId}
/>
    </div>
  );
}

