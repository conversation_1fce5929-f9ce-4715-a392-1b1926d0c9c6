using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.Responses;
using CMS.WebApi.Models.Requests;
using CMS.WebApi.Services.Interfaces;
using CMS.WebApi.Security;

namespace CMS.WebApi.Controllers;

[ApiController]
[Route("api/collections")]
[Authorize(Policy = AuthorizationPolicies.JwtOrApiToken)]
[Tags("Collection Management")]
public class CollectionController : ControllerBase
{
    private readonly ICollectionService _collectionService;
    private readonly ILogger<CollectionController> _logger;

    public CollectionController(ICollectionService collectionService, ILogger<CollectionController> logger)
    {
        _collectionService = collectionService;
        _logger = logger;
    }

    /// <summary>
    /// Get all collections
    /// </summary>
    /// <returns>List of collections</returns>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<CollectionListing>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<CollectionListing>>> GetAllCollections()
    {
        var collections = await _collectionService.GetAllCollectionsAsync();
        return Ok(collections);
    }

    /// <summary>
    /// Get all collections with details
    /// </summary>
    /// <returns>List of collections with full details</returns>
    [HttpGet("with-details")]
    [ProducesResponseType(typeof(IEnumerable<CollectionListing>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<CollectionListing>>> GetAllCollectionsWithDetails()
    {
        var collections = await _collectionService.GetAllCollectionsAsync();
        return Ok(collections);
    }

    /// <summary>
    /// Get collection by ID
    /// </summary>
    /// <param name="id">Collection ID</param>
    /// <returns>Collection details</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(CollectionListing), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<CollectionListing>> GetCollectionById(int id)
    {
        var collection = await _collectionService.GetCollectionByIdAsync(id);
        if (collection == null)
        {
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = $"Collection with ID {id} not found",
                Path = Request.Path
            });
        }
        return Ok(collection);
    }

    /// <summary>
    /// Get collection by API ID
    /// </summary>
    /// <param name="apiId">Collection API ID</param>
    /// <returns>Collection details</returns>
    [HttpGet("api-id/{apiId}")]
    [ProducesResponseType(typeof(CollectionListing), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<CollectionListing>> GetCollectionByApiId(string apiId)
    {
        var collection = await _collectionService.GetCollectionByApiIdAsync(apiId);
        if (collection == null)
        {
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = $"Collection with API ID '{apiId}' not found",
                Path = Request.Path
            });
        }
        return Ok(collection);
    }

    /// <summary>
    /// Get collections by category
    /// </summary>
    /// <param name="categoryId">Category ID</param>
    /// <returns>List of collections in the category</returns>
    [HttpGet("category/{categoryId}")]
    [ProducesResponseType(typeof(IEnumerable<CollectionListing>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<CollectionListing>>> GetCollectionsByCategory(int categoryId)
    {
        var collections = await _collectionService.GetCollectionsByCategoryAsync(categoryId);
        return Ok(collections);
    }

    /// <summary>
    /// Create a new collection
    /// </summary>
    /// <param name="request">Collection details</param>
    /// <returns>Created collection</returns>
    [HttpPost]
    [ProducesResponseType(typeof(CollectionListing), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<CollectionListing>> CreateCollection([FromBody] CollectionCreateRequest request)
    {
        try
        {
            // Validate that we have a category ID
            if (request.ActualCategoryId <= 0)
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = "Category ID is required",
                    Path = Request.Path
                });
            }

            // Map request DTO to entity
            var collection = new CollectionListing
            {
                CollectionName = request.CollectionName,
                CollectionDesc = request.CollectionDesc,
                AdditionalInformation = request.AdditionalInformation,
                DisclaimerText = request.DisclaimerText,
                CollectionApiId = request.CollectionApiId,
                CategoryId = request.ActualCategoryId
            };

            // Check if collection with same API ID already exists
            if (await _collectionService.CollectionExistsAsync(collection.CollectionApiId))
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = $"Collection with API ID '{collection.CollectionApiId}' already exists",
                    Path = Request.Path
                });
            }

            var createdCollection = await _collectionService.CreateCollectionAsync(collection);
            _logger.LogInformation("Collection created successfully: {CollectionName}", createdCollection.CollectionName);

            return CreatedAtAction(nameof(GetCollectionById), new { id = createdCollection.Id }, createdCollection);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create collection: {CollectionName}", request.CollectionName);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while creating the collection",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Update an existing collection
    /// </summary>
    /// <param name="id">Collection ID</param>
    /// <param name="collection">Updated collection details</param>
    /// <returns>Updated collection</returns>
    [HttpPut("{id}")]
    [ProducesResponseType(typeof(CollectionListing), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<CollectionListing>> UpdateCollection(int id, [FromBody] CollectionListing collection)
    {
        try
        {
            _logger.LogInformation("Updating collection {CollectionId} with data: {@Collection}", id, collection);

            // Validate required fields
            if (string.IsNullOrWhiteSpace(collection.CollectionName))
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = "Collection name is required",
                    Path = Request.Path
                });
            }

            if (string.IsNullOrWhiteSpace(collection.CollectionApiId))
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = "Collection API ID is required",
                    Path = Request.Path
                });
            }

            if (collection.CategoryId <= 0)
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = "Valid Category ID is required",
                    Path = Request.Path
                });
            }

            var updatedCollection = await _collectionService.UpdateCollectionAsync(id, collection);
            _logger.LogInformation("Collection updated successfully: {CollectionId}", id);
            return Ok(updatedCollection);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Collection not found: {CollectionId}", id);
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update collection: {CollectionId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = $"An error occurred while updating the collection: {ex.Message}",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Delete a collection
    /// </summary>
    /// <param name="id">Collection ID</param>
    /// <returns>No content</returns>
    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult> DeleteCollection(int id)
    {
        try
        {
            var collection = await _collectionService.GetCollectionByIdAsync(id);
            if (collection == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Collection with ID {id} not found",
                    Path = Request.Path
                });
            }

            await _collectionService.DeleteCollectionAsync(id);
            _logger.LogInformation("Collection deleted successfully: {CollectionId}", id);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete collection: {CollectionId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while deleting the collection",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get collection fields
    /// </summary>
    /// <param name="id">Collection ID</param>
    /// <returns>List of collection fields</returns>
    [HttpGet("{id}/fields")]
    [ProducesResponseType(typeof(IEnumerable<CollectionField>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<CollectionField>>> GetCollectionFields(int id)
    {
        var fields = await _collectionService.GetCollectionFieldsAsync(id);
        return Ok(fields);
    }

    /// <summary>
    /// Add field to collection
    /// </summary>
    /// <param name="id">Collection ID</param>
    /// <param name="field">Field details</param>
    /// <returns>Added field</returns>
    [HttpPost("{id}/fields")]
    [ProducesResponseType(typeof(CollectionField), StatusCodes.Status201Created)]
    public async Task<ActionResult<CollectionField>> AddFieldToCollection(int id, [FromBody] CollectionField field)
    {
        try
        {
            var addedField = await _collectionService.AddFieldToCollectionAsync(id, field);
            return CreatedAtAction(nameof(GetCollectionFields), new { id }, addedField);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add field to collection: {CollectionId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while adding the field",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Remove field from collection
    /// </summary>
    /// <param name="id">Collection ID</param>
    /// <param name="fieldId">Field ID</param>
    /// <returns>No content</returns>
    [HttpDelete("{id}/fields/{fieldId}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    public async Task<ActionResult> RemoveFieldFromCollection(int id, int fieldId)
    {
        try
        {
            await _collectionService.RemoveFieldFromCollectionAsync(id, fieldId);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to remove field from collection: {CollectionId}, FieldId: {FieldId}", id, fieldId);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while removing the field",
                Path = Request.Path
            });
        }
    }
}
