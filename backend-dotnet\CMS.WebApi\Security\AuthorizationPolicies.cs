using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Authentication.JwtBearer;

namespace CMS.WebApi.Security;

public static class AuthorizationPolicies
{
    public const string JwtOrApiToken = "JwtOrApiToken";
    public const string JwtOnly = "JwtOnly";
    public const string ApiTokenOnly = "ApiTokenOnly";

    public static void AddCustomPolicies(this IServiceCollection services)
    {
        services.AddAuthorization(options =>
        {
            // Policy that accepts either JWT or API token authentication
            options.AddPolicy(JwtOrApiToken, policy =>
            {
                policy.AuthenticationSchemes.Add(JwtBearerDefaults.AuthenticationScheme);
                policy.AuthenticationSchemes.Add(ApiTokenAuthenticationOptions.DefaultScheme);
                policy.RequireAuthenticatedUser();
            });

            // Policy that only accepts JWT authentication
            options.AddPolicy(JwtOnly, policy =>
            {
                policy.AuthenticationSchemes.Add(JwtBearerDefaults.AuthenticationScheme);
                policy.RequireAuthenticatedUser();
            });

            // Policy that only accepts API token authentication
            options.AddPolicy(ApiTokenOnly, policy =>
            {
                policy.AuthenticationSchemes.Add(ApiTokenAuthenticationOptions.DefaultScheme);
                policy.RequireAuthenticatedUser();
            });

            // Default policy uses JWT or API token
            options.DefaultPolicy = options.GetPolicy(JwtOrApiToken)!;
        });
    }
}
