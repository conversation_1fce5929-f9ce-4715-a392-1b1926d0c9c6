# Database Setup Scripts

## DatabaseSetup.sql

This is a comprehensive SQL script that creates the entire R-CMS database schema and inserts initial data.

### Usage

#### Option 1: Direct PostgreSQL execution
```bash
psql -h localhost -p 5432 -U postgres -d content_new -f DatabaseSetup.sql
```

#### Option 2: Using pgAdmin
1. Open pgAdmin
2. Connect to your PostgreSQL server
3. Select the `content_new` database
4. Open Query Tool
5. Load and execute `DatabaseSetup.sql`

#### Option 3: From .NET application
The Entity Framework migrations are still the recommended approach for production, but this script can be used for:
- Development environment setup
- Testing database creation
- Manual database initialization
- Database documentation

### What it creates

- **25+ Tables**: All CMS tables with proper relationships
- **16 Sequences**: PostgreSQL sequences for auto-incrementing IDs
- **Initial Data**: Essential field types, configurations, and system data
- **Indexes**: Performance optimization indexes
- **Constraints**: Foreign keys and data integrity rules

### Safety Features

- Idempotent: Can be run multiple times safely
- Clean setup: Drops existing tables before creation
- Data validation: Includes proper constraints and relationships
- Migration tracking: Updates EF migration history

### Notes

- This script is equivalent to running both EF migrations:
  - `20250722045836_InitialCreate`
  - `20250722054703_SeedInitialData`
- Database name: `content_new`
- Default user: `postgres`
- All sequences start at 100
- Includes 24 field types and 52+ field configurations

### Troubleshooting

If you encounter issues:
1. Ensure PostgreSQL is running
2. Verify database `content_new` exists
3. Check user permissions
4. Review connection parameters
5. Check PostgreSQL logs for detailed errors
