using CMS.WebApi.Models.Entities;

namespace CMS.WebApi.Services.Interfaces;

public interface ICollectionService
{
    Task<IEnumerable<CollectionListing>> GetAllCollectionsAsync();
    Task<IEnumerable<CollectionListing>> GetAllCollectionsWithDetailsAsync();
    Task<CollectionListing?> GetCollectionByIdAsync(int id);
    Task<CollectionListing?> GetCollectionByIdWithDetailsAsync(int id);
    Task<CollectionListing?> GetCollectionByApiIdAsync(string apiId);
    Task<CollectionListing?> GetCollectionByApiIdWithDetailsAsync(string apiId);
    Task<IEnumerable<CollectionListing>> GetCollectionsByCategoryAsync(int categoryId);
    Task<CollectionListing> CreateCollectionAsync(CollectionListing collection);
    Task<CollectionListing> UpdateCollectionAsync(int id, CollectionListing collection);
    Task DeleteCollectionAsync(int id);
    Task<bool> CollectionExistsAsync(string apiId);
    Task<IEnumerable<CollectionField>> GetCollectionFieldsAsync(int collectionId);
    Task<CollectionField> AddFieldToCollectionAsync(int collectionId, CollectionField field);
    Task RemoveFieldFromCollectionAsync(int collectionId, int fieldId);
}
