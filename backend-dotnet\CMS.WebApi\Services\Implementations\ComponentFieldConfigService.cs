using Microsoft.EntityFrameworkCore;
using CMS.WebApi.Data;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Services.Interfaces;

namespace CMS.WebApi.Services.Implementations;

public class ComponentFieldConfigService : IComponentFieldConfigService
{
    private readonly CmsDbContext _context;
    private readonly ILogger<ComponentFieldConfigService> _logger;

    public ComponentFieldConfigService(
        CmsDbContext context,
        ILogger<ComponentFieldConfigService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<IEnumerable<ComponentFieldConfig>> GetAllComponentFieldConfigsAsync()
    {
        return await _context.ComponentFieldConfigs
            .Include(cfc => cfc.ComponentField)
            .Include(cfc => cfc.FieldConfig)
            .ToListAsync();
    }

    public async Task<IEnumerable<ComponentFieldConfig>> GetComponentFieldConfigsByComponentFieldIdAsync(int componentFieldId)
    {
        return await _context.ComponentFieldConfigs
            .Include(cfc => cfc.ComponentField)
            .Include(cfc => cfc.FieldConfig)
            .Where(cfc => cfc.ComponentFieldId == componentFieldId)
            .ToListAsync();
    }

    public async Task<ComponentFieldConfig?> GetComponentFieldConfigByIdAsync(int id)
    {
        return await _context.ComponentFieldConfigs
            .Include(cfc => cfc.ComponentField)
            .Include(cfc => cfc.FieldConfig)
            .FirstOrDefaultAsync(cfc => cfc.Id == id);
    }

    public async Task<ComponentFieldConfig> CreateComponentFieldConfigAsync(ComponentFieldConfig componentFieldConfig)
    {
        _logger.LogInformation("Creating component field config: ComponentFieldId={ComponentFieldId}, FieldConfigId={FieldConfigId}, ConfigValue={ConfigValue}",
            componentFieldConfig.ComponentFieldId, componentFieldConfig.FieldConfigId, componentFieldConfig.ConfigValue);

        // Validate component field exists
        var componentField = await _context.ComponentFields
            .FirstOrDefaultAsync(cf => cf.Id == componentFieldConfig.ComponentFieldId);

        if (componentField == null)
        {
            _logger.LogError("Component field with ID {ComponentFieldId} not found", componentFieldConfig.ComponentFieldId);
            throw new ArgumentException($"Component field with ID {componentFieldConfig.ComponentFieldId} not found");
        }

        // Validate field config exists
        var fieldConfig = await _context.FieldConfigs
            .FirstOrDefaultAsync(fc => fc.Id == componentFieldConfig.FieldConfigId);

        if (fieldConfig == null)
        {
            _logger.LogError("Field config with ID {FieldConfigId} not found", componentFieldConfig.FieldConfigId);
            throw new ArgumentException($"Field config with ID {componentFieldConfig.FieldConfigId} not found");
        }

        _logger.LogInformation("Validation passed. Adding component field config to context");
        _context.ComponentFieldConfigs.Add(componentFieldConfig);

        _logger.LogInformation("Saving component field config to database");
        await _context.SaveChangesAsync();

        _logger.LogInformation("Component field config created successfully: ID {Id}", componentFieldConfig.Id);

        // Return with includes
        return await GetComponentFieldConfigByIdAsync(componentFieldConfig.Id) ?? componentFieldConfig;
    }

    public async Task<IEnumerable<ComponentFieldConfig>> CreateComponentFieldConfigsAsync(IEnumerable<ComponentFieldConfig> componentFieldConfigs)
    {
        var createdConfigs = new List<ComponentFieldConfig>();

        foreach (var config in componentFieldConfigs)
        {
            try
            {
                var createdConfig = await CreateComponentFieldConfigAsync(config);
                createdConfigs.Add(createdConfig);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating component field config for ComponentFieldId: {ComponentFieldId}, FieldConfigId: {FieldConfigId}", 
                    config.ComponentFieldId, config.FieldConfigId);
                throw;
            }
        }

        return createdConfigs;
    }

    public async Task<ComponentFieldConfig> UpdateComponentFieldConfigAsync(int id, ComponentFieldConfig componentFieldConfig)
    {
        var existingConfig = await _context.ComponentFieldConfigs
            .FirstOrDefaultAsync(cfc => cfc.Id == id);

        if (existingConfig == null)
        {
            throw new ArgumentException($"Component field config with ID {id} not found");
        }

        // Update properties
        existingConfig.ConfigValue = componentFieldConfig.ConfigValue;
        existingConfig.IsActive = componentFieldConfig.IsActive;
        existingConfig.ModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        _logger.LogInformation("Component field config updated successfully: ID {Id}", id);

        return await GetComponentFieldConfigByIdAsync(id) ?? existingConfig;
    }

    public async Task DeleteComponentFieldConfigAsync(int id)
    {
        var config = await _context.ComponentFieldConfigs
            .FirstOrDefaultAsync(cfc => cfc.Id == id);

        if (config == null)
        {
            throw new ArgumentException($"Component field config with ID {id} not found");
        }

        _context.ComponentFieldConfigs.Remove(config);
        await _context.SaveChangesAsync();

        _logger.LogInformation("Component field config deleted successfully: ID {Id}", id);
    }
}
