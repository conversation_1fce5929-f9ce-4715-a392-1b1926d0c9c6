using System.ComponentModel.DataAnnotations;

namespace CMS.WebApi.Models.Entities;

public class ComponentListing : BaseEntity
{
    public int Id { get; set; }

    [Required(ErrorMessage = "Component name is required")]
    [StringLength(255)]
    public string ComponentName { get; set; } = string.Empty;

    [StringLength(255)]
    public string? ComponentDisplayName { get; set; }

    [StringLength(255)]
    public string? ComponentApiId { get; set; }

    public bool IsActive { get; set; } = true;

    public string? GetUrl { get; set; }
    public string? PostUrl { get; set; }
    public string? UpdateUrl { get; set; }

    public string? AdditionalInformation { get; set; }

    [StringLength(255)]
    public string? AdditionalInfoImage { get; set; }

    public ICollection<ComponentField> Fields { get; set; } = new List<ComponentField>();
    public ICollection<CollectionComponent> CollectionComponents { get; set; } = new List<CollectionComponent>();
    public ICollection<ComponentComponent> ParentComponents { get; set; } = new List<ComponentComponent>();
    public ICollection<ComponentComponent> ChildComponents { get; set; } = new List<ComponentComponent>();
}
