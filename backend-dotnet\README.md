# CMS Web API (.NET 8)

This is the .NET 8 Web API conversion of the Spring Boot CMS application.

## Project Structure

```
CMS.WebApi/
├── Controllers/          # Web API controllers
├── Models/              # Data models and DTOs
│   ├── Entities/        # Entity Framework models
│   ├── Requests/        # Request DTOs
│   └── Responses/       # Response DTOs
├── Services/            # Business logic services
│   ├── Interfaces/      # Service interfaces
│   └── Implementations/ # Service implementations
├── Data/                # Entity Framework DbContext
├── DTOs/                # Data Transfer Objects
├── Configuration/       # Configuration classes
├── Middleware/          # Custom middleware
├── Security/            # Authentication and authorization
├── Utilities/           # Utility classes
└── Exceptions/          # Custom exceptions
```

## Features

- **Multi-tenant architecture** with schema-based tenant isolation
- **JWT Authentication** with ASP.NET Core Identity
- **PostgreSQL database** with Entity Framework Core
- **RESTful API** with comprehensive Swagger documentation
- **File upload and media management**
- **Structured logging** with Serilog
- **CORS support** for frontend integration
- **Exception handling middleware**
- **AutoMapper** for object mapping

## Prerequisites

- .NET 8.0 SDK
- PostgreSQL 12+
- Visual Studio 2022 or VS Code

## Getting Started

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd backend-dotnet
   ```

2. **Install dependencies**
   ```bash
   dotnet restore
   ```

3. **Update connection string**
   - Edit `appsettings.json` and update the PostgreSQL connection string

4. **Run database migrations**
   ```bash
   dotnet ef database update
   ```

5. **Run the application**
   ```bash
   dotnet run
   ```

6. **Access Swagger UI**
   - Navigate to `https://localhost:5001/swagger` (or the configured port)

## API Endpoints

The API maintains the same endpoints as the original Spring Boot application:

- **Authentication**: `/api/auth/*`
- **Tenants**: `/api/tenants/*`
- **Clients**: `/api/clients/*`
- **Categories**: `/api/categories/*`
- **Media**: `/api/media/*`
- **Collections**: `/api/collections/*`
- **Components**: `/api/components/*`

## Configuration

Key configuration sections in `appsettings.json`:

- **ConnectionStrings**: Database connection settings
- **JwtSettings**: JWT token configuration
- **MultiTenancy**: Multi-tenant settings
- **FileUpload**: File upload configuration
- **Cors**: CORS policy settings
- **Serilog**: Logging configuration

## Multi-Tenancy

The application supports multi-tenancy using PostgreSQL schemas:

- Each tenant has its own schema in the database
- Tenant context is resolved from JWT tokens or HTTP headers
- Default tenant is "public"

## Authentication

- JWT-based authentication with ASP.NET Core Identity
- Token expiration and refresh support
- Role-based authorization (future enhancement)

## Development

### Adding New Entities

1. Create entity class in `Models/Entities/`
2. Add DbSet to `CmsDbContext`
3. Create migration: `dotnet ef migrations add <MigrationName>`
4. Update database: `dotnet ef database update`

### Adding New Services

1. Create interface in `Services/Interfaces/`
2. Create implementation in `Services/Implementations/`
3. Register in `Program.cs` dependency injection

### Adding New Controllers

1. Create controller in `Controllers/`
2. Inherit from `ControllerBase`
3. Add appropriate routing and authorization attributes

## Testing

Run tests with:
```bash
dotnet test
```

## Deployment

The application can be deployed to:
- Azure App Service
- Docker containers
- IIS
- Linux servers with Kestrel

## Migration from Spring Boot

This project is a direct conversion from the Spring Boot CMS application, maintaining:
- Same API endpoints and functionality
- Same database schema and multi-tenancy approach
- Same authentication and authorization patterns
- Same business logic and data models
