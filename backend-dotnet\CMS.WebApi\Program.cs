using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using Serilog;
using CMS.WebApi.Data;

using CMS.WebApi.Models.Entities;
using CMS.WebApi.Services.Interfaces;
using CMS.WebApi.Services.Implementations;
using CMS.WebApi.Configuration;
using CMS.WebApi.Filters;
using CMS.WebApi.Middleware;
using CMS.WebApi.Security;

var builder = WebApplication.CreateBuilder(args);

// Configure URLs to listen on all interfaces (HTTP only for now)
builder.WebHost.UseUrls("http://0.0.0.0:5000");

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .Enrich.WithProperty("Application", "CMS.WebApi")
    .Enrich.WithEnvironmentName()
    .WriteTo.Console(outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}")
    .WriteTo.File("logs/cms-api-.log",
        rollingInterval: RollingInterval.Day,
        retainedFileCountLimit: 30,
        outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}")
    .WriteTo.File("logs/errors/cms-api-errors-.log",
        restrictedToMinimumLevel: Serilog.Events.LogEventLevel.Warning,
        rollingInterval: RollingInterval.Day,
        retainedFileCountLimit: 90)
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container.
builder.Services.AddControllers(options =>
{
    options.Filters.Add<ModelValidationFilter>();
})
    .AddNewtonsoftJson(options =>
    {
        options.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore;
        options.SerializerSettings.DateFormatString = "yyyy-MM-ddTHH:mm:ss.fffZ";
        options.SerializerSettings.ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver();
    });

// Configure Entity Framework
builder.Services.AddDbContext<CmsDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));

// Database initialization is now handled by the DatabaseSetup.sql script

// Configure Identity
builder.Services.AddIdentity<User, IdentityRole<long>>(options =>
{
    options.Password.RequireDigit = false;
    options.Password.RequireLowercase = false;
    options.Password.RequireUppercase = false;
    options.Password.RequireNonAlphanumeric = false;
    options.Password.RequiredLength = 6;
    options.User.RequireUniqueEmail = true;
})
.AddEntityFrameworkStores<CmsDbContext>()
.AddDefaultTokenProviders();

// Configure JWT Authentication
var jwtSettings = builder.Configuration.GetSection("JwtSettings");
var key = Encoding.ASCII.GetBytes(jwtSettings["SecretKey"]!);

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.RequireHttpsMetadata = false;
    options.SaveToken = true;
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = true,
        ValidIssuer = jwtSettings["Issuer"],
        ValidateAudience = true,
        ValidAudience = jwtSettings["Audience"],
        ValidateLifetime = true,
        ClockSkew = TimeSpan.Zero
    };
})
.AddScheme<ApiTokenAuthenticationOptions, ApiTokenAuthenticationHandler>(
    ApiTokenAuthenticationOptions.DefaultScheme, options => { });

// Configure CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });

    // Also add a more specific policy from configuration
    var corsSection = builder.Configuration.GetSection("Cors");
    var allowedOrigins = corsSection.GetSection("AllowedOrigins").Get<string[]>() ?? new[] { "http://localhost:3000", "http://localhost:5173" };

    options.AddPolicy("ConfiguredCors", policy =>
    {
        policy.WithOrigins(allowedOrigins)
              .AllowAnyMethod()
              .AllowAnyHeader()
              .AllowCredentials();
    });
});

// Register services
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<IClientService, ClientService>();
builder.Services.AddScoped<ICategoryService, CategoryService>();
builder.Services.AddScoped<IMediaService, MediaService>();
builder.Services.AddScoped<IMediaFolderService, MediaFolderService>();
builder.Services.AddScoped<ICollectionService, CollectionService>();
builder.Services.AddScoped<ICollectionOrderingService, CollectionOrderingService>();
builder.Services.AddScoped<ICollectionFieldService, CollectionFieldService>();
builder.Services.AddScoped<ICollectionComponentService, CollectionComponentService>();
builder.Services.AddScoped<IComponentService, ComponentService>();
builder.Services.AddScoped<IComponentFieldService, ComponentFieldService>();
builder.Services.AddScoped<IComponentFieldConfigService, ComponentFieldConfigService>();
builder.Services.AddScoped<IComponentComponentService, ComponentComponentService>();
builder.Services.AddScoped<IContentEntryService, ContentEntryService>();
builder.Services.AddScoped<IFieldTypeService, FieldTypeService>();
builder.Services.AddScoped<IConfigTypeService, ConfigTypeService>();
builder.Services.AddScoped<IFieldConfigService, FieldConfigService>();
builder.Services.AddScoped<IApiTokenService, ApiTokenService>();
builder.Services.AddScoped<IJwtTokenService, JwtTokenService>();

// Register mappers
builder.Services.AddScoped<SimplifiedCollectionMapper>();

// Register authentication services
builder.Services.AddJwtAuthenticationEntryPoint();

// Add custom authorization policies
builder.Services.AddCustomPolicies();

// Register error handling service
builder.Services.AddScoped<IErrorHandlingService, ErrorHandlingService>();

// Register audit service
builder.Services.AddHttpContextAccessor();
builder.Services.AddScoped<IAuditService, AuditService>();

// Configure AutoMapper
builder.Services.AddAutoMapper(typeof(Program));

// Configure file upload settings
builder.Services.Configure<FileUploadConfiguration>(
    builder.Configuration.GetSection(FileUploadConfiguration.SectionName));

// Configure Swagger/OpenAPI
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new()
    {
        Title = "CMS API",
        Version = "v1",
        Description = "A comprehensive Content Management System API with multi-tenancy support",
        Contact = new() { Name = "CMS API Support", Email = "<EMAIL>" }
    });

    // JWT Bearer Authentication
    c.AddSecurityDefinition("Bearer", new()
    {
        Description = "JWT Authorization header using the Bearer scheme. Enter 'Bearer' [space] and then your token in the text input below.",
        Name = "Authorization",
        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
        Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    // API Token Authentication
    c.AddSecurityDefinition("ApiKey", new()
    {
        Description = "API Key authentication using X-API-Key header",
        Name = "X-API-Key",
        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
        Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey
    });

    c.AddSecurityRequirement(new()
    {
        {
            new()
            {
                Reference = new() { Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme, Id = "Bearer" }
            },
            Array.Empty<string>()
        }
    });

    // Include XML comments if available
    var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }


});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "CMS API V1");
        c.RoutePrefix = "swagger";
    });
}

// Disable HTTPS redirection for now
// app.UseHttpsRedirection();

app.UseCors("AllowAll");

// Add custom middleware
app.UseMiddleware<RequestLoggingMiddleware>();
app.UseJwtAuthenticationEntryPoint();

app.UseAuthentication();
app.UseAuthorization();

app.UseMiddleware<ExceptionHandlingMiddleware>();

app.MapControllers();

try
{
    Log.Information("Starting CMS Web API");

    // Database initialization
    using (var scope = app.Services.CreateScope())
    {
        var context = scope.ServiceProvider.GetRequiredService<CmsDbContext>();
        
        Log.Information("Checking database connection...");
        var canConnect = await context.Database.CanConnectAsync();
        Log.Information("Can connect to database: {CanConnect}", canConnect);

        if (!canConnect)
        {
            Log.Error("Cannot connect to database. Check connection string.");
            return;
        }

        // Apply any pending migrations (this will create tables if they don't exist)
        Log.Information("Applying database migrations...");
        await context.Database.MigrateAsync();
        Log.Information("Database migrations completed");

        // Data seeding is now handled by Entity Framework in CmsDbContext.SeedData()
        Log.Information("Initial data seeding completed via Entity Framework");
        
        // Check what tables exist after script execution
        var tableQuery = "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'";
        var existingTables = await context.Database.SqlQueryRaw<string>(tableQuery).ToListAsync();
        Log.Information("Existing tables: {Tables}", string.Join(", ", existingTables));
    }

    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}



