import React, { useState, useRef, useCallback } from 'react';
import { Upload, X, FileText, Image, File } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { toast } from '@/components/ui/use-toast';

interface DropZoneProps {
  onFilesAdded: (files: File[]) => Promise<void>;
  acceptedFileTypes?: string[];
  maxFileSize?: number; // in bytes
  multiple?: boolean;
  className?: string;
  showFileList?: boolean;
}

export function DropZone({
  onFilesAdded,
  acceptedFileTypes = ['image/*', 'application/pdf'],
  maxFileSize = 10 * 1024 * 1024, // 10MB default
  multiple = true,
  className = '',
  showFileList = true,
}: DropZoneProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [files, setFiles] = useState<File[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<{ [key: string]: number }>({});
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const validateFiles = useCallback((fileList: FileList | File[]): File[] => {
    const validFiles: File[] = [];
    
    Array.from(fileList).forEach(file => {
      // Check file type
      const fileType = file.type;
      const isValidType = acceptedFileTypes.some(type => {
        if (type.endsWith('/*')) {
          const category = type.split('/')[0];
          return fileType.startsWith(`${category}/`);
        }
        return type === fileType;
      });

      if (!isValidType) {
        toast({
          title: 'Invalid file type',
          description: `${file.name} is not an accepted file type.`,
          variant: 'destructive',
        });
        return;
      }

      // Check file size
      if (file.size > maxFileSize) {
        toast({
          title: 'File too large',
          description: `${file.name} exceeds the maximum file size of ${maxFileSize / (1024 * 1024)}MB.`,
          variant: 'destructive',
        });
        return;
      }

      validFiles.push(file);
    });

    return validFiles;
  }, [acceptedFileTypes, maxFileSize]);

  const handleDrop = useCallback(async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    
    const droppedFiles = e.dataTransfer.files;
    if (!droppedFiles || droppedFiles.length === 0) return;
    
    const validFiles = validateFiles(droppedFiles);
    if (validFiles.length === 0) return;
    
    // If multiple is false, only take the first valid file
    const filesToAdd = multiple ? validFiles : [validFiles[0]];
    
    setFiles(prev => [...prev, ...filesToAdd]);
    
    try {
      setUploading(true);
      await onFilesAdded(filesToAdd);
      
      // Clear files after successful upload
      setFiles([]);
      setUploadProgress({});
      
      toast({
        title: 'Upload successful',
        description: `${filesToAdd.length} file(s) uploaded successfully.`,
      });
    } catch (error) {
      console.error('Upload error:', error);
      toast({
        title: 'Upload failed',
        description: 'There was an error uploading your files. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setUploading(false);
    }
  }, [multiple, onFilesAdded, validateFiles]);

  const handleFileInputChange = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;
    
    const validFiles = validateFiles(selectedFiles);
    if (validFiles.length === 0) return;
    
    // If multiple is false, only take the first valid file
    const filesToAdd = multiple ? validFiles : [validFiles[0]];
    
    setFiles(prev => [...prev, ...filesToAdd]);
    
    try {
      setUploading(true);
      await onFilesAdded(filesToAdd);
      
      // Clear files after successful upload
      setFiles([]);
      setUploadProgress({});
      
      toast({
        title: 'Upload successful',
        description: `${filesToAdd.length} file(s) uploaded successfully.`,
      });
    } catch (error) {
      console.error('Upload error:', error);
      toast({
        title: 'Upload failed',
        description: 'There was an error uploading your files. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setUploading(false);
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  }, [multiple, onFilesAdded, validateFiles]);

  const handleButtonClick = useCallback(() => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, []);

  const removeFile = useCallback((index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  }, []);

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) {
      return <Image className="h-5 w-5 text-primary" />;
    } else if (file.type === 'application/pdf') {
      return <FileText className="h-5 w-5 text-primary" />;
    } else {
      return <File className="h-5 w-5 text-primary" />;
    }
  };

  // Update progress for a specific file
  const updateProgress = useCallback((fileName: string, progress: number) => {
    setUploadProgress(prev => ({
      ...prev,
      [fileName]: progress
    }));
  }, []);

  return (
    <div className={`flex flex-col ${className}`}>
      <div
        className={`border-2 border-dashed rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer transition-colors ${
          isDragging ? 'border-primary bg-primary/5' : 'border-border'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleButtonClick}
      >
        <input
          type="file"
          ref={fileInputRef}
          className="hidden"
          onChange={handleFileInputChange}
          accept={acceptedFileTypes.join(',')}
          multiple={multiple}
        />
        
        <Upload className="h-10 w-10 text-primary mb-2" />
        <h3 className="text-lg font-medium mb-1">Drag & Drop Files</h3>
        <p className="text-sm text-muted-foreground mb-2">
          or click to browse your files
        </p>
        <p className="text-xs text-muted-foreground">
          Accepted file types: {acceptedFileTypes.join(', ')}
        </p>
        <p className="text-xs text-muted-foreground">
          Max file size: {maxFileSize / (1024 * 1024)}MB
        </p>
      </div>

      {showFileList && files.length > 0 && (
        <div className="mt-4 space-y-2">
          <h4 className="text-sm font-medium">Files to upload:</h4>
          <ul className="space-y-2">
            {files.map((file, index) => (
              <li key={`${file.name}-${index}`} className="flex items-center justify-between p-2 bg-muted rounded-md">
                <div className="flex items-center space-x-2">
                  {getFileIcon(file)}
                  <span className="text-sm truncate max-w-[200px]">{file.name}</span>
                  <span className="text-xs text-muted-foreground">
                    {(file.size / 1024).toFixed(1)} KB
                  </span>
                </div>
                
                {uploadProgress[file.name] !== undefined && (
                  <div className="w-24">
                    <Progress value={uploadProgress[file.name]} className="h-2" />
                  </div>
                )}
                
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6"
                  onClick={(e) => {
                    e.stopPropagation();
                    removeFile(index);
                  }}
                  disabled={uploading}
                >
                  <X className="h-4 w-4" />
                </Button>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
