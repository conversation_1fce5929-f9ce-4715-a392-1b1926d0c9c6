import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';

interface CollectionComponent {
  id?: number;
  name?: string;
  displayName?: string;
  additionalInfo?: string;
  additionalInfoImage?: string;
  isRepeatable?: boolean;
  minRepeatOccurrences?: number;
  maxRepeatOccurrences?: number;
  isActive?: boolean;
  component?: {
    id: number;
    componentName: string;
    componentDisplayName: string;
  };
}

interface CollectionComponentEditDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (data: CollectionComponent) => void;
  collectionComponent?: CollectionComponent;
  isEditing?: boolean;
}

export default function CollectionComponentEditDialog({
  open,
  onClose,
  onSave,
  collectionComponent,
  isEditing = false
}: CollectionComponentEditDialogProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  // Form state
  const [name, setName] = useState('');
  const [displayName, setDisplayName] = useState('');
  const [additionalInfo, setAdditionalInfo] = useState('');
  const [additionalInfoImage, setAdditionalInfoImage] = useState('');
  const [isRepeatable, setIsRepeatable] = useState(false);
  const [minRepeatOccurrences, setMinRepeatOccurrences] = useState<number | undefined>(undefined);
  const [maxRepeatOccurrences, setMaxRepeatOccurrences] = useState<number | undefined>(undefined);
  const [isActive, setIsActive] = useState(true);

  // Form validation
  const [errors, setErrors] = useState<{
    name?: string;
    displayName?: string;
  }>({});

  // Initialize form data when dialog opens or collection component changes
  useEffect(() => {
    if (open && collectionComponent) {
      setName(collectionComponent.name || '');
      setDisplayName(collectionComponent.displayName || '');
      setAdditionalInfo(collectionComponent.additionalInfo || '');
      setAdditionalInfoImage(collectionComponent.additionalInfoImage || '');
      setIsRepeatable(collectionComponent.isRepeatable || false);
      setMinRepeatOccurrences(collectionComponent.minRepeatOccurrences);
      setMaxRepeatOccurrences(collectionComponent.maxRepeatOccurrences);
      setIsActive(collectionComponent.isActive !== false);
    } else if (open && !isEditing) {
      // Reset form for new collection component
      setName('');
      setDisplayName('');
      setAdditionalInfo('');
      setAdditionalInfoImage('');
      setIsRepeatable(false);
      setMinRepeatOccurrences(undefined);
      setMaxRepeatOccurrences(undefined);
      setIsActive(true);
    }
    setErrors({});
  }, [open, collectionComponent, isEditing]);

  // Validate form
  const validateForm = () => {
    const newErrors: {
      name?: string;
      displayName?: string;
    } = {};

    if (!name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!displayName.trim()) {
      newErrors.displayName = 'Display name is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const formData: CollectionComponent = {
        ...(collectionComponent || {}),
        name,
        displayName,
        additionalInfo,
        additionalInfoImage,
        isRepeatable,
        minRepeatOccurrences: isRepeatable ? minRepeatOccurrences : undefined,
        maxRepeatOccurrences: isRepeatable ? maxRepeatOccurrences : undefined,
        isActive
      };

      await onSave(formData);

      toast({
        title: 'Success',
        description: `Collection component ${isEditing ? 'updated' : 'created'} successfully`,
      });

      onClose();
    } catch (error) {
      console.error('Error saving collection component:', error);
      // Removed error toast notification
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit Collection Component' : 'Add Collection Component Details'}
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Update the collection component details and configuration.'
              : 'Configure additional details for this collection component.'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            {/* Component Info (read-only if editing) */}
            {collectionComponent?.component && (
              <div className="space-y-2">
                <Label className="text-base font-medium">Component</Label>
                <div className="p-3 bg-muted rounded-md">
                  <p className="font-medium">{collectionComponent.component.componentDisplayName}</p>
                  <p className="text-sm text-muted-foreground">{collectionComponent.component.componentName}</p>
                </div>
              </div>
            )}

            {/* Name */}
            <div className="space-y-2">
              <Label htmlFor="name" className="text-base font-medium">Name *</Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="e.g., User Registration Section"
                disabled={loading}
                className="h-10"
              />
              {errors.name && (
                <p className="text-sm text-destructive">{errors.name}</p>
              )}
              <p className="text-sm text-muted-foreground">
                Internal name for this collection component
              </p>
            </div>

            {/* Display Name */}
            <div className="space-y-2">
              <Label htmlFor="displayName" className="text-base font-medium">Display Name *</Label>
              <Input
                id="displayName"
                value={displayName}
                onChange={(e) => setDisplayName(e.target.value)}
                placeholder="e.g., User Registration Form"
                disabled={loading}
                className="h-10"
              />
              {errors.displayName && (
                <p className="text-sm text-destructive">{errors.displayName}</p>
              )}
              <p className="text-sm text-muted-foreground">
                Display name shown to users
              </p>
            </div>

            {/* Additional Info */}
            <div className="space-y-2">
              <Label htmlFor="additionalInfo" className="text-base font-medium">Additional Information (Optional)</Label>
              <Textarea
                id="additionalInfo"
                value={additionalInfo}
                onChange={(e) => setAdditionalInfo(e.target.value)}
                placeholder="Additional details about this collection component"
                disabled={loading}
                className="min-h-[80px] resize-y"
              />
              <p className="text-sm text-muted-foreground">
                Extra information that will be included in the API response
              </p>
            </div>

            {/* Additional Info Image */}
            <div className="space-y-2">
              <Label htmlFor="additionalInfoImage" className="text-base font-medium">Additional Info Image (Optional)</Label>
              <Input
                id="additionalInfoImage"
                value={additionalInfoImage}
                onChange={(e) => setAdditionalInfoImage(e.target.value)}
                placeholder="e.g., /images/collection-component-info.png"
                disabled={loading}
                className="h-10"
              />
              <p className="text-sm text-muted-foreground">
                Path or URL to an image that provides additional information
              </p>
            </div>

            {/* Repeatable Configuration */}
            <div className="space-y-4 border-t pt-4">
              <div className="flex items-center space-x-3">
                <Checkbox
                  id="isRepeatable"
                  checked={isRepeatable}
                  onCheckedChange={(checked) => setIsRepeatable(checked as boolean)}
                  disabled={loading}
                  className="h-5 w-5"
                />
                <Label htmlFor="isRepeatable" className="cursor-pointer text-base font-medium">
                  Repeatable Component
                </Label>
              </div>
              <p className="text-sm text-muted-foreground ml-8">
                Allow multiple instances of this component in the collection
              </p>

              {isRepeatable && (
                <div className="grid grid-cols-2 gap-4 ml-8">
                  <div className="space-y-2">
                    <Label htmlFor="minRepeatOccurrences" className="text-sm font-medium">Min Occurrences</Label>
                    <Input
                      id="minRepeatOccurrences"
                      type="number"
                      value={minRepeatOccurrences || ''}
                      onChange={(e) => setMinRepeatOccurrences(e.target.value ? parseInt(e.target.value) : undefined)}
                      placeholder="1"
                      disabled={loading}
                      className="h-9"
                      min="0"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="maxRepeatOccurrences" className="text-sm font-medium">Max Occurrences</Label>
                    <Input
                      id="maxRepeatOccurrences"
                      type="number"
                      value={maxRepeatOccurrences || ''}
                      onChange={(e) => setMaxRepeatOccurrences(e.target.value ? parseInt(e.target.value) : undefined)}
                      placeholder="10"
                      disabled={loading}
                      className="h-9"
                      min="1"
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Active Status */}
            <div className="flex items-center space-x-3 border-t pt-4">
              <Checkbox
                id="isActive"
                checked={isActive}
                onCheckedChange={(checked) => setIsActive(checked as boolean)}
                disabled={loading}
                className="h-5 w-5"
              />
              <Label htmlFor="isActive" className="cursor-pointer text-base font-medium">
                Active
              </Label>
              <p className="text-sm text-muted-foreground ml-2">
                When active, this component will be included in the collection
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Saving...' : (isEditing ? 'Update' : 'Save')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
