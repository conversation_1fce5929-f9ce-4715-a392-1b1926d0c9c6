using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.Responses;
using CMS.WebApi.Services.Interfaces;
using CMS.WebApi.Security;

namespace CMS.WebApi.Controllers;

[ApiController]
[Route("api/simplified-collections")]
[Authorize(Policy = AuthorizationPolicies.JwtOrApiToken)]
[Tags("Simplified Collections")]
public class SimplifiedCollectionController : ControllerBase
{
    private readonly ICollectionService _collectionService;
    private readonly ILogger<SimplifiedCollectionController> _logger;

    public SimplifiedCollectionController(ICollectionService collectionService, ILogger<SimplifiedCollectionController> logger)
    {
        _collectionService = collectionService;
        _logger = logger;
    }

    /// <summary>
    /// Get all collections in simplified format
    /// </summary>
    /// <returns>List of all collections in simplified format</returns>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<SimplifiedCollectionResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<IEnumerable<SimplifiedCollectionResponse>>> GetAllSimplifiedCollections()
    {
        try
        {
            var collections = await _collectionService.GetAllCollectionsAsync();
            
            if (!collections.Any())
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = "No collections found",
                    Path = Request.Path
                });
            }

            var simplifiedCollections = collections.Select(c => new SimplifiedCollectionResponse
            {
                Id = c.Id,
                Name = c.CollectionName,
                ApiId = c.CollectionApiId,
                Description = c.CollectionDesc,
                IsActive = true, // Default to true since CollectionListing doesn't have IsActive
                CreatedAt = c.CreatedAt
            });

            _logger.LogInformation("Retrieved {Count} simplified collections", simplifiedCollections.Count());
            return Ok(simplifiedCollections);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve simplified collections");
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving simplified collections",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get simplified collection by API ID
    /// </summary>
    /// <param name="apiId">Collection API ID</param>
    /// <returns>Simplified collection details</returns>
    [HttpGet("api/{apiId}")]
    [ProducesResponseType(typeof(SimplifiedCollectionResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<SimplifiedCollectionResponse>> GetSimplifiedCollectionByApiId(string apiId)
    {
        try
        {
            var collection = await _collectionService.GetCollectionByApiIdAsync(apiId);
            
            if (collection == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Collection with API ID '{apiId}' not found",
                    Path = Request.Path
                });
            }

            var simplifiedCollection = new SimplifiedCollectionResponse
            {
                Id = collection.Id,
                Name = collection.CollectionName,
                ApiId = collection.CollectionApiId,
                Description = collection.CollectionDesc,
                IsActive = true, // Default to true since CollectionListing doesn't have IsActive
                CreatedAt = collection.CreatedAt
            };

            return Ok(simplifiedCollection);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve simplified collection with API ID: {ApiId}", apiId);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving the simplified collection",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get simplified collection by ID
    /// </summary>
    /// <param name="id">Collection ID</param>
    /// <returns>Simplified collection details</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(SimplifiedCollectionResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<SimplifiedCollectionResponse>> GetSimplifiedCollectionById(int id)
    {
        try
        {
            var collection = await _collectionService.GetCollectionByIdAsync(id);
            
            if (collection == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Collection with ID {id} not found",
                    Path = Request.Path
                });
            }

            var simplifiedCollection = new SimplifiedCollectionResponse
            {
                Id = collection.Id,
                Name = collection.CollectionName,
                ApiId = collection.CollectionApiId,
                Description = collection.CollectionDesc,
                IsActive = true, // Default to true since CollectionListing doesn't have IsActive
                CreatedAt = collection.CreatedAt
            };

            return Ok(simplifiedCollection);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve simplified collection with ID: {CollectionId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving the simplified collection",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get active simplified collections only
    /// </summary>
    /// <returns>List of active simplified collections</returns>
    [HttpGet("active")]
    [ProducesResponseType(typeof(IEnumerable<SimplifiedCollectionResponse>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<SimplifiedCollectionResponse>>> GetActiveSimplifiedCollections()
    {
        try
        {
            var collections = await _collectionService.GetAllCollectionsAsync();
            
            var activeCollections = collections
                .Select(c => new SimplifiedCollectionResponse
                {
                    Id = c.Id,
                    Name = c.CollectionName,
                    ApiId = c.CollectionApiId,
                    Description = c.CollectionDesc,
                    IsActive = true, // Default to true since CollectionListing doesn't have IsActive
                    CreatedAt = c.CreatedAt
                });

            _logger.LogInformation("Retrieved {Count} active simplified collections", activeCollections.Count());
            return Ok(activeCollections);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve active simplified collections");
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving active simplified collections",
                Path = Request.Path
            });
        }
    }
}
