using CMS.WebApi.Models.Entities;

namespace CMS.WebApi.Services.Interfaces;

public interface IApiTokenService
{
    Task<IEnumerable<ApiToken>> GetUserApiTokensAsync(long userId);
    Task<ApiToken?> GetApiTokenByIdAsync(long id, long userId);
    Task<ApiToken?> GetApiTokenByValueAsync(string tokenValue);
    Task<ApiToken> CreateApiTokenAsync(ApiToken apiToken);
    Task<ApiToken> UpdateApiTokenAsync(long id, long userId, string name, string? description, bool isActive);
    Task DeleteApiTokenAsync(long id, long userId);
    Task RevokeApiTokenAsync(long id, long userId);
    Task<bool> TokenExistsForUserAsync(string name, long userId);
    Task<bool> IsTokenValidAsync(string tokenValue);
    Task UpdateLastUsedAsync(long tokenId);
    Task CleanupExpiredTokensAsync();
    Task<string> GenerateTokenValueAsync();
}
