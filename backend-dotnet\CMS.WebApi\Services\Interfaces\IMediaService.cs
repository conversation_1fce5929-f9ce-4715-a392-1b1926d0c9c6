using CMS.WebApi.Models.Entities;

namespace CMS.WebApi.Services.Interfaces;

public interface IMediaService
{
    Task<IEnumerable<Media>> GetAllMediaAsync();
    Task<IEnumerable<Media>> GetMediaByFolderAsync(int? folderId);
    Task<Media?> GetMediaByIdAsync(int id);
    Task<Media> CreateMediaAsync(Media media);
    Task<Media> UpdateMediaAsync(int id, Media media);
    Task DeleteMediaAsync(int id);
    Task<Media> UploadFileAsync(IFormFile file, int? folderId = null);
    Task<byte[]> GetFileContentAsync(int mediaId);
    Task<string> GetFilePathAsync(int mediaId);
    Task<int> UpdatePublicUrlsAsync();
    Task<Media?> GetMediaByShareTokenAsync(string shareToken);
    Task<Media> ReplaceFileAsync(int mediaId, IFormFile newFile);
}
