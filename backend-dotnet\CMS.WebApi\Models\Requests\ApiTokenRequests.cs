using System.ComponentModel.DataAnnotations;

namespace CMS.WebApi.Models.Requests;

public class CreateApiTokenRequest
{
    [Required(ErrorMessage = "Token name is required")]
    [StringLength(50, ErrorMessage = "Token name cannot exceed 50 characters")]
    public string Name { get; set; } = string.Empty;

    [StringLength(200, ErrorMessage = "Description cannot exceed 200 characters")]
    public string? Description { get; set; }

    [Required(ErrorMessage = "Expiration date is required")]
    public DateTime ExpiresAt { get; set; }
}

public class UpdateApiTokenRequest
{
    [Required(ErrorMessage = "Token name is required")]
    [StringLength(50, ErrorMessage = "Token name cannot exceed 50 characters")]
    public string Name { get; set; } = string.Empty;

    [StringLength(200, ErrorMessage = "Description cannot exceed 200 characters")]
    public string? Description { get; set; }

    public bool IsActive { get; set; } = true;
}
