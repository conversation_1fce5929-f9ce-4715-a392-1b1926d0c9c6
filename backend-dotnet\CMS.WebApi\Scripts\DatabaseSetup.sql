-- =====================================================
-- R-CMS Database Setup Script
-- Complete database schema creation and initial data
-- PostgreSQL Database Setup
-- =====================================================

-- Drop existing tables if they exist (for clean setup)
DROP TABLE IF EXISTS collection_field_config CASCADE;
DROP TABLE IF EXISTS component_field_config CASCADE;
DROP TABLE IF EXISTS content_entries CASCADE;
DROP TABLE IF EXISTS collection_components CASCADE;
DROP TABLE IF EXISTS component_components CASCADE;
DROP TABLE IF EXISTS collection_fields CASCADE;
DROP TABLE IF EXISTS component_fields CASCADE;
DROP TABLE IF EXISTS field_configs CASCADE;
DROP TABLE IF EXISTS collection_listing CASCADE;
DROP TABLE IF EXISTS component_listing CASCADE;
DROP TABLE IF EXISTS config_types CASCADE;
DROP TABLE IF EXISTS field_types CASCADE;
DROP TABLE IF EXISTS api_tokens CASCADE;
DROP TABLE IF EXISTS media CASCADE;
DROP TABLE IF EXISTS media_folders CASCADE;
DROP TABLE IF EXISTS "AspNetUserTokens" CASCADE;
DROP TABLE IF EXISTS "AspNetUserRoles" CASCADE;
DROP TABLE IF EXISTS "AspNetUserLogins" CASCADE;
DROP TABLE IF EXISTS "AspNetUserClaims" CASCADE;
DROP TABLE IF EXISTS "AspNetRoleClaims" CASCADE;
DROP TABLE IF EXISTS "AspNetRoles" CASCADE;
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS category CASCADE;
DROP TABLE IF EXISTS clients CASCADE;

-- Drop sequences if they exist
DROP SEQUENCE IF EXISTS cms_api_token_seq CASCADE;
DROP SEQUENCE IF EXISTS cms_category_seq CASCADE;
DROP SEQUENCE IF EXISTS cms_client_seq CASCADE;
DROP SEQUENCE IF EXISTS cms_collection_component_seq CASCADE;
DROP SEQUENCE IF EXISTS cms_collection_field_seq CASCADE;
DROP SEQUENCE IF EXISTS cms_collection_listing_seq CASCADE;
DROP SEQUENCE IF EXISTS cms_component_component_seq CASCADE;
DROP SEQUENCE IF EXISTS cms_component_field_seq CASCADE;
DROP SEQUENCE IF EXISTS cms_component_listing_seq CASCADE;
DROP SEQUENCE IF EXISTS cms_config_type_seq CASCADE;
DROP SEQUENCE IF EXISTS cms_content_entry_seq CASCADE;
DROP SEQUENCE IF EXISTS cms_field_config_seq CASCADE;
DROP SEQUENCE IF EXISTS cms_field_type_seq CASCADE;
DROP SEQUENCE IF EXISTS cms_media_folder_seq CASCADE;
DROP SEQUENCE IF EXISTS cms_media_seq CASCADE;
DROP SEQUENCE IF EXISTS cms_user_seq CASCADE;

-- =====================================================
-- CREATE SEQUENCES
-- =====================================================

CREATE SEQUENCE cms_api_token_seq START 100 INCREMENT 1;
CREATE SEQUENCE cms_category_seq START 100 INCREMENT 1;
CREATE SEQUENCE cms_client_seq START 100 INCREMENT 1;
CREATE SEQUENCE cms_collection_component_seq START 100 INCREMENT 1;
CREATE SEQUENCE cms_collection_field_seq START 100 INCREMENT 1;
CREATE SEQUENCE cms_collection_listing_seq START 100 INCREMENT 1;
CREATE SEQUENCE cms_component_component_seq START 100 INCREMENT 1;
CREATE SEQUENCE cms_component_field_seq START 100 INCREMENT 1;
CREATE SEQUENCE cms_component_listing_seq START 100 INCREMENT 1;
CREATE SEQUENCE cms_config_type_seq START 100 INCREMENT 1;
CREATE SEQUENCE cms_content_entry_seq START 100 INCREMENT 1;
CREATE SEQUENCE cms_field_config_seq START 100 INCREMENT 1;
CREATE SEQUENCE cms_field_type_seq START 100 INCREMENT 1;
CREATE SEQUENCE cms_media_folder_seq START 100 INCREMENT 1;
CREATE SEQUENCE cms_media_seq START 100 INCREMENT 1;
CREATE SEQUENCE cms_user_seq START 100 INCREMENT 1;

-- =====================================================
-- CREATE CORE TABLES
-- =====================================================

-- Clients table
CREATE TABLE clients (
    id integer NOT NULL DEFAULT nextval('cms_client_seq'),
    client_name character varying(100) NOT NULL,
    client_desc text,
    client_logo character varying(255),
    is_active boolean NOT NULL DEFAULT true,
    "CreatedAt" timestamp with time zone NOT NULL,
    "ModifiedAt" timestamp with time zone,
    "CreatedBy" character varying(50),
    "ModifiedBy" character varying(50),
    CONSTRAINT "PK_clients" PRIMARY KEY (id)
);

-- Category table
CREATE TABLE category (
    id integer NOT NULL DEFAULT nextval('cms_category_seq'),
    category_name character varying(100) NOT NULL,
    category_desc text,
    client_id integer NOT NULL,
    is_active boolean NOT NULL DEFAULT true,
    "CreatedAt" timestamp with time zone NOT NULL,
    "ModifiedAt" timestamp with time zone,
    "CreatedBy" character varying(50),
    "ModifiedBy" character varying(50),
    CONSTRAINT "PK_category" PRIMARY KEY (id),
    CONSTRAINT "FK_category_clients_client_id" FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE
);

-- Users table
CREATE TABLE users (
    id bigint NOT NULL DEFAULT nextval('cms_user_seq'),
    username character varying(50),
    email character varying(100),
    password text,
    is_active boolean NOT NULL DEFAULT true,
    "CreatedAt" timestamp with time zone NOT NULL,
    "ModifiedAt" timestamp with time zone,
    "CreatedBy" character varying(50),
    "ModifiedBy" character varying(50),
    "AccessFailedCount" integer NOT NULL,
    "ConcurrencyStamp" text,
    "EmailConfirmed" boolean NOT NULL,
    "LockoutEnabled" boolean NOT NULL,
    "LockoutEnd" timestamp with time zone,
    "NormalizedEmail" character varying(256),
    "NormalizedUserName" character varying(256),
    "PasswordHash" text,
    "PhoneNumber" text,
    "PhoneNumberConfirmed" boolean NOT NULL,
    "SecurityStamp" text,
    "TwoFactorEnabled" boolean NOT NULL,
    "UserName" character varying(256),
    CONSTRAINT "PK_users" PRIMARY KEY (id)
);

-- =====================================================
-- CREATE ASP.NET IDENTITY TABLES
-- =====================================================

CREATE TABLE "AspNetRoles" (
    "Id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "Name" character varying(256),
    "NormalizedName" character varying(256),
    "ConcurrencyStamp" text,
    CONSTRAINT "PK_AspNetRoles" PRIMARY KEY ("Id")
);

CREATE TABLE "AspNetRoleClaims" (
    "Id" integer NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "RoleId" bigint NOT NULL,
    "ClaimType" text,
    "ClaimValue" text,
    CONSTRAINT "PK_AspNetRoleClaims" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_AspNetRoleClaims_AspNetRoles_RoleId" FOREIGN KEY ("RoleId") REFERENCES "AspNetRoles" ("Id") ON DELETE CASCADE
);

CREATE TABLE "AspNetUserClaims" (
    "Id" integer NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "UserId" bigint NOT NULL,
    "ClaimType" text,
    "ClaimValue" text,
    CONSTRAINT "PK_AspNetUserClaims" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_AspNetUserClaims_users_UserId" FOREIGN KEY ("UserId") REFERENCES users (id) ON DELETE CASCADE
);

CREATE TABLE "AspNetUserLogins" (
    "LoginProvider" text NOT NULL,
    "ProviderKey" text NOT NULL,
    "ProviderDisplayName" text,
    "UserId" bigint NOT NULL,
    CONSTRAINT "PK_AspNetUserLogins" PRIMARY KEY ("LoginProvider", "ProviderKey"),
    CONSTRAINT "FK_AspNetUserLogins_users_UserId" FOREIGN KEY ("UserId") REFERENCES users (id) ON DELETE CASCADE
);

CREATE TABLE "AspNetUserRoles" (
    "UserId" bigint NOT NULL,
    "RoleId" bigint NOT NULL,
    CONSTRAINT "PK_AspNetUserRoles" PRIMARY KEY ("UserId", "RoleId"),
    CONSTRAINT "FK_AspNetUserRoles_AspNetRoles_RoleId" FOREIGN KEY ("RoleId") REFERENCES "AspNetRoles" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_AspNetUserRoles_users_UserId" FOREIGN KEY ("UserId") REFERENCES users (id) ON DELETE CASCADE
);

CREATE TABLE "AspNetUserTokens" (
    "UserId" bigint NOT NULL,
    "LoginProvider" text NOT NULL,
    "Name" text NOT NULL,
    "Value" text,
    CONSTRAINT "PK_AspNetUserTokens" PRIMARY KEY ("UserId", "LoginProvider", "Name"),
    CONSTRAINT "FK_AspNetUserTokens_users_UserId" FOREIGN KEY ("UserId") REFERENCES users (id) ON DELETE CASCADE
);

-- =====================================================
-- CREATE FIELD SYSTEM TABLES
-- =====================================================

-- Config Types table
CREATE TABLE config_types (
    id integer NOT NULL DEFAULT nextval('cms_config_type_seq'),
    config_type_name character varying(100) NOT NULL,
    config_type_desc text,
    display_name character varying(100),
    additional_info text,
    disclaimer_text text,
    placeholder_text character varying(255),
    is_active boolean NOT NULL DEFAULT true,
    "CreatedAt" timestamp with time zone NOT NULL,
    "ModifiedAt" timestamp with time zone,
    "CreatedBy" character varying(50),
    "ModifiedBy" character varying(50),
    CONSTRAINT "PK_config_types" PRIMARY KEY (id)
);

-- Field Types table
CREATE TABLE field_types (
    id integer NOT NULL DEFAULT nextval('cms_field_type_seq'),
    field_type_name character varying(100) NOT NULL,
    field_type_desc text,
    display_name character varying(100),
    help_text text,
    logo_image_path character varying(255),
    is_active boolean NOT NULL DEFAULT true,
    "CreatedAt" timestamp with time zone NOT NULL,
    "ModifiedAt" timestamp with time zone,
    "CreatedBy" character varying(50),
    "ModifiedBy" character varying(50),
    CONSTRAINT "PK_field_types" PRIMARY KEY (id)
);

-- Field Configs table
CREATE TABLE field_configs (
    id integer NOT NULL DEFAULT nextval('cms_field_config_seq'),
    config_name character varying(100) NOT NULL,
    value_type character varying(50),
    field_type_id integer NOT NULL,
    config_type_id integer NOT NULL,
    is_active boolean NOT NULL DEFAULT true,
    "CreatedAt" timestamp with time zone NOT NULL,
    "ModifiedAt" timestamp with time zone,
    "CreatedBy" character varying(50),
    "ModifiedBy" character varying(50),
    CONSTRAINT "PK_field_configs" PRIMARY KEY (id),
    CONSTRAINT "FK_field_configs_config_types_config_type_id" FOREIGN KEY (config_type_id) REFERENCES config_types(id) ON DELETE CASCADE,
    CONSTRAINT "FK_field_configs_field_types_field_type_id" FOREIGN KEY (field_type_id) REFERENCES field_types(id) ON DELETE CASCADE
);

-- =====================================================
-- CREATE MEDIA TABLES
-- =====================================================

-- Media Folders table
CREATE TABLE media_folders (
    id integer NOT NULL DEFAULT nextval('cms_media_folder_seq'),
    folder_name character varying(255) NOT NULL,
    folder_path text NOT NULL,
    parent_folder_id integer,
    is_active boolean NOT NULL DEFAULT true,
    "CreatedAt" timestamp with time zone NOT NULL,
    "ModifiedAt" timestamp with time zone,
    "CreatedBy" character varying(50),
    "ModifiedBy" character varying(50),
    CONSTRAINT "PK_media_folders" PRIMARY KEY (id),
    CONSTRAINT "FK_media_folders_media_folders_parent_folder_id" FOREIGN KEY (parent_folder_id) REFERENCES media_folders(id)
);

-- Media table
CREATE TABLE media (
    id integer NOT NULL DEFAULT nextval('cms_media_seq'),
    file_name character varying(255) NOT NULL,
    original_file_name character varying(255) NOT NULL,
    file_path text NOT NULL,
    file_type character varying(100) NOT NULL,
    file_size bigint NOT NULL,
    width integer,
    height integer,
    alt_text text,
    caption text,
    folder_id integer,
    is_active boolean NOT NULL DEFAULT true,
    "CreatedAt" timestamp with time zone NOT NULL,
    "ModifiedAt" timestamp with time zone,
    "CreatedBy" character varying(50),
    "ModifiedBy" character varying(50),
    CONSTRAINT "PK_media" PRIMARY KEY (id),
    CONSTRAINT "FK_media_media_folders_folder_id" FOREIGN KEY (folder_id) REFERENCES media_folders(id)
);

-- API Tokens table
CREATE TABLE api_tokens (
    id bigint NOT NULL DEFAULT nextval('cms_api_token_seq'),
    token_name character varying(100) NOT NULL,
    token_value character varying(500) NOT NULL,
    expires_at timestamp with time zone,
    is_active boolean NOT NULL DEFAULT true,
    user_id bigint NOT NULL,
    "CreatedAt" timestamp with time zone NOT NULL,
    "ModifiedAt" timestamp with time zone,
    "CreatedBy" character varying(50),
    "ModifiedBy" character varying(50),
    CONSTRAINT "PK_api_tokens" PRIMARY KEY (id),
    CONSTRAINT "FK_api_tokens_users_user_id" FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- =====================================================
-- CREATE COLLECTION AND COMPONENT TABLES
-- =====================================================

-- Collection Listing table
CREATE TABLE collection_listing (
    id integer NOT NULL DEFAULT nextval('cms_collection_listing_seq'),
    collection_name character varying(100) NOT NULL,
    collection_desc text,
    api_id character varying(100) NOT NULL,
    is_active boolean NOT NULL DEFAULT true,
    "CreatedAt" timestamp with time zone NOT NULL,
    "ModifiedAt" timestamp with time zone,
    "CreatedBy" character varying(50),
    "ModifiedBy" character varying(50),
    CONSTRAINT "PK_collection_listing" PRIMARY KEY (id)
);

-- Component Listing table
CREATE TABLE component_listing (
    id integer NOT NULL DEFAULT nextval('cms_component_listing_seq'),
    component_name character varying(100) NOT NULL,
    component_desc text,
    api_id character varying(100) NOT NULL,
    is_active boolean NOT NULL DEFAULT true,
    "CreatedAt" timestamp with time zone NOT NULL,
    "ModifiedAt" timestamp with time zone,
    "CreatedBy" character varying(50),
    "ModifiedBy" character varying(50),
    CONSTRAINT "PK_component_listing" PRIMARY KEY (id)
);

-- Collection Fields table
CREATE TABLE collection_fields (
    id integer NOT NULL DEFAULT nextval('cms_collection_field_seq'),
    field_name character varying(100) NOT NULL,
    field_desc text,
    api_id character varying(100) NOT NULL,
    field_type_id integer NOT NULL,
    collection_id integer NOT NULL,
    field_order integer NOT NULL DEFAULT 0,
    is_required boolean NOT NULL DEFAULT false,
    is_active boolean NOT NULL DEFAULT true,
    "CreatedAt" timestamp with time zone NOT NULL,
    "ModifiedAt" timestamp with time zone,
    "CreatedBy" character varying(50),
    "ModifiedBy" character varying(50),
    CONSTRAINT "PK_collection_fields" PRIMARY KEY (id),
    CONSTRAINT "FK_collection_fields_collection_listing_collection_id" FOREIGN KEY (collection_id) REFERENCES collection_listing(id) ON DELETE CASCADE,
    CONSTRAINT "FK_collection_fields_field_types_field_type_id" FOREIGN KEY (field_type_id) REFERENCES field_types(id) ON DELETE CASCADE
);

-- Component Fields table
CREATE TABLE component_fields (
    id integer NOT NULL DEFAULT nextval('cms_component_field_seq'),
    field_name character varying(100) NOT NULL,
    field_desc text,
    api_id character varying(100) NOT NULL,
    field_type_id integer NOT NULL,
    component_id integer NOT NULL,
    field_order integer NOT NULL DEFAULT 0,
    is_required boolean NOT NULL DEFAULT false,
    is_active boolean NOT NULL DEFAULT true,
    "CreatedAt" timestamp with time zone NOT NULL,
    "ModifiedAt" timestamp with time zone,
    "CreatedBy" character varying(50),
    "ModifiedBy" character varying(50),
    CONSTRAINT "PK_component_fields" PRIMARY KEY (id),
    CONSTRAINT "FK_component_fields_component_listing_component_id" FOREIGN KEY (component_id) REFERENCES component_listing(id) ON DELETE CASCADE,
    CONSTRAINT "FK_component_fields_field_types_field_type_id" FOREIGN KEY (field_type_id) REFERENCES field_types(id) ON DELETE CASCADE
);

-- Collection Components table
CREATE TABLE collection_components (
    id integer NOT NULL DEFAULT nextval('cms_collection_component_seq'),
    collection_id integer NOT NULL,
    component_id integer NOT NULL,
    is_active boolean NOT NULL DEFAULT true,
    "CreatedAt" timestamp with time zone NOT NULL,
    "ModifiedAt" timestamp with time zone,
    "CreatedBy" character varying(50),
    "ModifiedBy" character varying(50),
    CONSTRAINT "PK_collection_components" PRIMARY KEY (id),
    CONSTRAINT "FK_collection_components_collection_listing_collection_id" FOREIGN KEY (collection_id) REFERENCES collection_listing(id) ON DELETE CASCADE,
    CONSTRAINT "FK_collection_components_component_listing_component_id" FOREIGN KEY (component_id) REFERENCES component_listing(id) ON DELETE CASCADE
);

-- Component Components table (self-referencing)
CREATE TABLE component_components (
    id integer NOT NULL DEFAULT nextval('cms_component_component_seq'),
    parent_component_id integer NOT NULL,
    child_component_id integer NOT NULL,
    is_active boolean NOT NULL DEFAULT true,
    "CreatedAt" timestamp with time zone NOT NULL,
    "ModifiedAt" timestamp with time zone,
    "CreatedBy" character varying(50),
    "ModifiedBy" character varying(50),
    CONSTRAINT "PK_component_components" PRIMARY KEY (id),
    CONSTRAINT "FK_component_components_component_listing_child_component_id" FOREIGN KEY (child_component_id) REFERENCES component_listing(id) ON DELETE CASCADE,
    CONSTRAINT "FK_component_components_component_listing_parent_component_id" FOREIGN KEY (parent_component_id) REFERENCES component_listing(id) ON DELETE CASCADE
);

-- Collection Field Config table
CREATE TABLE collection_field_config (
    id integer NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    collection_field_id integer NOT NULL,
    field_config_id integer NOT NULL,
    config_value text,
    is_active boolean NOT NULL DEFAULT true,
    "CreatedAt" timestamp with time zone NOT NULL,
    "ModifiedAt" timestamp with time zone,
    "CreatedBy" character varying(50),
    "ModifiedBy" character varying(50),
    CONSTRAINT "PK_collection_field_config" PRIMARY KEY (id),
    CONSTRAINT "FK_collection_field_config_collection_fields_collection_field_id" FOREIGN KEY (collection_field_id) REFERENCES collection_fields(id) ON DELETE CASCADE,
    CONSTRAINT "FK_collection_field_config_field_configs_field_config_id" FOREIGN KEY (field_config_id) REFERENCES field_configs(id) ON DELETE CASCADE
);

-- Component Field Config table
CREATE TABLE component_field_config (
    id integer NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    component_field_id integer NOT NULL,
    field_config_id integer NOT NULL,
    config_value text,
    is_active boolean NOT NULL DEFAULT true,
    "CreatedAt" timestamp with time zone NOT NULL,
    "ModifiedAt" timestamp with time zone,
    "CreatedBy" character varying(50),
    "ModifiedBy" character varying(50),
    CONSTRAINT "PK_component_field_config" PRIMARY KEY (id),
    CONSTRAINT "FK_component_field_config_component_fields_component_field_id" FOREIGN KEY (component_field_id) REFERENCES component_fields(id) ON DELETE CASCADE,
    CONSTRAINT "FK_component_field_config_field_configs_field_config_id" FOREIGN KEY (field_config_id) REFERENCES field_configs(id) ON DELETE CASCADE
);

-- Content Entries table
CREATE TABLE content_entries (
    id integer NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    collection_id integer NOT NULL,
    data_json jsonb,
    "CreatedAt" timestamp with time zone NOT NULL,
    "ModifiedAt" timestamp with time zone,
    "CreatedBy" character varying(50),
    "ModifiedBy" character varying(50),
    CONSTRAINT "PK_content_entries" PRIMARY KEY (id),
    CONSTRAINT "FK_content_entries_collection_listing_collection_id" FOREIGN KEY (collection_id) REFERENCES collection_listing(id) ON DELETE CASCADE
);

-- =====================================================
-- CREATE INDEXES
-- =====================================================

CREATE INDEX "IX_AspNetRoleClaims_RoleId" ON "AspNetRoleClaims" ("RoleId");
CREATE UNIQUE INDEX "RoleNameIndex" ON "AspNetRoles" ("NormalizedName");
CREATE INDEX "IX_AspNetUserClaims_UserId" ON "AspNetUserClaims" ("UserId");
CREATE INDEX "IX_AspNetUserLogins_UserId" ON "AspNetUserLogins" ("UserId");
CREATE INDEX "IX_AspNetUserRoles_RoleId" ON "AspNetUserRoles" ("RoleId");
CREATE INDEX "EmailIndex" ON users ("NormalizedEmail");
CREATE UNIQUE INDEX "UserNameIndex" ON users ("NormalizedUserName");

CREATE INDEX "IX_api_tokens_user_id" ON api_tokens (user_id);
CREATE INDEX "IX_category_client_id" ON category (client_id);
CREATE INDEX "IX_collection_components_collection_id" ON collection_components (collection_id);
CREATE INDEX "IX_collection_components_component_id" ON collection_components (component_id);
CREATE INDEX "IX_collection_field_config_collection_field_id" ON collection_field_config (collection_field_id);
CREATE INDEX "IX_collection_field_config_field_config_id" ON collection_field_config (field_config_id);
CREATE INDEX "IX_collection_fields_collection_id" ON collection_fields (collection_id);
CREATE INDEX "IX_collection_fields_field_type_id" ON collection_fields (field_type_id);
CREATE INDEX "IX_component_components_child_component_id" ON component_components (child_component_id);
CREATE INDEX "IX_component_components_parent_component_id" ON component_components (parent_component_id);
CREATE INDEX "IX_component_field_config_component_field_id" ON component_field_config (component_field_id);
CREATE INDEX "IX_component_field_config_field_config_id" ON component_field_config (field_config_id);
CREATE INDEX "IX_component_fields_component_id" ON component_fields (component_id);
CREATE INDEX "IX_component_fields_field_type_id" ON component_fields (field_type_id);
CREATE INDEX "IX_content_entries_collection_id" ON content_entries (collection_id);
CREATE INDEX "IX_field_configs_config_type_id" ON field_configs (config_type_id);
CREATE INDEX "IX_field_configs_field_type_id" ON field_configs (field_type_id);
CREATE INDEX "IX_media_folder_id" ON media (folder_id);
CREATE INDEX "IX_media_folders_parent_folder_id" ON media_folders (parent_folder_id);

-- =====================================================
-- INSERT INITIAL DATA
-- =====================================================

-- Insert Config Types
INSERT INTO config_types (id, config_type_name, config_type_desc, display_name, additional_info, disclaimer_text, placeholder_text, is_active, "CreatedAt", "CreatedBy") VALUES
(1, 'properties', 'Basic properties for fields', 'Properties', NULL, 'Basic properties for fields', 'Properties', true, '2024-01-01 00:00:00+00', 'System'),
(2, 'attributes', 'UI attributes for fields', 'Attributes', NULL, 'Attributes for fields', 'Attributes', true, '2024-01-01 00:00:00+00', 'System'),
(3, 'validations', 'Validation rules for fields', 'Validations', NULL, 'Validation rules for fields', 'Validations', true, '2024-01-01 00:00:00+00', 'System')
ON CONFLICT (id) DO NOTHING;

-- Reset sequence for config_types
SELECT setval('cms_config_type_seq', (SELECT COALESCE(MAX(id), 0) + 1 FROM config_types), false);

-- Insert Field Types
INSERT INTO field_types (id, field_type_name, field_type_desc, display_name, help_text, is_active, "CreatedAt", "CreatedBy") VALUES
(1, 'text', 'Simple text field', 'Text', 'Enter text here', true, '2024-01-01 00:00:00+00', 'System'),
(2, 'number', 'Numeric field', 'Number', 'Enter a number', true, '2024-01-01 00:00:00+00', 'System'),
(3, 'date', 'Date field', 'Date', 'Select a date', true, '2024-01-01 00:00:00+00', 'System'),
(4, 'image', 'Image upload field', 'Image', 'Upload an image', true, '2024-01-01 00:00:00+00', 'System'),
(5, 'rich_text', 'Rich text editor', 'Rich Text', 'Enter formatted text', true, '2024-01-01 00:00:00+00', 'System'),
(6, 'mask', 'Masked input field', 'Masked', 'Enter masked value (e.g., SSN, EIN)', true, '2024-01-01 00:00:00+00', 'System'),
(8, 'editor', 'Rich text editor', 'Editor', 'Enter formatted text', true, '2024-01-01 00:00:00+00', 'System'),
(9, 'password', 'Password input field', 'Password', 'Enter password', true, '2024-01-01 00:00:00+00', 'System'),
(10, 'autocomplete', 'Autocomplete suggestions', 'Autocomplete', 'Start typing for suggestions', true, '2024-01-01 00:00:00+00', 'System'),
(11, 'cascade_select', 'Cascade selection field', 'Cascade Select', 'Select dependent options', true, '2024-01-01 00:00:00+00', 'System'),
(12, 'dropdown', 'Dropdown selection', 'Dropdown', 'Select from dropdown', true, '2024-01-01 00:00:00+00', 'System'),
(13, 'file', 'File upload field', 'File', 'Upload a file', true, '2024-01-01 00:00:00+00', 'System'),
(14, 'multi_state_checkbox', 'Multi-state checkbox', 'Multi-State Checkbox', 'Select multiple states', true, '2024-01-01 00:00:00+00', 'System'),
(15, 'multi_select', 'Multi-select field', 'Multi-Select', 'Select multiple options', true, '2024-01-01 00:00:00+00', 'System'),
(16, 'mention', 'Mention users or tags', 'Mention', 'Mention users or tags', true, '2024-01-01 00:00:00+00', 'System'),
(17, 'textarea', 'Multi-line text input', 'Textarea', 'Enter multi-line text', true, '2024-01-01 00:00:00+00', 'System'),
(18, 'otp', 'One-time password input', 'OTP', 'Enter one-time password', true, '2024-01-01 00:00:00+00', 'System'),
(19, 'checkbox', 'Checkbox input field', 'Checkbox', 'Select one or more options', true, '2024-01-01 00:00:00+00', 'System'),
(20, 'radio_button', 'Radio button selection field', 'RadioButton', 'Select one option', true, '2024-01-01 00:00:00+00', 'System'),
(21, 'input_switch', 'Toggle switch input', 'InputSwitch', 'Toggle the option on or off', true, '2024-01-01 00:00:00+00', 'System'),
(22, 'slider', 'Slider input field', 'Slider', 'Drag to select a value', true, '2024-01-01 00:00:00+00', 'System'),
(23, 'rating', 'Rating input field', 'Rating', 'Rate from 1 to 5 stars', true, '2024-01-01 00:00:00+00', 'System'),
(24, 'chips', 'Chips input field', 'Chips', 'Enter tags or keywords', true, '2024-01-01 00:00:00+00', 'System'),
(25, 'enumeration', 'Enumeration field for fixed value lists', 'Enumeration', 'Define a list of fixed values', true, '2024-01-01 00:00:00+00', 'System')
ON CONFLICT (id) DO NOTHING;

-- Reset sequence for field_types
SELECT setval('cms_field_type_seq', (SELECT COALESCE(MAX(id), 0) + 1 FROM field_types), false);

-- Insert Field Configs (Essential configurations for each field type)
INSERT INTO field_configs (id, config_name, value_type, field_type_id, config_type_id, is_active, "CreatedAt", "CreatedBy") VALUES
-- Text field configurations
(1, 'placeholder', 'text', 1, 2, true, '2024-01-01 00:00:00+00', 'System'),
(2, 'maxlength', 'number', 1, 1, true, '2024-01-01 00:00:00+00', 'System'),
(3, 'required', 'boolean', 1, 3, true, '2024-01-01 00:00:00+00', 'System'),
(4, 'readonly', 'boolean', 1, 2, true, '2024-01-01 00:00:00+00', 'System'),
(5, 'disabled', 'boolean', 1, 2, true, '2024-01-01 00:00:00+00', 'System'),

-- Number field configurations
(6, 'placeholder', 'text', 2, 2, true, '2024-01-01 00:00:00+00', 'System'),
(7, 'min', 'number', 2, 3, true, '2024-01-01 00:00:00+00', 'System'),
(8, 'max', 'number', 2, 3, true, '2024-01-01 00:00:00+00', 'System'),
(9, 'step', 'number', 2, 1, true, '2024-01-01 00:00:00+00', 'System'),
(10, 'required', 'boolean', 2, 3, true, '2024-01-01 00:00:00+00', 'System'),

-- Date field configurations
(11, 'placeholder', 'text', 3, 2, true, '2024-01-01 00:00:00+00', 'System'),
(12, 'dateFormat', 'text', 3, 1, true, '2024-01-01 00:00:00+00', 'System'),
(13, 'showTime', 'boolean', 3, 1, true, '2024-01-01 00:00:00+00', 'System'),
(14, 'required', 'boolean', 3, 3, true, '2024-01-01 00:00:00+00', 'System'),

-- Image field configurations
(15, 'accept', 'text', 4, 1, true, '2024-01-01 00:00:00+00', 'System'),
(16, 'maxFileSize', 'number', 4, 3, true, '2024-01-01 00:00:00+00', 'System'),
(17, 'multiple', 'boolean', 4, 1, true, '2024-01-01 00:00:00+00', 'System'),
(18, 'required', 'boolean', 4, 3, true, '2024-01-01 00:00:00+00', 'System'),

-- Rich Text field configurations
(19, 'toolbar', 'text', 5, 1, true, '2024-01-01 00:00:00+00', 'System'),
(20, 'height', 'number', 5, 2, true, '2024-01-01 00:00:00+00', 'System'),
(21, 'required', 'boolean', 5, 3, true, '2024-01-01 00:00:00+00', 'System'),

-- Dropdown field configurations
(22, 'options', 'text', 12, 1, true, '2024-01-01 00:00:00+00', 'System'),
(23, 'placeholder', 'text', 12, 2, true, '2024-01-01 00:00:00+00', 'System'),
(24, 'required', 'boolean', 12, 3, true, '2024-01-01 00:00:00+00', 'System'),
(25, 'filter', 'boolean', 12, 1, true, '2024-01-01 00:00:00+00', 'System'),

-- File field configurations
(26, 'accept', 'text', 13, 1, true, '2024-01-01 00:00:00+00', 'System'),
(27, 'maxFileSize', 'number', 13, 3, true, '2024-01-01 00:00:00+00', 'System'),
(28, 'multiple', 'boolean', 13, 1, true, '2024-01-01 00:00:00+00', 'System'),
(29, 'required', 'boolean', 13, 3, true, '2024-01-01 00:00:00+00', 'System'),

-- Multi-select field configurations
(30, 'options', 'text', 15, 1, true, '2024-01-01 00:00:00+00', 'System'),
(31, 'placeholder', 'text', 15, 2, true, '2024-01-01 00:00:00+00', 'System'),
(32, 'required', 'boolean', 15, 3, true, '2024-01-01 00:00:00+00', 'System'),
(33, 'maxSelectedLabels', 'number', 15, 2, true, '2024-01-01 00:00:00+00', 'System'),

-- Textarea field configurations
(34, 'placeholder', 'text', 17, 2, true, '2024-01-01 00:00:00+00', 'System'),
(35, 'rows', 'number', 17, 2, true, '2024-01-01 00:00:00+00', 'System'),
(36, 'cols', 'number', 17, 2, true, '2024-01-01 00:00:00+00', 'System'),
(37, 'maxlength', 'number', 17, 1, true, '2024-01-01 00:00:00+00', 'System'),
(38, 'required', 'boolean', 17, 3, true, '2024-01-01 00:00:00+00', 'System'),

-- Checkbox field configurations
(39, 'options', 'text', 19, 1, true, '2024-01-01 00:00:00+00', 'System'),
(40, 'required', 'boolean', 19, 3, true, '2024-01-01 00:00:00+00', 'System'),

-- Radio Button field configurations
(41, 'options', 'text', 20, 1, true, '2024-01-01 00:00:00+00', 'System'),
(42, 'required', 'boolean', 20, 3, true, '2024-01-01 00:00:00+00', 'System'),

-- Slider field configurations
(43, 'min', 'number', 22, 1, true, '2024-01-01 00:00:00+00', 'System'),
(44, 'max', 'number', 22, 1, true, '2024-01-01 00:00:00+00', 'System'),
(45, 'step', 'number', 22, 1, true, '2024-01-01 00:00:00+00', 'System'),
(46, 'required', 'boolean', 22, 3, true, '2024-01-01 00:00:00+00', 'System'),

-- Rating field configurations
(47, 'stars', 'number', 23, 1, true, '2024-01-01 00:00:00+00', 'System'),
(48, 'cancel', 'boolean', 23, 1, true, '2024-01-01 00:00:00+00', 'System'),
(49, 'required', 'boolean', 23, 3, true, '2024-01-01 00:00:00+00', 'System'),

-- Chips field configurations
(50, 'placeholder', 'text', 24, 2, true, '2024-01-01 00:00:00+00', 'System'),
(51, 'max', 'number', 24, 3, true, '2024-01-01 00:00:00+00', 'System'),
(52, 'required', 'boolean', 24, 3, true, '2024-01-01 00:00:00+00', 'System')
ON CONFLICT (id) DO NOTHING;

-- Reset sequence for field_configs
SELECT setval('cms_field_config_seq', (SELECT COALESCE(MAX(id), 0) + 1 FROM field_configs), false);

-- =====================================================
-- CREATE MIGRATIONS HISTORY TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
);

-- Insert migration history
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion") VALUES
('20250722045836_InitialCreate', '8.0.0'),
('20250722054703_SeedInitialData', '8.0.0')
ON CONFLICT ("MigrationId") DO NOTHING;

-- =====================================================
-- SCRIPT COMPLETION MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE 'R-CMS Database setup completed successfully!';
    RAISE NOTICE 'Tables created: %', (SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_name != '__EFMigrationsHistory');
    RAISE NOTICE 'Sequences created: %', (SELECT count(*) FROM information_schema.sequences WHERE sequence_schema = 'public');
END $$;
