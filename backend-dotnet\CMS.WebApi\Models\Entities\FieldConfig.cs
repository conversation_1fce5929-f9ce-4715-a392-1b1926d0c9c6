using System.ComponentModel.DataAnnotations;

namespace CMS.WebApi.Models.Entities;

public class FieldConfig : BaseEntity
{
    public int Id { get; set; }

    public int? FieldTypeId { get; set; }
    public FieldType? FieldType { get; set; }

    public int? ConfigTypeId { get; set; }
    public ConfigType? ConfigType { get; set; }

    [StringLength(255)]
    public string? ConfigName { get; set; }

    public bool IsActive { get; set; } = true;

    [StringLength(50)]
    public string? ValueType { get; set; }
}
