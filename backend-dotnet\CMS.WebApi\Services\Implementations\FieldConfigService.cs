using CMS.WebApi.Data;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace CMS.WebApi.Services.Implementations;

public class FieldConfigService : IFieldConfigService
{
    private readonly CmsDbContext _context;
    private readonly ILogger<FieldConfigService> _logger;

    public FieldConfigService(CmsDbContext context, ILogger<FieldConfigService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<IEnumerable<FieldConfig>> GetAllFieldConfigsAsync()
    {
        return await _context.FieldConfigs
            .Include(fc => fc.FieldType)
            .Include(fc => fc.ConfigType)
            .OrderBy(fc => fc.Id)
            .ToListAsync();
    }

    public async Task<IEnumerable<FieldConfig>> GetActiveFieldConfigsAsync()
    {
        return await _context.FieldConfigs
            .Include(fc => fc.FieldType)
            .Include(fc => fc.ConfigType)
            .Where(fc => fc.IsActive)
            .OrderBy(fc => fc.Id)
            .ToListAsync();
    }

    public async Task<FieldConfig?> GetFieldConfigByIdAsync(int id)
    {
        return await _context.FieldConfigs
            .Include(fc => fc.FieldType)
            .Include(fc => fc.ConfigType)
            .FirstOrDefaultAsync(fc => fc.Id == id);
    }

    public async Task<IEnumerable<FieldConfig>> GetFieldConfigsByFieldTypeAsync(int fieldTypeId)
    {
        return await _context.FieldConfigs
            .Include(fc => fc.FieldType)
            .Include(fc => fc.ConfigType)
            .Where(fc => fc.FieldTypeId == fieldTypeId)
            .OrderBy(fc => fc.Id)
            .ToListAsync();
    }

    public async Task<IEnumerable<FieldConfig>> GetFieldConfigsByConfigTypeAsync(int configTypeId)
    {
        return await _context.FieldConfigs
            .Include(fc => fc.FieldType)
            .Include(fc => fc.ConfigType)
            .Where(fc => fc.ConfigTypeId == configTypeId)
            .OrderBy(fc => fc.Id)
            .ToListAsync();
    }

    public async Task<FieldConfig> CreateFieldConfigAsync(FieldConfig fieldConfig)
    {
        fieldConfig.CreatedAt = DateTime.UtcNow;

        _context.FieldConfigs.Add(fieldConfig);
        await _context.SaveChangesAsync();

        // Reload with includes
        return await GetFieldConfigByIdAsync(fieldConfig.Id) ?? fieldConfig;
    }

    public async Task<FieldConfig> UpdateFieldConfigAsync(int id, FieldConfig fieldConfig)
    {
        var existingFieldConfig = await _context.FieldConfigs.FindAsync(id);
        if (existingFieldConfig == null)
            throw new ArgumentException($"Field configuration with ID {id} not found");

        existingFieldConfig.FieldTypeId = fieldConfig.FieldTypeId;
        existingFieldConfig.ConfigTypeId = fieldConfig.ConfigTypeId;
        existingFieldConfig.ConfigName = fieldConfig.ConfigName;
        existingFieldConfig.IsActive = fieldConfig.IsActive;
        existingFieldConfig.ValueType = fieldConfig.ValueType;
        existingFieldConfig.ModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        // Reload with includes
        return await GetFieldConfigByIdAsync(id) ?? existingFieldConfig;
    }

    public async Task DeleteFieldConfigAsync(int id)
    {
        var fieldConfig = await _context.FieldConfigs.FindAsync(id);
        if (fieldConfig != null)
        {
            _context.FieldConfigs.Remove(fieldConfig);
            await _context.SaveChangesAsync();
        }
    }

    public async Task<bool> FieldConfigExistsByConfigNameAsync(string configName)
    {
        return await _context.FieldConfigs
            .AnyAsync(fc => fc.ConfigName == configName);
    }
}
