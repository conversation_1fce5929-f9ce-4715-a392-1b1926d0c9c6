# Component Field Creation API

This document describes how to use the "Create Field" functionality that saves field data to both `component_fields` and `component_field_config` tables.

## API Endpoint

**POST** `/api/component-fields/create-with-configs`

## Request Body Structure

```json
{
  "componentId": 100,
  "fieldTypeId": 1,
  "displayPreference": 10,
  "dependentOnId": null,
  "additionalInformation": "This is a sample text field",
  "configurations": [
    {
      "fieldConfigId": 1,
      "configValue": "Sample Field",
      "isActive": true
    },
    {
      "fieldConfigId": 2,
      "configValue": "sample_field",
      "isActive": true
    },
    {
      "fieldConfigId": 3,
      "configValue": "Enter your text here",
      "isActive": true
    },
    {
      "fieldConfigId": 10,
      "configValue": "true",
      "isActive": true
    },
    {
      "fieldConfigId": 15,
      "configValue": "5",
      "isActive": true
    },
    {
      "fieldConfigId": 16,
      "configValue": "100",
      "isActive": true
    }
  ]
}
```

## Field Configuration Mapping

Based on the UI tabs, here's how configurations typically map:

### Properties Tab
- **Display Name**: `fieldConfigId: 1` (display_name config)
- **Name**: `fieldConfigId: 2` (field_name config)  
- **Description**: `fieldConfigId: 3` (description config)

### Validations Tab
- **Required**: `fieldConfigId: 10` (required config) - value: "true"/"false"
- **Min Length**: `fieldConfigId: 15` (min_length config) - value: number as string
- **Max Length**: `fieldConfigId: 16` (max_length config) - value: number as string
- **Pattern**: `fieldConfigId: 17` (pattern config) - value: regex pattern

### Attributes Tab
- **Placeholder**: `fieldConfigId: 20` (placeholder config)
- **Help Text**: `fieldConfigId: 21` (help_text config)
- **Default Value**: `fieldConfigId: 22` (default_value config)

### Special Settings
- **Unique Field**: `fieldConfigId: 25` (unique config) - value: "true"/"false"

## Response Structure

```json
{
  "id": 150,
  "componentId": 100,
  "componentName": "test component",
  "componentDisplayName": "Test Component",
  "fieldTypeId": 1,
  "fieldTypeName": "text",
  "fieldTypeDisplayName": "Text Field",
  "displayPreference": 10,
  "dependentOnId": null,
  "additionalInformation": "This is a sample text field",
  "configs": [
    {
      "id": 200,
      "componentFieldId": 150,
      "fieldConfigId": 1,
      "configName": "display_name",
      "configValue": "Sample Field",
      "isActive": true,
      "valueType": "string",
      "createdAt": "2024-01-15T10:30:00Z",
      "createdBy": "<EMAIL>"
    }
  ],
  "createdAt": "2024-01-15T10:30:00Z",
  "createdBy": "<EMAIL>",
  "modifiedAt": null,
  "modifiedBy": null
}
```

## Database Operations

When you call this endpoint, it performs the following operations:

1. **Creates ComponentField record** in `component_fields` table:
   - `component_id`: From request.componentId
   - `field_type_id`: From request.fieldTypeId  
   - `display_preference`: From request.displayPreference (auto-calculated if not provided)
   - `dependent_on`: From request.dependentOnId
   - `additional_information`: From request.additionalInformation

2. **Creates ComponentFieldConfig records** in `component_field_config` table:
   - One record for each configuration in the request
   - `component_field_id`: ID of the created field
   - `field_config_id`: From each config.fieldConfigId
   - `config_value`: From each config.configValue
   - `is_active`: From each config.isActive

## Error Handling

- **400 Bad Request**: Invalid field type, component not found, or validation errors
- **500 Internal Server Error**: Database errors or unexpected exceptions

## Usage Example

```bash
curl -X POST "https://localhost:5001/api/component-fields/create-with-configs" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "componentId": 100,
    "fieldTypeId": 1,
    "configurations": [
      {
        "fieldConfigId": 1,
        "configValue": "My Field Name",
        "isActive": true
      },
      {
        "fieldConfigId": 10,
        "configValue": "true",
        "isActive": true
      }
    ]
  }'
```

This will create a new field with display name "My Field Name" and make it required.
