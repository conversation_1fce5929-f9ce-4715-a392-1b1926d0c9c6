namespace CMS.WebApi.Models.Responses;

public class ComponentFieldResponse
{
    public int Id { get; set; }
    public int ComponentId { get; set; }
    public string ComponentName { get; set; } = string.Empty;
    public string ComponentDisplayName { get; set; } = string.Empty;
    public int FieldTypeId { get; set; }
    public string FieldTypeName { get; set; } = string.Empty;
    public string FieldTypeDisplayName { get; set; } = string.Empty;
    public int? DisplayPreference { get; set; }
    public int? DependentOnId { get; set; }
    public string? AdditionalInformation { get; set; }
    public List<ComponentFieldConfigResponse> Configs { get; set; } = new List<ComponentFieldConfigResponse>();
    public DateTime CreatedAt { get; set; }
    public string? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public string? ModifiedBy { get; set; }
}

public class ComponentFieldConfigResponse
{
    public int Id { get; set; }
    public int ComponentFieldId { get; set; }
    public int FieldConfigId { get; set; }
    public string ConfigName { get; set; } = string.Empty;
    public string? ConfigValue { get; set; }
    public bool IsActive { get; set; }
    public string? ValueType { get; set; }
    public DateTime CreatedAt { get; set; }
    public string? CreatedBy { get; set; }
}
