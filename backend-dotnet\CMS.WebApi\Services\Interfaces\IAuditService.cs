using System.Security.Claims;

namespace CMS.WebApi.Services.Interfaces;

public interface IAuditService
{
    /// <summary>
    /// Gets the current user identifier for audit purposes
    /// </summary>
    /// <returns>The current user identifier or "system" if no user is authenticated</returns>
    string GetCurrentUser();
    
    /// <summary>
    /// Gets the current user ID
    /// </summary>
    /// <returns>The current user ID or null if no user is authenticated</returns>
    long? GetCurrentUserId();
    
    /// <summary>
    /// Gets the current user's username
    /// </summary>
    /// <returns>The current user's username or null if no user is authenticated</returns>
    string? GetCurrentUsername();
}
