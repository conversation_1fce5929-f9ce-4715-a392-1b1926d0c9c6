import React, { useState, useEffect, useRef } from 'react';
import { useMediaStore } from '@/lib/store';
import { mediaApi } from '@/lib/api';
import { useToast } from '@/components/ui/use-toast';
import {
  FolderPlus,
  Upload,
  Grid,
  List,
  Search,
  Settings,
  FileText,
  Image as ImageIcon,
  File,
  Video,
  Music,
  Copy,
  Check,
  Trash2,
  Share2,
  Folder,
  ChevronRight,
  X,
  Info
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { format } from 'date-fns';

// Define types for media assets and folders
interface MediaAsset {
  id: number;
  fileName: string;
  originalFileName: string;
  fileType: string;
  fileSize: number;
  width?: number;
  height?: number;
  duration?: number;
  altText?: string;
  description?: string;
  publicUrl: string;
  isPublic: boolean;
  folderId?: number;
  folderName?: string;
  uploadedByUsername?: string;
  createdAt: string;
  modifiedAt?: string;
  shareUrl?: string;
}

interface MediaFolder {
  id: number;
  folderName: string;
  description?: string;
  parentId?: number;
  parentName?: string;
  subfolders?: MediaFolder[];
  mediaCount: number;
  createdByUsername?: string;
  createdAt: string;
  modifiedAt?: string;
}

export default function MediaLibrary() {
  try {
  console.log('MediaLibrary component rendering');

  const { toast } = useToast();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('recent');
  const [searchQuery, setSearchQuery] = useState('');
  const [currentFolder, setCurrentFolder] = useState<MediaFolder | null>(null);
  const [breadcrumbs, setBreadcrumbs] = useState<MediaFolder[]>([]);
  const [assets, setAssets] = useState<MediaAsset[]>([]);
  const [folders, setFolders] = useState<MediaFolder[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedAsset, setSelectedAsset] = useState<MediaAsset | null>(null);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [createFolderDialogOpen, setCreateFolderDialogOpen] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');
  const [newFolderDescription, setNewFolderDescription] = useState('');
  const [shareDialogOpen, setShareDialogOpen] = useState(false);
  const [shareUrl, setShareUrl] = useState('');
  const [copied, setCopied] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<{ type: 'asset' | 'folder', id: number } | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Check if the media library is empty
  const isEmpty = assets.length === 0 && folders.length === 0;

  // Set document title
  useEffect(() => {
    document.title = 'Media Library | R-CMS';
    console.log('MediaLibrary component mounted');

    // Log any errors that might be happening
    window.addEventListener('error', (e) => {
      console.error('Global error caught:', e.error || e.message);
    });

    return () => {
      window.removeEventListener('error', (e) => {
        console.error('Global error caught:', e.error || e.message);
      });
    };
  }, []);

  // Fetch assets and folders
  useEffect(() => {
    console.log('Starting fetchMedia effect');

    const fetchMedia = async () => {
      console.log('fetchMedia function called');
      setLoading(true);
      setError(null); // Reset error state

      try {
        console.log('Setting up mock data for testing');

        // Use mock data for now to isolate the issue
        const mockFolders = [
          {
            id: 1,
            folderName: 'Test Folder 1',
            description: 'Test folder description',
            mediaCount: 2,
            createdByUsername: 'admin',
            createdAt: new Date().toISOString()
          },
          {
            id: 2,
            folderName: 'Test Folder 2',
            description: 'Another test folder',
            mediaCount: 0,
            createdByUsername: 'admin',
            createdAt: new Date().toISOString()
          }
        ];

        const mockAssets = [
          {
            id: 1,
            fileName: 'test-image.jpg',
            originalFileName: 'test-image.jpg',
            fileType: 'image/jpeg',
            fileSize: 12345,
            publicUrl: 'https://via.placeholder.com/150',
            isPublic: true,
            uploadedByUsername: 'admin',
            createdAt: new Date().toISOString()
          },
          {
            id: 2,
            fileName: 'test-document.pdf',
            originalFileName: 'test-document.pdf',
            fileType: 'application/pdf',
            fileSize: 54321,
            publicUrl: 'https://example.com/test.pdf',
            isPublic: true,
            uploadedByUsername: 'admin',
            createdAt: new Date().toISOString()
          }
        ];

        console.log('Setting mock data to state');
        setFolders(mockFolders);
        setAssets(mockAssets);

        // Update breadcrumbs if we have a current folder
        if (currentFolder) {
          console.log('Updating breadcrumbs for current folder:', currentFolder);
          // This is a simplified approach - in a real app, you'd need to fetch the full path
          const newBreadcrumbs = [...breadcrumbs];
          if (!newBreadcrumbs.find(f => f.id === currentFolder.id)) {
            newBreadcrumbs.push(currentFolder);
          }
          setBreadcrumbs(newBreadcrumbs);
        } else {
          console.log('Resetting breadcrumbs');
          setBreadcrumbs([]);
        }

        console.log('fetchMedia completed successfully');
      } catch (error) {
        console.error('Error in fetchMedia:', error);
        setError('Failed to load media assets. Please try again later.');
        // Removed error toast notification
      } finally {
        console.log('Setting loading to false');
        setLoading(false);
      }
    };

    fetchMedia().catch(err => {
      console.error('Unhandled error in fetchMedia:', err);
      setError('An unexpected error occurred. Please try again later.');
      setLoading(false);
    });

    return () => {
      console.log('Cleaning up fetchMedia effect');
    };
  }, [currentFolder, toast, breadcrumbs]);

  // Handle folder navigation
  const handleFolderClick = (folder: MediaFolder) => {
    setCurrentFolder(folder);
  };

  const handleBreadcrumbClick = (index: number) => {
    if (index === -1) {
      // Root level
      setCurrentFolder(null);
      setBreadcrumbs([]);
    } else {
      setCurrentFolder(breadcrumbs[index]);
      setBreadcrumbs(breadcrumbs.slice(0, index + 1));
    }
  };

  // Handle file upload
  const handleFileUpload = async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Upload each file
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const formData = new FormData();
        formData.append('file', file);

        if (currentFolder) {
          formData.append('folderId', currentFolder.id.toString());
        }

        // Set default to public
        formData.append('isPublic', 'true');

        console.log('Uploading file:', file.name, 'to folder:', currentFolder?.id);

        try {
          const response = await mediaApi.uploadAsset(formData);

          console.log('Upload response:', response);

          // Add the new asset to the list
          setAssets(prev => [response.data, ...prev]);
        } catch (uploadError) {
          console.error(`Error uploading file ${file.name}:`, uploadError);
          toast({
            title: 'Error',
            description: `Failed to upload ${file.name}`,
            variant: 'destructive',
          });
        }

        // Update progress even if individual upload fails
        setUploadProgress(Math.round(((i + 1) / files.length) * 100));
      }

      toast({
        title: 'Upload Complete',
        description: `Finished processing ${files.length} file(s)`,
      });
    } catch (error) {
      console.error('Error in upload process:', error);
      toast({
        title: 'Error',
        description: 'Failed to complete upload process',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
      setUploadDialogOpen(false);
    }
  };

  // Handle folder creation
  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) {
      toast({
        title: 'Error',
        description: 'Folder name is required',
        variant: 'destructive',
      });
      return;
    }

    try {
      // Log the request for debugging
      const folderData = {
        folderName: newFolderName,
        description: newFolderDescription,
        parentId: currentFolder?.id,
      };
      console.log('Creating folder with data:', folderData);

      const response = await mediaApi.createFolder(folderData);

      console.log('Folder creation response:', response);

      // Add the new folder to the list
      setFolders(prev => [response.data, ...prev]);

      // Reset form
      setNewFolderName('');
      setNewFolderDescription('');
      setCreateFolderDialogOpen(false);

      toast({
        title: 'Success',
        description: 'Folder created successfully',
      });
    } catch (error) {
      console.error('Error creating folder:', error);
      toast({
        title: 'Error',
        description: 'Failed to create folder. Please check the console for details.',
        variant: 'destructive',
      });
    }
  };

  // Handle asset selection
  const handleAssetClick = (asset: MediaAsset) => {
    setSelectedAsset(asset);
  };

  // Handle share link generation
  const handleShareClick = async (asset: MediaAsset) => {
    try {
      const response = await mediaApi.generateShareLink(asset.id.toString());
      setShareUrl(response.data);
      setShareDialogOpen(true);
    } catch (error) {
      console.error('Error generating share link:', error);
      toast({
        title: 'Error',
        description: 'Failed to generate share link',
        variant: 'destructive',
      });
    }
  };

  // Handle copy to clipboard
  const handleCopyClick = () => {
    navigator.clipboard.writeText(shareUrl);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  // Handle delete
  const handleDeleteClick = (type: 'asset' | 'folder', id: number) => {
    setItemToDelete({ type, id });
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!itemToDelete) return;

    try {
      if (itemToDelete.type === 'asset') {
        await mediaApi.deleteAsset(itemToDelete.id.toString());
        setAssets(prev => prev.filter(asset => asset.id !== itemToDelete.id));
      } else {
        await mediaApi.deleteFolder(itemToDelete.id.toString());
        setFolders(prev => prev.filter(folder => folder.id !== itemToDelete.id));
      }

      toast({
        title: 'Success',
        description: `${itemToDelete.type === 'asset' ? 'File' : 'Folder'} deleted successfully`,
      });
    } catch (error) {
      console.error('Error deleting item:', error);
      toast({
        title: 'Error',
        description: `Failed to delete ${itemToDelete.type}`,
        variant: 'destructive',
      });
    } finally {
      setDeleteDialogOpen(false);
      setItemToDelete(null);
    }
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Get icon for file type
  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return <ImageIcon className="h-6 w-6" />;
    } else if (fileType.startsWith('video/')) {
      return <Video className="h-6 w-6" />;
    } else if (fileType.startsWith('audio/')) {
      return <Music className="h-6 w-6" />;
    } else {
      return <File className="h-6 w-6" />;
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy h:mm a');
    } catch (error) {
      return 'Unknown date';
    }
  };

  // Simplified return statement for debugging
  console.log('Rendering MediaLibrary component');
  console.log('State:', { loading, error, isEmpty, assets, folders });

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h1 className="text-3xl font-bold">Media Library</h1>
          {/* Debug button - only visible in development */}
          {process.env.NODE_ENV === 'development' && (
            <Button
              variant="outline"
              size="sm"
              className="bg-yellow-100 hover:bg-yellow-200 text-yellow-800 border-yellow-300"
              onClick={() => {
                console.log('Debug info:');
                console.log('Current folder:', currentFolder);
                console.log('Breadcrumbs:', breadcrumbs);
                console.log('Assets:', assets);
                console.log('Folders:', folders);
                console.log('API base URL:', window.location.origin);
                toast({
                  title: 'Debug Info',
                  description: 'Check browser console for details',
                });
              }}
            >
              <Info className="mr-2 h-4 w-4" />
              Debug
            </Button>
          )}
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => setCreateFolderDialogOpen(true)}>
            <FolderPlus className="mr-2 h-4 w-4" />
            Add new folder
          </Button>
          <Button onClick={() => setUploadDialogOpen(true)}>
            <Upload className="mr-2 h-4 w-4" />
            Add new assets
          </Button>
        </div>
      </div>

      {/* Breadcrumbs */}
      <div className="flex items-center text-sm">
        <Button
          variant="ghost"
          size="sm"
          className="hover:bg-muted/50"
          onClick={() => handleBreadcrumbClick(-1)}
        >
          Media Library
        </Button>
        {breadcrumbs.map((folder, index) => (
          <React.Fragment key={folder.id}>
            <ChevronRight className="h-4 w-4 mx-1 text-muted-foreground" />
            <Button
              variant="ghost"
              size="sm"
              className="hover:bg-muted/50"
              onClick={() => handleBreadcrumbClick(index)}
            >
              {folder.folderName}
            </Button>
          </React.Fragment>
        ))}
      </div>

      <div className="flex flex-wrap items-center justify-between gap-4">
        <div className="flex-1">
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="recent">Most recent uploads</SelectItem>
              <SelectItem value="oldest">Oldest uploads</SelectItem>
              <SelectItem value="name">Name (A to Z)</SelectItem>
              <SelectItem value="size">Size (smallest first)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search..."
              className="pl-8 w-[200px]"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <div className="border rounded-md flex">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="icon"
              className="rounded-r-none"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="icon"
              className="rounded-l-none"
              onClick={() => setViewMode('list')}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>

          <Button variant="ghost" size="icon">
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {loading ? (
        // Loading state
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {Array.from({ length: 8 }).map((_, index) => (
            <div key={index} className="bg-muted/20 rounded-md aspect-square animate-pulse" />
          ))}
        </div>
      ) : error ? (
        // Error state
        <div className="flex flex-col items-center justify-center p-12 border border-dashed rounded-lg bg-destructive/10">
          <div className="w-20 h-20 mb-4 flex items-center justify-center rounded-full bg-destructive/20">
            <FileText className="h-10 w-10 text-destructive" />
          </div>
          <h3 className="text-xl font-medium mb-2 text-destructive">Error loading media</h3>
          <p className="text-muted-foreground mb-6 text-center max-w-md">
            {error}
          </p>
          <Button onClick={() => window.location.reload()}>
            <Upload className="mr-2 h-4 w-4" />
            Retry
          </Button>
        </div>
      ) : isEmpty ? (
        // Empty state
        <div className="flex flex-col items-center justify-center p-12 border border-dashed rounded-lg bg-muted/20">
          {error ? (
            <>
              <div className="w-20 h-20 mb-4 flex items-center justify-center rounded-full bg-destructive/10">
                <X className="h-10 w-10 text-destructive" />
              </div>
              <h3 className="text-xl font-medium mb-2">Something went wrong</h3>
              <p className="text-muted-foreground mb-6 text-center max-w-md">
                {error}
              </p>
              <Button onClick={() => window.location.reload()}>
                Try Again
              </Button>
            </>
          ) : currentFolder ? (
            <>
              <div className="w-20 h-20 mb-4 flex items-center justify-center rounded-full bg-primary/10">
                <Folder className="h-10 w-10 text-primary" />
              </div>
              <h3 className="text-xl font-medium mb-2">This folder is empty</h3>
              <p className="text-muted-foreground mb-6 text-center max-w-md">
                Upload files or create a subfolder to organize your media
              </p>
            </>
          ) : (
            <>
              <div className="w-20 h-20 mb-4 flex items-center justify-center rounded-full bg-primary/10">
                <FileText className="h-10 w-10 text-primary" />
              </div>
              <h3 className="text-xl font-medium mb-2">Upload your first assets...</h3>
              <p className="text-muted-foreground mb-6 text-center max-w-md">
                Drag and drop files to upload content to the Media Library
              </p>
            </>
          )}
          <div className="flex gap-2">
            {currentFolder && (
              <Button variant="outline" onClick={() => setCreateFolderDialogOpen(true)}>
                <FolderPlus className="mr-2 h-4 w-4" />
                New Folder
              </Button>
            )}
            <Button onClick={() => setUploadDialogOpen(true)}>
              <Upload className="mr-2 h-4 w-4" />
              Add new assets
            </Button>
          </div>
        </div>
      ) : (
        // Media items grid/list
        <div className="space-y-4">
          {/* Folders */}
          {folders.length > 0 && (
            <div className={viewMode === 'grid' ? "grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4" : "space-y-2"}>
              {folders.map((folder) => (
                <div
                  key={folder.id}
                  className={
                    viewMode === 'grid'
                      ? "bg-muted/10 rounded-md p-4 flex flex-col items-center justify-center hover:bg-muted/20 cursor-pointer transition-colors"
                      : "flex items-center p-3 border rounded-md hover:bg-muted/10 cursor-pointer transition-colors"
                  }
                  onClick={() => handleFolderClick(folder)}
                >
                  {viewMode === 'grid' ? (
                    <>
                      <Folder className="h-12 w-12 text-primary mb-2" />
                      <div className="font-medium text-center truncate w-full">{folder.folderName}</div>
                      <div className="text-xs text-muted-foreground">{folder.mediaCount} items</div>
                    </>
                  ) : (
                    <div className="flex items-center space-x-3 w-full">
                      <div className="bg-primary/10 rounded-md p-2">
                        <Folder className="h-6 w-6 text-primary" />
                      </div>
                      <div className="flex-1">
                        <div className="font-medium">{folder.folderName}</div>
                        <div className="text-sm text-muted-foreground">
                          {folder.mediaCount} items • Created by {folder.createdByUsername || 'Unknown'}
                        </div>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <Settings className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteClick('folder', folder.id);
                          }}>
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {/* Media Assets */}
          <div className={viewMode === 'grid' ? "grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4" : "space-y-2"}>
            {assets.map((asset) => (
              <div
                key={asset.id}
                className={
                  viewMode === 'grid'
                    ? "bg-muted/5 rounded-md overflow-hidden flex flex-col hover:bg-muted/10 cursor-pointer transition-colors border"
                    : "flex items-center p-3 border rounded-md hover:bg-muted/10 cursor-pointer transition-colors"
                }
                onClick={() => handleAssetClick(asset)}
              >
                {viewMode === 'grid' ? (
                  <>
                    <div className="aspect-square flex items-center justify-center bg-muted/10 relative">
                      {asset.fileType.startsWith('image/') ? (
                        <img
                          src={asset.publicUrl}
                          alt={asset.altText || asset.fileName}
                          className="object-cover w-full h-full"
                        />
                      ) : (
                        <div className="flex items-center justify-center h-full w-full">
                          {getFileIcon(asset.fileType)}
                        </div>
                      )}
                      <Badge className="absolute top-2 right-2 bg-background/80">
                        {asset.fileType.split('/')[0]}
                      </Badge>
                    </div>
                    <div className="p-3">
                      <div className="font-medium truncate">{asset.originalFileName}</div>
                      <div className="text-xs text-muted-foreground flex justify-between">
                        <span>{formatFileSize(asset.fileSize)}</span>
                        <span>By {asset.uploadedByUsername || 'Unknown'}</span>
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="flex items-center space-x-3 w-full">
                    <div className="bg-muted/10 rounded-md p-2">
                      {getFileIcon(asset.fileType)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="font-medium truncate">{asset.originalFileName}</div>
                      <div className="text-sm text-muted-foreground">
                        {formatFileSize(asset.fileSize)} • Added {formatDate(asset.createdAt)}
                      </div>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                          <Settings className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          handleShareClick(asset);
                        }}>
                          <Share2 className="h-4 w-4 mr-2" />
                          Share
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteClick('asset', asset.id);
                        }}>
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Upload Dialog */}
      <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Upload Media</DialogTitle>
            <DialogDescription>
              Upload files to your media library. Images, videos, documents, and audio files are supported.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div
              className="border-2 border-dashed rounded-lg p-12 text-center hover:bg-muted/5 transition-colors cursor-pointer"
              onClick={() => fileInputRef.current?.click()}
            >
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                multiple
                onChange={(e) => handleFileUpload(e.target.files)}
              />
              <Upload className="h-10 w-10 mx-auto mb-4 text-muted-foreground" />
              <p className="text-sm font-medium mb-1">Drag and drop files here or click to browse</p>
              <p className="text-xs text-muted-foreground">Maximum file size: 50MB</p>
            </div>

            {isUploading && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Uploading...</span>
                  <span>{uploadProgress}%</span>
                </div>
                <div className="w-full bg-muted rounded-full h-2.5">
                  <div
                    className="bg-primary h-2.5 rounded-full"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setUploadDialogOpen(false)} disabled={isUploading}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Create Folder Dialog */}
      <Dialog open={createFolderDialogOpen} onOpenChange={setCreateFolderDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Folder</DialogTitle>
            <DialogDescription>
              Create a new folder to organize your media files.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="folderName" className="text-sm font-medium">
                Folder Name
              </label>
              <Input
                id="folderName"
                value={newFolderName}
                onChange={(e) => setNewFolderName(e.target.value)}
                placeholder="My Folder"
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="folderDescription" className="text-sm font-medium">
                Description (Optional)
              </label>
              <Textarea
                id="folderDescription"
                value={newFolderDescription}
                onChange={(e) => setNewFolderDescription(e.target.value)}
                placeholder="Folder description..."
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setCreateFolderDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateFolder}>
              Create Folder
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Share Dialog */}
      <Dialog open={shareDialogOpen} onOpenChange={setShareDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Share Media</DialogTitle>
            <DialogDescription>
              Share this media file with others using the link below.
            </DialogDescription>
          </DialogHeader>

          <div className="flex items-center space-x-2">
            <Input value={shareUrl} readOnly />
            <Button size="icon" variant="outline" onClick={handleCopyClick}>
              {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
            </Button>
          </div>

          <DialogFooter>
            <Button onClick={() => setShareDialogOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this {itemToDelete?.type}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleConfirmDelete}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
  } catch (error) {
    console.error('Error rendering MediaLibrary component:', error);
    return (
      <div className="p-8 space-y-4 border border-destructive/50 rounded-lg bg-destructive/10">
        <h2 className="text-xl font-bold text-destructive">Error Rendering Media Library</h2>
        <p>An error occurred while rendering the Media Library component.</p>
        <pre className="p-4 bg-background border rounded-md overflow-auto max-h-[300px]">
          {error instanceof Error ? error.message : String(error)}
        </pre>
        <Button onClick={() => window.location.reload()} variant="outline">
          Reload Page
        </Button>
      </div>
    );
  }
}
