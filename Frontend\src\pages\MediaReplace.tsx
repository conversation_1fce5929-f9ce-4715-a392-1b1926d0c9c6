import React, { useState, useRef, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Upload } from 'lucide-react';
import { mediaApi } from '@/lib/api';
import { useToast } from '@/components/ui/use-toast';

export default function MediaReplace() {
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);

  // Handle file upload
  const handleUpload = () => {
    // Trigger the file input click
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Handle file selection
  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setIsUploading(true);

    try {
      // Upload each file
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const formData = new FormData();
        formData.append('file', file);

        // Set default to public
        formData.append('isPublic', 'true');

        console.log('Uploading file:', file.name);

        try {
          const response = await mediaApi.uploadAsset(formData);

          console.log('Upload response:', response);

          toast({
            title: 'Upload Success',
            description: `File ${file.name} uploaded successfully`,
          });
        } catch (uploadError) {
          console.error(`Error uploading file ${file.name}:`, uploadError);
          toast({
            title: 'Upload Failed',
            description: `Could not upload ${file.name}. Please try again.`,
            variant: 'destructive',
          });
        }
      }

      toast({
        title: 'Upload Complete',
        description: `${files.length} file(s) uploaded successfully`,
      });

      // Navigate back to media library
      navigate('/media-library');
    } catch (error) {
      console.error('Upload error:', error);
      toast({
        title: 'Upload Failed',
        description: 'Could not upload file(s). Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Replace</h1>
        <Button onClick={() => navigate('/media-library')} variant="outline">
          Back
        </Button>
      </div>

      <div className="border-2 border-dashed rounded-lg p-12 text-center hover:bg-muted/5 transition-colors cursor-pointer" onClick={handleUpload}>
        <input
          type="file"
          ref={fileInputRef}
          className="hidden"
          multiple
          onChange={handleFileChange}
          accept="image/*,video/*,audio/*,application/pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt"
        />
        <Upload className="h-10 w-10 mx-auto mb-4 text-muted-foreground" />
        <p className="text-sm font-medium mb-1">Drag and drop files here or click to browse</p>
        <p className="text-xs text-muted-foreground">Maximum file size: 50MB</p>
      </div>

      {isUploading && (
        <div className="flex items-center justify-center p-4 bg-muted/10 rounded-md">
          <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
          <span>Uploading...</span>
        </div>
      )}
    </div>
  );
}
