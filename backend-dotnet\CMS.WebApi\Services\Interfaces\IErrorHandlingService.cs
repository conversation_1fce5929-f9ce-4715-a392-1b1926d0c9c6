using CMS.WebApi.Models.Responses;

namespace CMS.WebApi.Services.Interfaces;

public interface IErrorHandlingService
{
    ErrorResponse CreateErrorResponse(int statusCode, string error, string message, string path, object? details = null);
    ErrorResponse CreateValidationErrorResponse(string message, string path, Dictionary<string, string[]> validationErrors);
    void LogError(Exception exception, string message, params object[] args);
    void LogWarning(string message, params object[] args);
}
