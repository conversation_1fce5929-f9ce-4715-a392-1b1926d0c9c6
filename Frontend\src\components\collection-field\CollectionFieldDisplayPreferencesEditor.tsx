import React, { useCallback, useEffect, useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ArrowUpDown } from 'lucide-react';
import { Field, FieldTypeEnum } from '@/lib/store';
import { collectionFieldsApi, collectionComponentsApi, collectionFieldConfigsApi } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

interface CollectionFieldDisplayPreferencesEditorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  collectionId: string;
  fields: Field[];
  onFieldsReordered: () => void;
}

export function CollectionFieldDisplayPreferencesEditor({
  open,
  onOpenChange,
  collectionId,
  fields,
  onFieldsReordered,
}: CollectionFieldDisplayPreferencesEditorProps) {
  const { toast } = useToast();
  const [orderedFields, setOrderedFields] = useState<Field[]>([]);
  const [loading, setLoading] = useState(false);
  const [fieldDisplayNames, setFieldDisplayNames] = useState<Record<string, string>>({});

  // Function to fetch display names for all fields
  const fetchFieldDisplayNames = useCallback(async (fieldsToProcess: Field[]) => {
    const displayNames: Record<string, string> = {};

    try {
      // Fetch display names for each field (excluding components)
      await Promise.all(
        fieldsToProcess
          .filter(field => !field.isComponent) // Only process actual fields, not components
          .map(async (field) => {
            try {
              const configResponse = await collectionFieldConfigsApi.getByCollectionFieldId(field.id.toString());
              const configs = configResponse.data || [];

              // Find the display-name config
              const displayNameConfig = configs.find((config: any) => config.configName === 'display-name');
              if (displayNameConfig && displayNameConfig.configValue) {
                displayNames[field.id.toString()] = displayNameConfig.configValue;
              } else {
                // Try to extract name from additionalInformation
                let fieldName = 'Unknown Field';
                if (field.additionalInformation) {
                  try {
                    const additionalInfo = JSON.parse(field.additionalInformation);
                    fieldName = additionalInfo.name || additionalInfo.fieldName || additionalInfo.label || fieldName;
                  } catch (e) {
                    console.error('Error parsing additionalInformation for field', field.id, e);
                  }
                }

                // If still no name found, use field type name as fallback
                if (fieldName === 'Unknown Field') {
                  fieldName = field.fieldType?.fieldTypeName || field.fieldType?.displayName || 'Unknown Field';
                }

                displayNames[field.id.toString()] = fieldName;
              }
            } catch (error) {
              console.error(`Error fetching display name for field ${field.id}:`, error);
              // Try to extract name from additionalInformation as fallback
              let fieldName = 'Unknown Field';
              if (field.additionalInformation) {
                try {
                  const additionalInfo = JSON.parse(field.additionalInformation);
                  fieldName = additionalInfo.name || additionalInfo.fieldName || additionalInfo.label || fieldName;
                } catch (e) {
                  console.error('Error parsing additionalInformation for field', field.id, e);
                }
              }

              // If still no name found, use field type name as fallback
              if (fieldName === 'Unknown Field') {
                fieldName = field.fieldType?.fieldTypeName || field.fieldType?.displayName || 'Unknown Field';
              }

              displayNames[field.id.toString()] = fieldName;
            }
          })
      );

      // For components, use their component name
      fieldsToProcess
        .filter(field => field.isComponent)
        .forEach(field => {
          displayNames[field.id.toString()] = field.name || 'Component';
        });

      setFieldDisplayNames(displayNames);
    } catch (error) {
      console.error('Error fetching field display names:', error);
    }
  }, []);

  // Function to fetch the latest data from the backend
  const fetchLatestData = useCallback(async () => {
    if (!collectionId) return;

    // Check if we have a timestamp for the last update
    const lastUpdateTimestamp = localStorage.getItem(`collection_field_display_prefs_updated_${collectionId}`);

    // Add a cache-busting parameter to force a fresh request
    const cacheBuster = lastUpdateTimestamp || Date.now();

    try {
      // Fetch both collection fields and collection components
      const [fieldsResponse, componentsResponse] = await Promise.all([
        collectionFieldsApi.getByCollectionId(collectionId),
        collectionComponentsApi.getByCollectionId(collectionId.toString())
      ]);

      const allItems: any[] = [];

      // Process collection fields
      if (fieldsResponse.data && Array.isArray(fieldsResponse.data)) {
        const regularFields = fieldsResponse.data.filter((field: any) => {
          // Basic validation - field should have an ID
          return field.id;
        });
        allItems.push(...regularFields);
      }

      // Process collection components
      if (componentsResponse.data && Array.isArray(componentsResponse.data)) {
        const components = componentsResponse.data.filter((component: any) => {
          // Basic validation - component should have an ID and be active
          return component.id && component.isActive !== false;
        });
        allItems.push(...components);
      }

      if (allItems.length > 0) {

        // Convert API response to Field format and ensure unique display preferences
        const updatedFields = allItems.map((item: any, index: number) => {
          // Determine if this is a field or component
          const isComponent = item.component !== undefined;

          // Extract name from multiple possible sources
          let itemName = 'Unnamed Item';

          if (isComponent) {
            // For components, get name from component data
            itemName = item.component?.componentName || item.name || item.displayName || `Component ${item.id}`;
          } else {
            // For fields, get name from field data
            if (item.name) {
              itemName = item.name;
            } else if (item.fieldName) {
              itemName = item.fieldName;
            } else if (item.additionalInformation) {
              try {
                const additionalInfo = JSON.parse(item.additionalInformation);
                itemName = additionalInfo.name || additionalInfo.fieldName || additionalInfo.label || `Field ${item.id}`;
              } catch (error) {
                itemName = `Field ${item.id}`;
              }
            } else {
              itemName = `Field ${item.id}`;
            }
          }

          // Ensure unique display preference - if missing or duplicate, assign based on index
          let displayPreference = item.displayPreference;
          if (!displayPreference || displayPreference <= 0) {
            displayPreference = (index + 1) * 10;
          }

          const processedItem = {
            id: item.id.toString(),
            name: itemName,
            displayPreference: displayPreference,
            type: isComponent ? 'Component' as FieldTypeEnum : (item.fieldType?.fieldTypeName || item.fieldTypeName || 'TEXT') as FieldTypeEnum,
            fieldTypeId: isComponent ? null : (item.fieldType?.id || item.fieldTypeId),
            additionalInformation: item.additionalInformation,
            isComponent: isComponent,
            componentId: isComponent ? item.component?.id : null,
          };

          return processedItem;
        });

        // Fix any duplicate display preferences
        const displayPrefs = updatedFields.map(f => f.displayPreference);
        const duplicatePrefs = displayPrefs.filter((pref, index) => displayPrefs.indexOf(pref) !== index);

        let finalFields = updatedFields;
        if (duplicatePrefs.length > 0) {
          // Fix duplicate display preferences by reassigning them
          finalFields = updatedFields.map((field, index) => ({
            ...field,
            displayPreference: (index + 1) * 10
          }));
        }

        // Sort by display preference to ensure correct order
        const sortedFields = [...finalFields].sort((a, b) => {
          const prefA = a.displayPreference || 0;
          const prefB = b.displayPreference || 0;
          return prefA - prefB;
        });

        setOrderedFields(sortedFields);

        // Fetch display names for the fields
        await fetchFieldDisplayNames(sortedFields);
      } else {
        setOrderedFields([]);
      }
    } catch (error) {
      console.error('Error fetching latest collection fields:', error);
      // Fall back to using the fields prop
      initializeFromProps();
    }
  }, [collectionId]);

  // Function to initialize from props (fallback)
  const initializeFromProps = useCallback(() => {
    if (!fields || fields.length === 0) {
      setOrderedFields([]);
      return;
    }

    // Filter out component fields - only show regular fields
    const regularFields = fields.filter(field => {
      return field.type !== FieldTypeEnum.COMPONENT;
    });

    // Sort by display preference and ensure proper format
    const sortedFields = [...regularFields].sort((a, b) => {
      const prefA = a.displayPreference || 0;
      const prefB = b.displayPreference || 0;
      return prefA - prefB;
    });

    // Ensure unique display preferences in 10, 20, 30 format
    const normalizedFields = sortedFields.map((field, index) => ({
      ...field,
      displayPreference: (index + 1) * 10,
    }));

    setOrderedFields(normalizedFields);

    // Fetch display names for the fields
    fetchFieldDisplayNames(normalizedFields);
  }, [fields]);

  // Initialize fields when dialog opens or fields change
  useEffect(() => {
    if (open) {
      console.log('Collection field display preferences dialog opened');
      fetchLatestData();
    }
  }, [open, fetchLatestData]);

  // Handle manual input of display preference
  const handleDisplayPreferenceChange = (index: number, value: string) => {
    const numValue = parseInt(value, 10);
    if (isNaN(numValue)) return;

    const newOrderedFields = [...orderedFields];
    newOrderedFields[index] = {
      ...newOrderedFields[index],
      displayPreference: numValue,
    };

    setOrderedFields(newOrderedFields);
  };

  // Save the updated display preferences
  const saveDisplayPreferences = async () => {
    if (!collectionId) {
      toast({
        title: 'Error',
        description: 'Collection ID is missing. Cannot save display preferences.',
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);

    try {
      // Sort fields by current display preference to maintain order
      const sortedFields = [...orderedFields].sort((a, b) => {
        const prefA = a.displayPreference || 0;
        const prefB = b.displayPreference || 0;
        return prefA - prefB;
      });

      // Ensure unique display preferences in 10, 20, 30 format
      const fieldsWithUniquePrefs = sortedFields.map((field, index) => ({
        ...field,
        displayPreference: (index + 1) * 10
      }));



      // Create update promises for each field and component
      const updatePromises = fieldsWithUniquePrefs.map((item) => {
        if (!item.id) {
          console.warn(`Item ${item.name} has no ID, skipping`);
          return null;
        }

        const itemId = item.id.toString();
        const displayPreference = item.displayPreference;

        // Use the appropriate API based on whether it's a component or field
        if (item.isComponent) {
          console.log(`🔧 FRONTEND - Updating COMPONENT ${itemId} display preference to ${displayPreference}`);
          return collectionComponentsApi.updateDisplayPreference(itemId, displayPreference)
            .then(response => {
              console.log(`✅ FRONTEND - Component ${itemId} update successful:`, response.data);
              return response;
            })
            .catch(error => {
              console.error(`❌ FRONTEND - Component ${itemId} update failed:`, error);
              throw error;
            });
        } else {
          console.log(`🔧 FRONTEND - Updating FIELD ${itemId} display preference to ${displayPreference}`);
          return collectionFieldsApi.updateDisplayPreference(itemId, displayPreference)
            .then(response => {
              console.log(`✅ FRONTEND - Field ${itemId} update successful:`, response.data);
              return response;
            })
            .catch(error => {
              console.error(`❌ FRONTEND - Field ${itemId} update failed:`, error);
              throw error;
            });
        }
      }).filter(promise => promise !== null);

      // Wait for all updates to complete
      await Promise.all(updatePromises);

      // Fetch the updated fields and components to get the latest data
      await fetchLatestData();

      // Add a timestamp to localStorage to force refresh when dialog reopens
      localStorage.setItem(`collection_field_display_prefs_updated_${collectionId}`, Date.now().toString());

      // Notify parent component that fields have been reordered
      onFieldsReordered();

      // Show success toast with details
      toast({
        title: 'Success',
        description: `Display preferences updated successfully for ${updatePromises.length} items`,
        variant: 'default',
      });

      // Close the dialog
      onOpenChange(false);
    } catch (error) {
      console.error('Error saving collection field display preferences:', error);
      toast({
        title: 'Error',
        description: 'Failed to save display preferences. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Edit Collection Display Preferences</DialogTitle>
          <DialogDescription>
            Change the order of collection fields and components by adjusting their display preferences. Lower numbers appear first.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <div className="grid grid-cols-12 gap-4 mb-2 text-sm font-medium text-muted-foreground">
            <div className="col-span-6">Name</div>
            <div className="col-span-3">Type</div>
            <div className="col-span-3 text-center">Display Order</div>
          </div>

          <div className="space-y-2 max-h-[400px] overflow-y-auto pr-2">
            {orderedFields.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No collection fields or components found. Add some fields or components first.
              </div>
            ) : (
              orderedFields
                .sort((a, b) => (a.displayPreference || 0) - (b.displayPreference || 0))
                .map((field, index) => (
                <div key={field.id} className="grid grid-cols-12 gap-4 items-center p-2 rounded-md bg-muted/20 border">
                  <div className="col-span-6 font-medium truncate" title={fieldDisplayNames[field.id] || field.name}>
                    {fieldDisplayNames[field.id] || field.name}
                  </div>
                  <div className="col-span-3">
                    {(() => {
                      // Handle components differently from fields
                      if (field.isComponent) {
                        return 'Component';
                      }

                      // Debug log to see what fieldTypeId we're getting
                      console.log(`Field ${field.name} has fieldTypeId: ${field.fieldTypeId}`);

                      // Map fieldTypeId to correct type name for display
                      if (field.fieldTypeId) {
                        switch (field.fieldTypeId) {
                          case 1: return 'Text';
                          case 2: return 'Number';
                          case 3: return 'Date';
                          case 4: return 'Image';
                          case 5: return 'Rich Text';
                          case 6: return 'Masked';
                          case 8: return 'Editor';
                          case 9: return 'Password';
                          case 10: return 'Autocomplete';
                          case 11: return 'Cascade Select';
                          case 12: return 'Dropdown';
                          case 13: return 'File';
                          case 14: return 'Multi-State Checkbox';
                          case 15: return 'Multi-Select';
                          case 16: return 'Multi-Select';
                          case 17: return 'Mention';
                          case 18: return 'Text Area Extended';
                          case 19: return 'OTP';
                          case 20: return 'Multi Checkbox';
                          case 21: return 'Radio Button';
                          case 22: return 'Input Switch';
                          case 23: return 'Dummy';
                          case 24: return 'API Details';
                          case 25: return 'Enumeration';
                          default: return field.type || 'Unknown';
                        }
                      }
                      return field.type || 'Unknown';
                    })()}
                  </div>
                  <div className="col-span-3">
                    <Input
                      type="number"
                      value={field.displayPreference || (index + 1) * 10}
                      onChange={(e) => handleDisplayPreferenceChange(index, e.target.value)}
                      className="h-8"
                      min="10"
                      step="10"
                    />
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={saveDisplayPreferences} disabled={loading || orderedFields.length === 0}>
            {loading ? 'Saving...' : 'Save Changes'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
