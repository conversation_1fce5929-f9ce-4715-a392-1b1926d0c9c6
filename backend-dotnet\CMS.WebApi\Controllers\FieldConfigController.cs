using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.Responses;
using CMS.WebApi.Services.Interfaces;
using CMS.WebApi.Security;

namespace CMS.WebApi.Controllers;

[ApiController]
[Route("api/field-configs")]
//[Authorize(Policy = AuthorizationPolicies.JwtOrApiToken)] // Temporarily disabled for testing
[Tags("Field Config Management")]
public class FieldConfigController : ControllerBase
{
    private readonly IFieldConfigService _fieldConfigService;
    private readonly ILogger<FieldConfigController> _logger;

    public FieldConfigController(IFieldConfigService fieldConfigService, ILogger<FieldConfigController> logger)
    {
        _fieldConfigService = fieldConfigService;
        _logger = logger;
    }

    /// <summary>
    /// Get all field configurations
    /// </summary>
    /// <returns>List of all field configurations</returns>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<FieldConfig>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<IEnumerable<FieldConfig>>> GetAllFieldConfigs()
    {
        try
        {
            var fieldConfigs = await _fieldConfigService.GetAllFieldConfigsAsync();
            
            if (!fieldConfigs.Any())
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = "No field configurations found",
                    Path = Request.Path
                });
            }

            _logger.LogInformation("Retrieved {Count} field configurations", fieldConfigs.Count());
            return Ok(fieldConfigs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve field configurations");
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving field configurations",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get field configuration by ID
    /// </summary>
    /// <param name="id">Field configuration ID</param>
    /// <returns>Field configuration details</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(FieldConfig), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<FieldConfig>> GetFieldConfigById(int id)
    {
        try
        {
            var fieldConfig = await _fieldConfigService.GetFieldConfigByIdAsync(id);
            
            if (fieldConfig == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Field configuration with ID {id} not found",
                    Path = Request.Path
                });
            }

            return Ok(fieldConfig);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve field configuration with ID: {FieldConfigId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving the field configuration",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get field configurations by field type (alternative endpoint for frontend compatibility)
    /// </summary>
    /// <param name="fieldTypeId">Field type ID</param>
    /// <returns>List of field configurations for the specified field type</returns>
    [HttpGet("getFieldTypeId/{fieldTypeId}")]
    [ProducesResponseType(typeof(IEnumerable<FieldConfig>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<IEnumerable<FieldConfig>>> GetFieldConfigsByFieldTypeId(int fieldTypeId)
    {
        try
        {
            var fieldConfigs = await _fieldConfigService.GetFieldConfigsByFieldTypeAsync(fieldTypeId);

            if (!fieldConfigs.Any())
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"No field configurations found for field type ID {fieldTypeId}",
                    Path = Request.Path
                });
            }

            _logger.LogInformation("Retrieved {Count} field configurations for field type {FieldTypeId}", fieldConfigs.Count(), fieldTypeId);
            return Ok(fieldConfigs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve field configurations for field type: {FieldTypeId}", fieldTypeId);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving field configurations",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get field configurations by field type (alternative endpoint matching frontend call)
    /// </summary>
    /// <param name="fieldTypeId">Field type ID</param>
    /// <returns>List of field configurations for the specified field type</returns>
    [HttpGet("getByFieldTypeId/{fieldTypeId}")]
    [ProducesResponseType(typeof(IEnumerable<FieldConfig>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<IEnumerable<FieldConfig>>> GetFieldConfigsByFieldTypeIdAlt(int fieldTypeId)
    {
        try
        {
            var fieldConfigs = await _fieldConfigService.GetFieldConfigsByFieldTypeAsync(fieldTypeId);

            if (!fieldConfigs.Any())
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"No field configurations found for field type ID {fieldTypeId}",
                    Path = Request.Path
                });
            }

            _logger.LogInformation("Retrieved {Count} field configurations for field type {FieldTypeId}", fieldConfigs.Count(), fieldTypeId);
            return Ok(fieldConfigs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve field configurations for field type: {FieldTypeId}", fieldTypeId);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving field configurations",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get field configurations by field type
    /// </summary>
    /// <param name="fieldTypeId">Field type ID</param>
    /// <returns>List of field configurations for the specified field type</returns>
    [HttpGet("by-field-type/{fieldTypeId}")]
    [ProducesResponseType(typeof(IEnumerable<FieldConfig>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<IEnumerable<FieldConfig>>> GetFieldConfigsByFieldType(int fieldTypeId)
    {
        try
        {
            var fieldConfigs = await _fieldConfigService.GetFieldConfigsByFieldTypeAsync(fieldTypeId);
            
            if (!fieldConfigs.Any())
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"No field configurations found for field type ID {fieldTypeId}",
                    Path = Request.Path
                });
            }

            _logger.LogInformation("Retrieved {Count} field configurations for field type {FieldTypeId}", fieldConfigs.Count(), fieldTypeId);
            return Ok(fieldConfigs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve field configurations for field type: {FieldTypeId}", fieldTypeId);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving field configurations",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Create a new field configuration
    /// </summary>
    /// <param name="fieldConfig">Field configuration details</param>
    /// <returns>Created field configuration</returns>
    [HttpPost]
    [ProducesResponseType(typeof(FieldConfig), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<FieldConfig>> CreateFieldConfig([FromBody] FieldConfig fieldConfig)
    {
        try
        {
            var createdFieldConfig = await _fieldConfigService.CreateFieldConfigAsync(fieldConfig);
            _logger.LogInformation("Field configuration created successfully: {FieldConfigId}", createdFieldConfig.Id);

            return CreatedAtAction(nameof(GetFieldConfigById), new { id = createdFieldConfig.Id }, createdFieldConfig);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create field configuration");
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while creating the field configuration",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Update an existing field configuration
    /// </summary>
    /// <param name="id">Field configuration ID</param>
    /// <param name="fieldConfig">Updated field configuration details</param>
    /// <returns>Updated field configuration</returns>
    [HttpPut("{id}")]
    [ProducesResponseType(typeof(FieldConfig), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<FieldConfig>> UpdateFieldConfig(int id, [FromBody] FieldConfig fieldConfig)
    {
        try
        {
            var updatedFieldConfig = await _fieldConfigService.UpdateFieldConfigAsync(id, fieldConfig);
            _logger.LogInformation("Field configuration updated successfully: {FieldConfigId}", id);
            return Ok(updatedFieldConfig);
        }
        catch (ArgumentException ex)
        {
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update field configuration: {FieldConfigId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while updating the field configuration",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Delete a field configuration
    /// </summary>
    /// <param name="id">Field configuration ID</param>
    /// <returns>No content</returns>
    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult> DeleteFieldConfig(int id)
    {
        try
        {
            var fieldConfig = await _fieldConfigService.GetFieldConfigByIdAsync(id);
            if (fieldConfig == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Field configuration with ID {id} not found",
                    Path = Request.Path
                });
            }

            await _fieldConfigService.DeleteFieldConfigAsync(id);
            _logger.LogInformation("Field configuration deleted successfully: {FieldConfigId}", id);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete field configuration: {FieldConfigId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while deleting the field configuration",
                Path = Request.Path
            });
        }
    }
}
