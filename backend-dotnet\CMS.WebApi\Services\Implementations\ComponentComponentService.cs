using Microsoft.EntityFrameworkCore;
using CMS.WebApi.Data;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.Requests;
using CMS.WebApi.Services.Interfaces;

namespace CMS.WebApi.Services.Implementations;

public class ComponentComponentService : IComponentComponentService
{
    private readonly CmsDbContext _context;
    private readonly ILogger<ComponentComponentService> _logger;

    public ComponentComponentService(CmsDbContext context, ILogger<ComponentComponentService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<IEnumerable<ComponentComponent>> GetAllComponentComponentsAsync()
    {
        _logger.LogInformation("Retrieving all component components");
        
        return await _context.ComponentComponents
            .Include(cc => cc.ParentComponent)
            .Include(cc => cc.ChildComponent)
            .ToListAsync();
    }

    public async Task<ComponentComponent?> GetComponentComponentByIdAsync(int id)
    {
        _logger.LogInformation("Retrieving component component with ID: {ComponentComponentId}", id);
        
        return await _context.ComponentComponents
            .Include(cc => cc.ParentComponent)
            .Include(cc => cc.ChildComponent)
            .FirstOrDefaultAsync(cc => cc.Id == id);
    }

    public async Task<IEnumerable<ComponentComponent>> GetComponentComponentsByParentIdAsync(int parentComponentId)
    {
        _logger.LogInformation("Retrieving component components for parent ID: {ParentComponentId}", parentComponentId);
        
        return await _context.ComponentComponents
            .Include(cc => cc.ParentComponent)
            .Include(cc => cc.ChildComponent)
            .Where(cc => cc.ParentComponentId == parentComponentId)
            .OrderBy(cc => cc.DisplayPreference)
            .ToListAsync();
    }

    public async Task<ComponentComponent> CreateComponentComponentAsync(CreateComponentComponentRequest request)
    {
        _logger.LogInformation("Creating component component: Parent={ParentId}, Child={ChildId}", 
            request.ParentComponentId, request.ChildComponentId);

        // Validate parent component exists
        var parentComponent = await _context.ComponentListings
            .FirstOrDefaultAsync(c => c.Id == request.ParentComponentId);
        if (parentComponent == null)
        {
            throw new ArgumentException($"Parent component with ID {request.ParentComponentId} not found");
        }

        // Validate child component exists
        var childComponent = await _context.ComponentListings
            .FirstOrDefaultAsync(c => c.Id == request.ChildComponentId);
        if (childComponent == null)
        {
            throw new ArgumentException($"Child component with ID {request.ChildComponentId} not found");
        }

        // Check for circular reference (component cannot be child of itself)
        if (request.ParentComponentId == request.ChildComponentId)
        {
            throw new InvalidOperationException("A component cannot be a child of itself");
        }

        // Check for deeper circular references
        await CheckForCircularReferencesAsync(request.ParentComponentId, request.ChildComponentId);

        // Set display preference if not provided
        var displayPreference = request.DisplayPreference;
        if (displayPreference == null)
        {
            var maxDisplayPreference = await _context.ComponentComponents
                .Where(cc => cc.ParentComponentId == request.ParentComponentId)
                .MaxAsync(cc => (int?)cc.DisplayPreference);
            displayPreference = (maxDisplayPreference ?? 0) + 10;
        }

        var componentComponent = new ComponentComponent
        {
            ParentComponentId = request.ParentComponentId,
            ChildComponentId = request.ChildComponentId,
            DisplayPreference = displayPreference,
            IsRepeatable = request.IsRepeatable,
            IsActive = request.IsActive,
            AdditionalInformation = request.AdditionalInformation,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = "system" // TODO: Get from current user context
        };

        _context.ComponentComponents.Add(componentComponent);
        await _context.SaveChangesAsync();

        _logger.LogInformation("Component component created with ID: {ComponentComponentId}", componentComponent.Id);

        // Return the created entity with navigation properties loaded
        return await GetComponentComponentByIdAsync(componentComponent.Id) 
            ?? throw new InvalidOperationException("Failed to retrieve created component component");
    }

    public async Task<ComponentComponent> UpdateComponentComponentAsync(int id, UpdateComponentComponentRequest request)
    {
        _logger.LogInformation("Updating component component with ID: {ComponentComponentId}", id);

        var existingComponent = await GetComponentComponentByIdAsync(id);
        if (existingComponent == null)
        {
            throw new ArgumentException($"Component component with ID {id} not found");
        }

        // Update child component if provided
        if (request.ChildComponentId.HasValue)
        {
            var childComponent = await _context.ComponentListings
                .FirstOrDefaultAsync(c => c.Id == request.ChildComponentId.Value);
            if (childComponent == null)
            {
                throw new ArgumentException($"Child component with ID {request.ChildComponentId.Value} not found");
            }

            // Check for circular reference if child component is changing
            if (existingComponent.ChildComponentId != request.ChildComponentId.Value)
            {
                if (existingComponent.ParentComponentId == request.ChildComponentId.Value)
                {
                    throw new InvalidOperationException("A component cannot be a child of itself");
                }

                await CheckForCircularReferencesAsync(existingComponent.ParentComponentId, request.ChildComponentId.Value);
            }

            existingComponent.ChildComponentId = request.ChildComponentId.Value;
        }

        // Update other fields if provided
        if (request.DisplayPreference.HasValue)
            existingComponent.DisplayPreference = request.DisplayPreference.Value;
        
        if (request.IsRepeatable.HasValue)
            existingComponent.IsRepeatable = request.IsRepeatable.Value;
        
        if (request.IsActive.HasValue)
            existingComponent.IsActive = request.IsActive.Value;
        
        if (request.AdditionalInformation != null)
            existingComponent.AdditionalInformation = request.AdditionalInformation;

        existingComponent.ModifiedAt = DateTime.UtcNow;
        existingComponent.ModifiedBy = "system"; // TODO: Get from current user context

        await _context.SaveChangesAsync();

        _logger.LogInformation("Component component updated: {ComponentComponentId}", id);

        return await GetComponentComponentByIdAsync(id) 
            ?? throw new InvalidOperationException("Failed to retrieve updated component component");
    }

    public async Task DeleteComponentComponentAsync(int id)
    {
        _logger.LogInformation("Deleting component component with ID: {ComponentComponentId}", id);

        var componentComponent = await _context.ComponentComponents.FindAsync(id);
        if (componentComponent == null)
        {
            throw new ArgumentException($"Component component with ID {id} not found");
        }

        _context.ComponentComponents.Remove(componentComponent);
        await _context.SaveChangesAsync();

        _logger.LogInformation("Component component deleted: {ComponentComponentId}", id);
    }

    public async Task ReorderComponentComponentsAsync(int parentComponentId, List<int> componentComponentIds)
    {
        _logger.LogInformation("Reordering component components for parent ID: {ParentComponentId}", parentComponentId);

        var componentComponents = await _context.ComponentComponents
            .Where(cc => cc.ParentComponentId == parentComponentId && componentComponentIds.Contains(cc.Id))
            .ToListAsync();

        for (int i = 0; i < componentComponentIds.Count; i++)
        {
            var componentComponent = componentComponents.FirstOrDefault(cc => cc.Id == componentComponentIds[i]);
            if (componentComponent != null)
            {
                componentComponent.DisplayPreference = (i + 1) * 10;
                componentComponent.ModifiedAt = DateTime.UtcNow;
                componentComponent.ModifiedBy = "system"; // TODO: Get from current user context
            }
        }

        await _context.SaveChangesAsync();

        _logger.LogInformation("Component components reordered for parent ID: {ParentComponentId}", parentComponentId);
    }

    private async Task CheckForCircularReferencesAsync(int parentComponentId, int childComponentId)
    {
        var visited = new HashSet<int>();
        await CheckForCircularReferencesRecursiveAsync(parentComponentId, childComponentId, visited);
    }

    private async Task CheckForCircularReferencesRecursiveAsync(int parentComponentId, int targetChildComponentId, HashSet<int> visited)
    {
        if (visited.Contains(targetChildComponentId))
        {
            throw new InvalidOperationException("Circular reference detected in component hierarchy");
        }

        visited.Add(targetChildComponentId);

        var childComponents = await _context.ComponentComponents
            .Where(cc => cc.ParentComponentId == targetChildComponentId)
            .Select(cc => cc.ChildComponentId)
            .ToListAsync();

        foreach (var childComponentId in childComponents)
        {
            if (childComponentId == parentComponentId)
            {
                throw new InvalidOperationException("Circular reference detected in component hierarchy");
            }

            await CheckForCircularReferencesRecursiveAsync(parentComponentId, childComponentId, visited);
        }

        visited.Remove(targetChildComponentId);
    }
}
