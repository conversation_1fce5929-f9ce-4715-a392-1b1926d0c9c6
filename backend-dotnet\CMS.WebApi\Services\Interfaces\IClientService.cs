using CMS.WebApi.Models.Entities;

namespace CMS.WebApi.Services.Interfaces;

public interface IClientService
{
    Task<IEnumerable<Client>> GetAllClientsAsync();
    Task<Client?> GetClientByIdAsync(int id);
    Task<Client?> GetClientByNameAsync(string name);
    Task<Client> CreateClientAsync(Client client);
    Task<Client> UpdateClientAsync(int id, Client client);
    Task DeleteClientAsync(int id);
    Task<bool> ClientExistsAsync(string name);
}
