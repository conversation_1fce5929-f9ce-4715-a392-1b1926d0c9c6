using System.ComponentModel.DataAnnotations;

namespace CMS.WebApi.Models.Entities;

public class CollectionFieldConfig : BaseEntity
{
    public int Id { get; set; }

    [Required]
    public int CollectionFieldId { get; set; }
    public CollectionField CollectionField { get; set; } = null!;

    [Required]
    public int FieldConfigId { get; set; }
    public FieldConfig FieldConfig { get; set; } = null!;

    [StringLength(255)]
    public string? ConfigValue { get; set; }

    public bool IsActive { get; set; } = true;
}
