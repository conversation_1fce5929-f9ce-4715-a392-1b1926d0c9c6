using Microsoft.EntityFrameworkCore;
using CMS.WebApi.Data;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Services.Interfaces;

namespace CMS.WebApi.Services.Implementations;

public class CollectionComponentService : ICollectionComponentService
{
    private readonly CmsDbContext _context;
    private readonly ICollectionOrderingService _collectionOrderingService;
    private readonly ILogger<CollectionComponentService> _logger;

    public CollectionComponentService(
        CmsDbContext context,
        ICollectionOrderingService collectionOrderingService,
        ILogger<CollectionComponentService> logger)
    {
        _context = context;
        _collectionOrderingService = collectionOrderingService;
        _logger = logger;
    }

    public async Task<CollectionComponent?> GetCollectionComponentByIdAsync(int id)
    {
        return await _context.CollectionComponents
            .Include(cc => cc.Collection)
            .Include(cc => cc.Component)
            .FirstOrDefaultAsync(cc => cc.Id == id);
    }

    public async Task<IEnumerable<CollectionComponent>> GetAllCollectionComponentsAsync()
    {
        return await _context.CollectionComponents
            .Include(cc => cc.Collection)
            .Include(cc => cc.Component)
            .OrderBy(cc => cc.CollectionId)
            .ThenBy(cc => cc.DisplayPreference)
            .ToListAsync();
    }

    public async Task<IEnumerable<CollectionComponent>> GetCollectionComponentsByCollectionIdAsync(int collectionId)
    {
        _logger.LogInformation("Getting collection components for collection ID: {CollectionId}", collectionId);

        // Verify collection exists
        var collectionExists = await _context.CollectionListings
            .AnyAsync(c => c.Id == collectionId);

        _logger.LogInformation("Collection exists check for ID {CollectionId}: {Exists}", collectionId, collectionExists);

        if (!collectionExists)
        {
            throw new ArgumentException($"Collection not found with id: {collectionId}");
        }

        // Get all collection components for debugging
        var allComponents = await _context.CollectionComponents.ToListAsync();
        _logger.LogInformation("Total collection components in database: {Count}", allComponents.Count);

        foreach (var comp in allComponents)
        {
            _logger.LogInformation("Found component: ID={Id}, CollectionId={CollectionId}, ComponentId={ComponentId}",
                comp.Id, comp.CollectionId, comp.ComponentId);
        }

        var components = await _context.CollectionComponents
            .Include(cc => cc.Component)
            .Where(cc => cc.CollectionId == collectionId)
            .OrderBy(cc => cc.DisplayPreference ?? int.MaxValue)
            .ToListAsync();

        _logger.LogInformation("Found {Count} collection components for collection ID: {CollectionId}",
            components.Count, collectionId);

        return components;
    }

    public async Task<CollectionComponent> CreateCollectionComponentAsync(CollectionComponent collectionComponent)
    {
        // Validate collection
        if (collectionComponent.CollectionId <= 0)
        {
            throw new ArgumentException("Collection is required");
        }

        var collection = await _context.CollectionListings
            .FirstOrDefaultAsync(c => c.Id == collectionComponent.CollectionId);

        if (collection == null)
        {
            throw new ArgumentException($"Collection with ID {collectionComponent.CollectionId} not found");
        }

        // Validate component
        if (collectionComponent.ComponentId <= 0)
        {
            throw new ArgumentException("Component is required");
        }

        var component = await _context.ComponentListings
            .FirstOrDefaultAsync(c => c.Id == collectionComponent.ComponentId);

        if (component == null)
        {
            throw new ArgumentException($"Component with ID {collectionComponent.ComponentId} not found");
        }

        // Set display preference to be the max value for this collection + 10
        var nextDisplayPreference = await _collectionOrderingService.GetNextDisplayPreferenceAsync(collectionComponent.CollectionId);
        collectionComponent.DisplayPreference = nextDisplayPreference;

        _logger.LogDebug("Setting display preference to {DisplayPreference} for collection {CollectionId}",
            collectionComponent.DisplayPreference, collectionComponent.CollectionId);

        // IsActive is already set with a default value of true in the entity

        // Set audit fields
        collectionComponent.CreatedAt = DateTime.UtcNow;

        _context.CollectionComponents.Add(collectionComponent);
        await _context.SaveChangesAsync();

        _logger.LogInformation("Collection component created successfully: ID {Id}, Collection {CollectionId}, Component {ComponentId}",
            collectionComponent.Id, collectionComponent.CollectionId, collectionComponent.ComponentId);

        return collectionComponent;
    }

    public async Task<CollectionComponent> UpdateCollectionComponentAsync(int id, CollectionComponent collectionComponent)
    {
        var existingComponent = await _context.CollectionComponents
            .FirstOrDefaultAsync(cc => cc.Id == id);

        if (existingComponent == null)
        {
            throw new ArgumentException($"Collection component with ID {id} not found");
        }

        // Update properties
        existingComponent.ComponentId = collectionComponent.ComponentId;
        existingComponent.DisplayPreference = collectionComponent.DisplayPreference;
        existingComponent.IsRepeatable = collectionComponent.IsRepeatable;
        existingComponent.MinRepeatOccurrences = collectionComponent.MinRepeatOccurrences;
        existingComponent.MaxRepeatOccurrences = collectionComponent.MaxRepeatOccurrences;
        existingComponent.IsActive = collectionComponent.IsActive;
        existingComponent.Name = collectionComponent.Name;
        existingComponent.DisplayName = collectionComponent.DisplayName;
        existingComponent.AdditionalInfo = collectionComponent.AdditionalInfo;
        existingComponent.AdditionalInfoImage = collectionComponent.AdditionalInfoImage;
        existingComponent.ModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        _logger.LogInformation("Collection component updated successfully: ID {Id}", id);

        return existingComponent;
    }

    public async Task DeleteCollectionComponentAsync(int id)
    {
        var component = await _context.CollectionComponents
            .FirstOrDefaultAsync(cc => cc.Id == id);

        if (component == null)
        {
            throw new ArgumentException($"Collection component with ID {id} not found");
        }

        _context.CollectionComponents.Remove(component);
        await _context.SaveChangesAsync();

        _logger.LogInformation("Collection component deleted successfully: ID {Id}", id);
    }

    public async Task<int> GetNextAvailableIdAsync()
    {
        // Get the maximum ID currently in use
        var maxId = await _context.CollectionComponents
            .MaxAsync(cc => (int?)cc.Id);

        // If no records exist, start with ID 1, otherwise use max + 1
        return maxId.HasValue ? maxId.Value + 1 : 1;
    }

    public async Task ReorderCollectionComponentsAsync(int collectionId, List<int> componentIds)
    {
        // Verify collection exists
        var collectionExists = await _context.CollectionListings
            .AnyAsync(c => c.Id == collectionId);

        if (!collectionExists)
        {
            throw new ArgumentException($"Collection not found with id: {collectionId}");
        }

        // Get all components for this collection
        var components = await _context.CollectionComponents
            .Where(cc => cc.CollectionId == collectionId && componentIds.Contains(cc.Id))
            .ToListAsync();

        // Update display preferences based on the order in componentIds
        for (int i = 0; i < componentIds.Count; i++)
        {
            var component = components.FirstOrDefault(c => c.Id == componentIds[i]);
            if (component != null)
            {
                component.DisplayPreference = (i + 1) * 10; // 10, 20, 30, etc.
                component.ModifiedAt = DateTime.UtcNow;
            }
        }

        await _context.SaveChangesAsync();

        _logger.LogInformation("Reordered {Count} collection components for collection {CollectionId}",
            componentIds.Count, collectionId);
    }

    public async Task<CollectionComponent> UpdateDisplayPreferenceAsync(int id, int displayPreference)
    {
        var component = await _context.CollectionComponents
            .FirstOrDefaultAsync(cc => cc.Id == id);

        if (component == null)
        {
            throw new ArgumentException($"Collection component with ID {id} not found");
        }

        component.DisplayPreference = displayPreference;
        component.ModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        _logger.LogInformation("Display preference updated for collection component {Id}: {DisplayPreference}", id, displayPreference);

        return component;
    }
}
