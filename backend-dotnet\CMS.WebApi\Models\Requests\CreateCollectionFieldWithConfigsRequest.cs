using System.ComponentModel.DataAnnotations;

namespace CMS.WebApi.Models.Requests;

public class CreateCollectionFieldWithConfigsRequest
{
    [Required]
    public int CollectionId { get; set; }

    [Required]
    public int FieldTypeId { get; set; }

    public int? DisplayPreference { get; set; }

    public int? DependentOnId { get; set; }

    public string? AdditionalInformation { get; set; }

    public List<CollectionFieldConfigRequest> Configurations { get; set; } = new();
}

public class CollectionFieldConfigRequest
{
    [Required]
    public int FieldConfigId { get; set; }

    public string? ConfigValue { get; set; }

    public bool IsActive { get; set; } = true;
}
