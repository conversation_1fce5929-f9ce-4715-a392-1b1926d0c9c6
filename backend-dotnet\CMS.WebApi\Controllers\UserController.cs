using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.Requests;
using CMS.WebApi.Models.Responses;
using CMS.WebApi.Services.Interfaces;
using CMS.WebApi.Security;
using System.Security.Claims;

namespace CMS.WebApi.Controllers;

[ApiController]
[Route("api/users")]
[Authorize(Policy = AuthorizationPolicies.JwtOrApiToken)]
[Tags("User Management")]
public class UserController : ControllerBase
{
    private readonly IUserService _userService;
    private readonly UserManager<User> _userManager;
    private readonly ILogger<UserController> _logger;

    public UserController(
        IUserService userService,
        UserManager<User> userManager,
        ILogger<UserController> logger)
    {
        _userService = userService;
        _userManager = userManager;
        _logger = logger;
    }

    /// <summary>
    /// Get all users
    /// </summary>
    /// <returns>List of all users</returns>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<UserProfileResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<IEnumerable<UserProfileResponse>>> GetAllUsers()
    {
        try
        {
            var users = await _userService.GetAllUsersAsync();

            var response = users.Select(user => new UserProfileResponse
            {
                Id = user.Id,
                Username = user.UserName!,
                Email = user.Email!,
                FirstName = null, // User entity doesn't have FirstName
                LastName = null,  // User entity doesn't have LastName
                IsActive = user.IsActive,
                CreatedAt = user.CreatedAt,
                UpdatedAt = user.ModifiedAt // Use ModifiedAt instead of UpdatedAt
            });

            _logger.LogInformation("Retrieved {Count} users", users.Count());
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve users");
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving users",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get current user profile
    /// </summary>
    /// <returns>Current user profile</returns>
    [HttpGet("profile")]
    [ProducesResponseType(typeof(UserProfileResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<UserProfileResponse>> GetProfile()
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized(new ErrorResponse
                {
                    Status = 401,
                    Error = "Unauthorized",
                    Message = "User ID not found in token",
                    Path = Request.Path
                });
            }

            var user = await _userService.GetUserByIdAsync(long.Parse(userId));
            if (user == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = "User not found",
                    Path = Request.Path
                });
            }

            var response = new UserProfileResponse
            {
                Id = user.Id,
                Username = user.UserName!,
                Email = user.Email!,
                FirstName = null, // User entity doesn't have FirstName
                LastName = null,  // User entity doesn't have LastName
                IsActive = user.IsActive,
                CreatedAt = user.CreatedAt,
                UpdatedAt = user.ModifiedAt // Use ModifiedAt instead of UpdatedAt
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get user profile for user ID: {UserId}", User.FindFirst(ClaimTypes.NameIdentifier)?.Value);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving the user profile",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Update current user profile
    /// </summary>
    /// <param name="request">Updated profile information</param>
    /// <returns>Updated user profile</returns>
    [HttpPut("profile")]
    [ProducesResponseType(typeof(UserProfileResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<UserProfileResponse>> UpdateProfile([FromBody] UpdateUserProfileRequest request)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized(new ErrorResponse
                {
                    Status = 401,
                    Error = "Unauthorized",
                    Message = "User ID not found in token",
                    Path = Request.Path
                });
            }

            var user = await _userService.GetUserByIdAsync(long.Parse(userId));
            if (user == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = "User not found",
                    Path = Request.Path
                });
            }

            // Update user properties (User entity has Email and UserName)
            user.Email = request.Email ?? user.Email;
            user.UserName = request.Username ?? user.UserName;
            user.ModifiedAt = DateTime.UtcNow;
            user.ModifiedBy = user.UserName; // Track who modified

            var result = await _userManager.UpdateAsync(user);
            if (!result.Succeeded)
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = "Failed to update user profile",
                    Path = Request.Path,
                    ValidationErrors = result.Errors.ToDictionary(e => e.Code, e => new[] { e.Description })
                });
            }

            var response = new UserProfileResponse
            {
                Id = user.Id,
                Username = user.UserName!,
                Email = user.Email!,
                FirstName = null, // User entity doesn't have FirstName
                LastName = null,  // User entity doesn't have LastName
                IsActive = user.IsActive,
                CreatedAt = user.CreatedAt,
                UpdatedAt = user.ModifiedAt // Use ModifiedAt instead of UpdatedAt
            };

            _logger.LogInformation("User profile updated successfully for user ID: {UserId}", userId);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update user profile for user ID: {UserId}", User.FindFirst(ClaimTypes.NameIdentifier)?.Value);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while updating the user profile",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Change user password
    /// </summary>
    /// <param name="request">Password change request</param>
    /// <returns>Success message</returns>
    [HttpPost("change-password")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult> ChangePassword([FromBody] ChangePasswordRequest request)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized(new ErrorResponse
                {
                    Status = 401,
                    Error = "Unauthorized",
                    Message = "User ID not found in token",
                    Path = Request.Path
                });
            }

            var user = await _userService.GetUserByIdAsync(long.Parse(userId));
            if (user == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = "User not found",
                    Path = Request.Path
                });
            }

            var result = await _userManager.ChangePasswordAsync(user, request.CurrentPassword, request.NewPassword);
            if (!result.Succeeded)
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = "Failed to change password",
                    Path = Request.Path,
                    ValidationErrors = result.Errors.ToDictionary(e => e.Code, e => new[] { e.Description })
                });
            }

            _logger.LogInformation("Password changed successfully for user ID: {UserId}", userId);
            return Ok(new { Message = "Password changed successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to change password for user ID: {UserId}", User.FindFirst(ClaimTypes.NameIdentifier)?.Value);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while changing the password",
                Path = Request.Path
            });
        }
    }
}
