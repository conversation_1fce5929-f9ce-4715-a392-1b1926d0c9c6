using System.ComponentModel.DataAnnotations;

namespace CMS.WebApi.Models.Entities;

public class Category : BaseEntity
{
    public int Id { get; set; }

    [Required(ErrorMessage = "Category name is required")]
    [StringLength(255)]
    public string CategoryName { get; set; } = string.Empty;

    public int? ClientId { get; set; }
    public Client? Client { get; set; }

    // Self-referencing relationship for parent category
    public int? ParentCategoryId { get; set; }
    public Category? ParentCategory { get; set; }

    public ICollection<Category> ChildCategories { get; set; } = new List<Category>();
    public ICollection<CollectionListing> Collections { get; set; } = new List<CollectionListing>();
    public ICollection<ComponentListing> Components { get; set; } = new List<ComponentListing>();
}
