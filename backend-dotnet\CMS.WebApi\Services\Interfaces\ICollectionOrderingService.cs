using CMS.WebApi.Models.Entities;

namespace CMS.WebApi.Services.Interfaces;

public interface ICollectionOrderingService
{
    /// <summary>
    /// Get the maximum display preference value for a collection
    /// considering both components and fields
    /// </summary>
    /// <param name="collectionId">The collection ID</param>
    /// <returns>The maximum display preference value</returns>
    Task<int> GetMaxDisplayPreferenceAsync(int collectionId);

    /// <summary>
    /// Get all components and fields for a collection in display order
    /// </summary>
    /// <param name="collectionId">The collection ID</param>
    /// <returns>Object containing ordered components and fields</returns>
    Task<object> GetOrderedCollectionItemsAsync(int collectionId);

    /// <summary>
    /// Reorder components and fields for a collection
    /// </summary>
    /// <param name="collectionId">The collection ID</param>
    /// <param name="componentIds">List of component IDs in display order</param>
    /// <param name="fieldIds">List of field IDs in display order</param>
    /// <returns>Object containing updated components and fields</returns>
    Task<object> ReorderCollectionItemsAsync(int collectionId, List<int> componentIds, List<int> fieldIds);

    /// <summary>
    /// Get the next display preference value for a collection
    /// </summary>
    /// <param name="collectionId">The collection ID</param>
    /// <returns>The next display preference value</returns>
    Task<int> GetNextDisplayPreferenceAsync(int collectionId);

    /// <summary>
    /// Get all components for a collection in display order
    /// </summary>
    /// <param name="collectionId">The collection ID</param>
    /// <returns>List of components in display order</returns>
    Task<IEnumerable<CollectionComponent>> GetOrderedComponentsAsync(int collectionId);

    /// <summary>
    /// Get all fields for a collection in display order
    /// </summary>
    /// <param name="collectionId">The collection ID</param>
    /// <returns>List of fields in display order</returns>
    Task<IEnumerable<CollectionField>> GetOrderedFieldsAsync(int collectionId);
}
