using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.Responses;
using CMS.WebApi.Models.Requests;
using CMS.WebApi.Services.Interfaces;
using CMS.WebApi.Security;

namespace CMS.WebApi.Controllers;

[ApiController]
[Route("api/collection-fields")]
[Authorize(Policy = AuthorizationPolicies.JwtOrApiToken)]
[Tags("Collection Field")]
public class CollectionFieldController : ControllerBase
{
    private readonly ICollectionFieldService _collectionFieldService;
    private readonly ILogger<CollectionFieldController> _logger;

    public CollectionFieldController(
        ICollectionFieldService collectionFieldService,
        ILogger<CollectionFieldController> logger)
    {
        _collectionFieldService = collectionFieldService;
        _logger = logger;
    }

    /// <summary>
    /// Get all collection fields
    /// </summary>
    /// <returns>List of all collection fields</returns>
    [HttpGet("getAll")]
    [ProducesResponseType(typeof(IEnumerable<CollectionField>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(object), StatusCodes.Status204NoContent)]
    public async Task<ActionResult<IEnumerable<CollectionField>>> GetAllCollectionFields()
    {
        try
        {
            var fields = await _collectionFieldService.GetAllCollectionFieldsAsync();
            
            if (!fields.Any())
            {
                return NoContent();
            }

            return Ok(fields);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get all collection fields");
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving collection fields",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get collection field by ID
    /// </summary>
    /// <param name="id">Collection field ID</param>
    /// <returns>Collection field</returns>
    [HttpGet("getById/{id}")]
    [ProducesResponseType(typeof(CollectionField), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<CollectionField>> GetCollectionFieldById(int id)
    {
        try
        {
            var field = await _collectionFieldService.GetCollectionFieldByIdAsync(id);
            
            if (field == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Collection field with ID {id} not found",
                    Path = Request.Path
                });
            }

            return Ok(field);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get collection field by ID: {Id}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving the collection field",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get collection fields by collection ID
    /// </summary>
    /// <param name="collectionId">Collection ID</param>
    /// <returns>List of collection fields for the collection</returns>
    [HttpGet("getByCollectionId/{collectionId}")]
    [ProducesResponseType(typeof(IEnumerable<CollectionFieldResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(object), StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<IEnumerable<CollectionFieldResponse>>> GetCollectionFieldsByCollectionId(int collectionId)
    {
        try
        {
            var fields = await _collectionFieldService.GetCollectionFieldsByCollectionIdAsync(collectionId);

            if (!fields.Any())
            {
                return NoContent();
            }

            // Convert to DTOs to avoid circular reference issues
            var fieldDTOs = fields.Select(CollectionFieldResponse.FromEntity).ToList();
            return Ok(fieldDTOs);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Collection not found: {CollectionId}", collectionId);
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get collection fields for collection: {CollectionId}", collectionId);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving collection fields",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get next available ID for collection field
    /// </summary>
    /// <returns>Next available ID</returns>
    [HttpGet("getNextId")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    public async Task<ActionResult<object>> GetNextId()
    {
        try
        {
            var nextId = await _collectionFieldService.GetNextAvailableIdAsync();
            return Ok(new { nextId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get next available ID for collection field");
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while getting next available ID",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Create a new collection field
    /// </summary>
    /// <param name="request">Collection field details</param>
    /// <returns>Created collection field</returns>
    [HttpPost("create")]
    [ProducesResponseType(typeof(CollectionField), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<CollectionField>> CreateCollectionField([FromBody] CollectionFieldCreateRequest request)
    {
        try
        {
            // Map request to entity
            var collectionField = new CollectionField
            {
                CollectionId = request.CollectionId > 0 ? request.CollectionId : request.Collection?.Id ?? 0,
                FieldTypeId = request.FieldTypeId > 0 ? request.FieldTypeId : request.FieldType?.Id ?? 0,
                DisplayPreference = request.DisplayPreference,
                DependentOnId = request.DependentOnId,
                AdditionalInformation = request.AdditionalInformation
            };

            // Validate that we have required IDs
            if (collectionField.CollectionId <= 0)
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = "Collection ID is required",
                    Path = Request.Path
                });
            }

            if (collectionField.FieldTypeId <= 0)
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = "Field Type ID is required",
                    Path = Request.Path
                });
            }

            var createdField = await _collectionFieldService.CreateCollectionFieldAsync(collectionField);

            _logger.LogInformation("Collection field created successfully: {Id}", createdField.Id);

            return CreatedAtAction(
                nameof(GetCollectionFieldById),
                new { id = createdField.Id },
                createdField);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid collection field data");
            return BadRequest(new ErrorResponse
            {
                Status = 400,
                Error = "Bad Request",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create collection field");
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while creating the collection field",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Create a new collection field with configurations
    /// </summary>
    /// <param name="request">Collection field details with configurations</param>
    /// <returns>Created collection field with configurations</returns>
    [HttpPost("create-with-configs")]
    [ProducesResponseType(typeof(CollectionField), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<CollectionField>> CreateCollectionFieldWithConfigs([FromBody] CreateCollectionFieldWithConfigsRequest request)
    {
        try
        {
            _logger.LogInformation("Creating collection field with {ConfigCount} configurations for collection ID: {CollectionId}",
                request.Configurations.Count, request.CollectionId);

            var createdField = await _collectionFieldService.CreateCollectionFieldWithConfigsAsync(request);

            _logger.LogInformation("Collection field with configurations created successfully: {Id}", createdField.Id);

            return CreatedAtAction(
                nameof(GetCollectionFieldById),
                new { id = createdField.Id },
                createdField);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid collection field data with configurations");
            return BadRequest(new ErrorResponse
            {
                Status = 400,
                Error = "Bad Request",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create collection field with configurations");
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while creating the collection field with configurations",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Update an existing collection field
    /// </summary>
    /// <param name="id">Collection field ID</param>
    /// <param name="collectionField">Updated collection field data</param>
    /// <returns>Updated collection field</returns>
    [HttpPut("update/{id}")]
    [ProducesResponseType(typeof(CollectionField), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<CollectionField>> UpdateCollectionField(int id, [FromBody] UpdateCollectionFieldRequest request)
    {
        try
        {
            _logger.LogInformation("Updating collection field ID: {Id}", id);
            _logger.LogInformation("Request details - CollectionId: {CollectionId}, FieldTypeId: {FieldTypeId}, ConfigCount: {ConfigCount}",
                request.CollectionId, request.FieldTypeId, request.Configs.Count);
            _logger.LogDebug("Full update request: {@Request}", request);

            // Log each configuration for debugging
            if (request.Configs.Any())
            {
                _logger.LogInformation("Received {ConfigCount} configurations:", request.Configs.Count);
                for (int i = 0; i < request.Configs.Count; i++)
                {
                    var config = request.Configs[i];
                    _logger.LogInformation("  Config {Index}: FieldConfigId={FieldConfigId}, Value='{Value}', IsActive={IsActive}",
                        i + 1, config.FieldConfigId, config.ConfigValue, config.IsActive);
                }
            }

            // Map request to entity, handling both .NET and Java backend formats
            var collectionField = new CollectionField
            {
                Id = id,
                CollectionId = request.CollectionId > 0 ? request.CollectionId : request.Collection?.Id ?? 0,
                FieldTypeId = request.FieldTypeId > 0 ? request.FieldTypeId : request.FieldType?.Id ?? 0,
                DisplayPreference = request.DisplayPreference,
                DependentOnId = request.DependentOnId,
                AdditionalInformation = request.AdditionalInformation
            };

            // Validate required fields
            if (collectionField.CollectionId <= 0)
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = "Collection ID is required",
                    Path = Request.Path
                });
            }

            if (collectionField.FieldTypeId <= 0)
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = "Field Type ID is required",
                    Path = Request.Path
                });
            }

            CollectionField updatedField;

            // Use the appropriate service method based on whether configurations are provided
            if (request.Configs.Any())
            {
                _logger.LogInformation("Updating collection field with {ConfigCount} configurations", request.Configs.Count);
                updatedField = await _collectionFieldService.UpdateCollectionFieldWithConfigsAsync(id, request);
            }
            else
            {
                _logger.LogInformation("Updating collection field without configurations");
                updatedField = await _collectionFieldService.UpdateCollectionFieldAsync(id, collectionField);
            }

            _logger.LogInformation("Collection field updated successfully: {Id}", id);

            return Ok(updatedField);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Collection field not found or invalid data: {Id}", id);

            if (ex.Message.Contains("not found"))
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = ex.Message,
                    Path = Request.Path
                });
            }

            return BadRequest(new ErrorResponse
            {
                Status = 400,
                Error = "Bad Request",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update collection field: {Id}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while updating the collection field",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Update an existing collection field with configurations
    /// </summary>
    /// <param name="id">Collection field ID</param>
    /// <param name="request">Updated collection field data with configurations</param>
    /// <returns>Updated collection field with configurations</returns>
    [HttpPut("update-with-configs/{id}")]
    [ProducesResponseType(typeof(CollectionField), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<CollectionField>> UpdateCollectionFieldWithConfigs(int id, [FromBody] UpdateCollectionFieldRequest request)
    {
        try
        {
            _logger.LogInformation("Updating collection field with configurations: ID {Id}, ConfigCount: {ConfigCount}",
                id, request.Configs.Count);

            var updatedField = await _collectionFieldService.UpdateCollectionFieldWithConfigsAsync(id, request);

            _logger.LogInformation("Collection field with configurations updated successfully: {Id}", id);

            return Ok(updatedField);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Collection field not found or invalid data: {Id}", id);

            if (ex.Message.Contains("not found"))
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = ex.Message,
                    Path = Request.Path
                });
            }

            return BadRequest(new ErrorResponse
            {
                Status = 400,
                Error = "Bad Request",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update collection field with configurations: {Id}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while updating the collection field with configurations",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Delete a collection field
    /// </summary>
    /// <param name="id">Collection field ID</param>
    /// <returns>Success response</returns>
    [HttpDelete("delete/{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult> DeleteCollectionField(int id)
    {
        try
        {
            await _collectionFieldService.DeleteCollectionFieldAsync(id);
            
            _logger.LogInformation("Collection field deleted successfully: {Id}", id);
            
            return NoContent();
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Collection field not found: {Id}", id);
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete collection field: {Id}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while deleting the collection field",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Update display preference for collection field
    /// </summary>
    /// <param name="id">Collection field ID</param>
    /// <param name="request">Display preference update request</param>
    /// <returns>Updated collection field</returns>
    [HttpPut("updateDisplayPreference/{id}")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<object>> UpdateDisplayPreference(int id, [FromBody] DisplayPreferenceRequest request)
    {
        try
        {
            _logger.LogInformation("Display preference update requested for collection field ID: {FieldId} with preference: {DisplayPreference}",
                id, request.DisplayPreference);

            var updatedField = await _collectionFieldService.UpdateDisplayPreferenceAsync(id, request.DisplayPreference);

            _logger.LogInformation("Successfully updated display preference for collection field {FieldId}", id);

            return Ok(new
            {
                success = true,
                message = "Display preference updated successfully",
                data = new
                {
                    id = updatedField.Id,
                    displayPreference = updatedField.DisplayPreference,
                    modifiedAt = updatedField.ModifiedAt
                }
            });
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Collection field not found: {FieldId}", id);
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating display preference for collection field: {FieldId}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while updating display preference",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Delete a collection field (Java backend compatible endpoint)
    /// </summary>
    /// <param name="id">Collection field ID</param>
    /// <returns>Success response with message</returns>
    [HttpDelete("deleteById/{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult> DeleteCollectionFieldById(int id)
    {
        try
        {
            _logger.LogInformation("Collection field deletion requested for ID: {Id}", id);

            // Check if the collection field exists first
            var existingField = await _collectionFieldService.GetCollectionFieldByIdAsync(id);
            if (existingField == null)
            {
                _logger.LogInformation("No collection field found with ID: {Id}", id);
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"No collection field found with id: {id}",
                    Path = Request.Path
                });
            }

            // Delete the collection field
            await _collectionFieldService.DeleteCollectionFieldAsync(id);

            _logger.LogInformation("Collection field deleted successfully: {Id}", id);

            // Return 204 No Content (proper HTTP response for successful deletion)
            return NoContent();
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Collection field not found: {Id}", id);
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = $"No collection field found with id: {id}",
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete collection field: {Id}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = $"Failed to process delete request for collection field with id: {id}",
                Path = Request.Path
            });
        }
    }
}
