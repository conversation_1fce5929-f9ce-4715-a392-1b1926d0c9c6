using System.ComponentModel.DataAnnotations;

namespace CMS.WebApi.Models.Entities;

public class ComponentFieldConfig : BaseEntity
{
    public int Id { get; set; }

    [Required]
    public int ComponentFieldId { get; set; }
    public ComponentField ComponentField { get; set; } = null!;

    [Required]
    public int FieldConfigId { get; set; }
    public FieldConfig FieldConfig { get; set; } = null!;

    [StringLength(255)]
    public string? ConfigValue { get; set; }

    public bool IsActive { get; set; } = true;
}
