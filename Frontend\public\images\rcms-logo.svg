<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 500 500">
  <path d="M250 50C137.157 50 46.6 140.557 46.6 253.4c0 112.843 90.557 203.4 203.4 203.4 112.843 0 203.4-90.557 203.4-203.4C453.4 140.557 362.843 50 250 50z" fill="#fff"/>
  <path d="M100 150c-38.66 0-70 31.34-70 70s31.34 70 70 70c25.463 0 47.745-13.614 60-34.033C172.255 276.386 194.537 290 220 290c38.66 0 70-31.34 70-70s-31.34-70-70-70c-25.463 0-47.745 13.614-60 34.033C147.745 163.614 125.463 150 100 150z" fill="url(#blue_gradient)"/>
  <path d="M400 150c-38.66 0-70 31.34-70 70s31.34 70 70 70c25.463 0 47.745-13.614 60-34.033C472.255 276.386 494.537 290 520 290c38.66 0 70-31.34 70-70s-31.34-70-70-70c-25.463 0-47.745 13.614-60 34.033C447.745 163.614 425.463 150 400 150z" fill="url(#red_gradient)" transform="translate(-150, 0)"/>
  <circle cx="160" cy="220" r="15" fill="#fff"/>
  <circle cx="340" cy="220" r="15" fill="#fff"/>
  <defs>
    <linearGradient id="blue_gradient" x1="50" y1="150" x2="250" y2="290" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0066FF"/>
      <stop offset="1" stop-color="#0099FF"/>
    </linearGradient>
    <linearGradient id="red_gradient" x1="350" y1="150" x2="550" y2="290" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FF0066"/>
      <stop offset="1" stop-color="#FF3366"/>
    </linearGradient>
  </defs>
</svg>
