using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.Responses;
using CMS.WebApi.Models.Requests;
using CMS.WebApi.Services.Interfaces;
using CMS.WebApi.Security;
using System.Security.Claims;

namespace CMS.WebApi.Controllers;

[ApiController]
[Route("api/api-tokens")]
[Authorize(Policy = AuthorizationPolicies.JwtOrApiToken)]
[Tags("API Token Management")]
public class ApiTokenController : ControllerBase
{
    private readonly IApiTokenService _apiTokenService;
    private readonly ILogger<ApiTokenController> _logger;

    public ApiTokenController(IApiTokenService apiTokenService, ILogger<ApiTokenController> logger)
    {
        _apiTokenService = apiTokenService;
        _logger = logger;
    }

    /// <summary>
    /// Get all API tokens for the current user
    /// </summary>
    /// <returns>List of API tokens</returns>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<ApiTokenResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<IEnumerable<ApiTokenResponse>>> GetUserApiTokens()
    {
        try
        {
            var userId = GetCurrentUserId();
            var apiTokens = await _apiTokenService.GetUserApiTokensAsync(userId);
            
            if (!apiTokens.Any())
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = "No API tokens found for the current user",
                    Path = Request.Path
                });
            }

            var response = apiTokens.Select(token => new ApiTokenResponse
            {
                Id = token.Id,
                Name = token.Name,
                Description = token.Description,
                ExpiresAt = token.ExpiresAt,
                LastUsedAt = token.LastUsedAt,
                IsActive = token.IsActive,
                CreatedAt = token.CreatedAt
            });

            _logger.LogInformation("Retrieved {Count} API tokens for user {UserId}", apiTokens.Count(), userId);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve API tokens");
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving API tokens",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get API token by ID
    /// </summary>
    /// <param name="id">API token ID</param>
    /// <returns>API token details</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(ApiTokenResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ApiTokenResponse>> GetApiTokenById(long id)
    {
        try
        {
            var userId = GetCurrentUserId();
            var apiToken = await _apiTokenService.GetApiTokenByIdAsync(id, userId);
            
            if (apiToken == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"API token with ID {id} not found",
                    Path = Request.Path
                });
            }

            var response = new ApiTokenResponse
            {
                Id = apiToken.Id,
                Name = apiToken.Name,
                Description = apiToken.Description,
                ExpiresAt = apiToken.ExpiresAt,
                LastUsedAt = apiToken.LastUsedAt,
                IsActive = apiToken.IsActive,
                CreatedAt = apiToken.CreatedAt
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve API token with ID: {ApiTokenId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving the API token",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Create a new API token
    /// </summary>
    /// <param name="request">API token creation request</param>
    /// <returns>Created API token with token value</returns>
    [HttpPost]
    [ProducesResponseType(typeof(CreateApiTokenResponse), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<CreateApiTokenResponse>> CreateApiToken([FromBody] CreateApiTokenRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            
            // Check if token with same name already exists for this user
            if (await _apiTokenService.TokenExistsForUserAsync(request.Name, userId))
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = $"API token with name '{request.Name}' already exists",
                    Path = Request.Path
                });
            }

            var apiToken = new ApiToken
            {
                Name = request.Name,
                Description = request.Description,
                ExpiresAt = request.ExpiresAt,
                UserId = userId
            };

            var createdToken = await _apiTokenService.CreateApiTokenAsync(apiToken);
            _logger.LogInformation("API token created successfully: {TokenName} for user {UserId}", createdToken.Name, userId);

            var response = new CreateApiTokenResponse
            {
                Id = createdToken.Id,
                Name = createdToken.Name,
                Description = createdToken.Description,
                TokenValue = createdToken.TokenValue, // Only returned on creation
                ExpiresAt = createdToken.ExpiresAt,
                IsActive = createdToken.IsActive,
                CreatedAt = createdToken.CreatedAt
            };

            return CreatedAtAction(nameof(GetApiTokenById), new { id = createdToken.Id }, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create API token: {TokenName}", request.Name);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while creating the API token",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Update an existing API token
    /// </summary>
    /// <param name="id">API token ID</param>
    /// <param name="request">Updated API token details</param>
    /// <returns>Updated API token</returns>
    [HttpPut("{id}")]
    [ProducesResponseType(typeof(ApiTokenResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ApiTokenResponse>> UpdateApiToken(long id, [FromBody] UpdateApiTokenRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var updatedToken = await _apiTokenService.UpdateApiTokenAsync(id, userId, request.Name, request.Description, request.IsActive);
            
            var response = new ApiTokenResponse
            {
                Id = updatedToken.Id,
                Name = updatedToken.Name,
                Description = updatedToken.Description,
                ExpiresAt = updatedToken.ExpiresAt,
                LastUsedAt = updatedToken.LastUsedAt,
                IsActive = updatedToken.IsActive,
                CreatedAt = updatedToken.CreatedAt
            };

            _logger.LogInformation("API token updated successfully: {ApiTokenId}", id);
            return Ok(response);
        }
        catch (ArgumentException ex)
        {
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update API token: {ApiTokenId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while updating the API token",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Delete an API token
    /// </summary>
    /// <param name="id">API token ID</param>
    /// <returns>No content</returns>
    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult> DeleteApiToken(long id)
    {
        try
        {
            var userId = GetCurrentUserId();
            var apiToken = await _apiTokenService.GetApiTokenByIdAsync(id, userId);
            
            if (apiToken == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"API token with ID {id} not found",
                    Path = Request.Path
                });
            }

            await _apiTokenService.DeleteApiTokenAsync(id, userId);
            _logger.LogInformation("API token deleted successfully: {ApiTokenId}", id);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete API token: {ApiTokenId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while deleting the API token",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Revoke an API token (deactivate)
    /// </summary>
    /// <param name="id">API token ID</param>
    /// <returns>No content</returns>
    [HttpPost("{id}/revoke")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult> RevokeApiToken(long id)
    {
        try
        {
            var userId = GetCurrentUserId();
            await _apiTokenService.RevokeApiTokenAsync(id, userId);
            _logger.LogInformation("API token revoked successfully: {ApiTokenId}", id);
            return NoContent();
        }
        catch (ArgumentException ex)
        {
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to revoke API token: {ApiTokenId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while revoking the API token",
                Path = Request.Path
            });
        }
    }

    private long GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userIdClaim) || !long.TryParse(userIdClaim, out var userId))
        {
            throw new UnauthorizedAccessException("User ID not found in token");
        }
        return userId;
    }
}
