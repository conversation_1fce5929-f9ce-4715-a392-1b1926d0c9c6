using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Services.Interfaces;

namespace CMS.WebApi.Data;

public class CmsDbContext : IdentityDbContext<User, IdentityRole<long>, long>
{
    private readonly IAuditService? _auditService;

    public CmsDbContext(DbContextOptions<CmsDbContext> options) : base(options)
    {
    }

    public CmsDbContext(DbContextOptions<CmsDbContext> options, IAuditService auditService) : base(options)
    {
        _auditService = auditService;
    }

    // Entity DbSets
    public DbSet<Client> Clients { get; set; }
    public DbSet<Category> Categories { get; set; }
    public DbSet<Media> Media { get; set; }
    public DbSet<MediaFolder> MediaFolders { get; set; }
    public DbSet<ApiToken> ApiTokens { get; set; }
    public DbSet<FieldType> FieldTypes { get; set; }
    public DbSet<FieldConfig> FieldConfigs { get; set; }
    public DbSet<ConfigType> ConfigTypes { get; set; }
    public DbSet<CollectionListing> CollectionListings { get; set; }
    public DbSet<ComponentListing> ComponentListings { get; set; }
    public DbSet<CollectionField> CollectionFields { get; set; }
    public DbSet<ComponentField> ComponentFields { get; set; }
    public DbSet<CollectionComponent> CollectionComponents { get; set; }
    public DbSet<ComponentComponent> ComponentComponents { get; set; }
    public DbSet<CollectionFieldConfig> CollectionFieldConfigs { get; set; }
    public DbSet<ComponentFieldConfig> ComponentFieldConfigs { get; set; }
    public DbSet<ContentEntry> ContentEntries { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure entity relationships and constraints
        ConfigureUserEntity(modelBuilder);
        ConfigureIdentityTables(modelBuilder);
        ConfigureClientEntity(modelBuilder);
        ConfigureCategoryEntity(modelBuilder);
        ConfigureMediaEntity(modelBuilder);
        ConfigureMediaFolderEntity(modelBuilder);
        ConfigureApiTokenEntity(modelBuilder);
        ConfigureFieldTypeEntity(modelBuilder);
        ConfigureFieldConfigEntity(modelBuilder);
        ConfigureConfigTypeEntity(modelBuilder);
        ConfigureCollectionListingEntity(modelBuilder);
        ConfigureComponentListingEntity(modelBuilder);
        ConfigureCollectionFieldEntity(modelBuilder);
        ConfigureComponentFieldEntity(modelBuilder);
        ConfigureCollectionComponentEntity(modelBuilder);
        ConfigureComponentComponentEntity(modelBuilder);
        ConfigureCollectionFieldConfigEntity(modelBuilder);
        ConfigureComponentFieldConfigEntity(modelBuilder);
        ConfigureContentEntryEntity(modelBuilder);

        // Configure sequences for PostgreSQL
        ConfigureSequences(modelBuilder);

        // Seed initial data
        SeedData(modelBuilder);
    }

    private void ConfigureUserEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<User>(entity =>
        {
            entity.ToTable("users");
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.UserName).HasColumnName("username").HasMaxLength(50);
            entity.Property(e => e.Email).HasColumnName("email").HasMaxLength(100);
            entity.Property(e => e.PasswordHash).HasColumnName("password");
            entity.Property(e => e.IsActive).HasColumnName("is_active").HasDefaultValue(true);
        });
    }

    private void ConfigureIdentityTables(ModelBuilder modelBuilder)
    {
        // Configure ASP.NET Identity tables
        modelBuilder.Entity<IdentityRole<long>>(entity =>
        {
            entity.ToTable("AspNetRoles");
        });

        modelBuilder.Entity<IdentityUserClaim<long>>(entity =>
        {
            entity.ToTable("AspNetUserClaims");
        });

        modelBuilder.Entity<IdentityUserLogin<long>>(entity =>
        {
            entity.ToTable("AspNetUserLogins");
        });

        modelBuilder.Entity<IdentityUserToken<long>>(entity =>
        {
            entity.ToTable("AspNetUserTokens");
        });

        modelBuilder.Entity<IdentityUserRole<long>>(entity =>
        {
            entity.ToTable("AspNetUserRoles");
        });

        modelBuilder.Entity<IdentityRoleClaim<long>>(entity =>
        {
            entity.ToTable("AspNetRoleClaims");
        });
    }



    private void ConfigureClientEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Client>(entity =>
        {
            entity.ToTable("clients");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id").UseIdentityColumn();
            entity.Property(e => e.Name).HasColumnName("name").HasMaxLength(255).IsRequired();
        });
    }

    private void ConfigureCategoryEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Category>(entity =>
        {
            entity.ToTable("category");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id").UseIdentityColumn();
            entity.Property(e => e.CategoryName).HasColumnName("category_name").HasMaxLength(255).IsRequired();
            entity.Property(e => e.ClientId).HasColumnName("client_id");
            entity.Property(e => e.ParentCategoryId).HasColumnName("parent_category_id");

            entity.HasOne(e => e.Client)
                  .WithMany()
                  .HasForeignKey(e => e.ClientId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.ParentCategory)
                  .WithMany(c => c.ChildCategories)
                  .HasForeignKey(e => e.ParentCategoryId)
                  .OnDelete(DeleteBehavior.SetNull);
        });
    }

    private void ConfigureMediaEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Media>(entity =>
        {
            entity.ToTable("media");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id").UseIdentityColumn();
            entity.Property(e => e.FileName).HasColumnName("file_name").HasMaxLength(255).IsRequired();
            entity.Property(e => e.OriginalFileName).HasColumnName("original_file_name").HasMaxLength(255).IsRequired();
            entity.Property(e => e.FilePath).HasColumnName("file_path").IsRequired();
            entity.Property(e => e.FileType).HasColumnName("file_type").HasMaxLength(100).IsRequired();
            entity.Property(e => e.FileSize).HasColumnName("file_size").IsRequired();
            entity.Property(e => e.Width).HasColumnName("width");
            entity.Property(e => e.Height).HasColumnName("height");
            entity.Property(e => e.Duration).HasColumnName("duration");
            entity.Property(e => e.AltText).HasColumnName("alt_text");
            entity.Property(e => e.Description).HasColumnName("description");
            entity.Property(e => e.PublicUrl).HasColumnName("public_url");
            entity.Property(e => e.ShareToken).HasColumnName("share_token");
            entity.Property(e => e.IsPublic).HasColumnName("is_public").HasDefaultValue(false);
            entity.Property(e => e.FolderId).HasColumnName("folder_id");
            
            entity.HasOne(e => e.Folder)
                  .WithMany(f => f.MediaFiles)
                  .HasForeignKey(e => e.FolderId)
                  .OnDelete(DeleteBehavior.SetNull);

            entity.HasOne(e => e.User)
                  .WithMany()
                  .HasForeignKey(e => e.UserId)
                  .OnDelete(DeleteBehavior.SetNull);
        });
    }

    private void ConfigureMediaFolderEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<MediaFolder>(entity =>
        {
            entity.ToTable("media_folders");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id").UseIdentityColumn();
            entity.Property(e => e.FolderName).HasColumnName("folder_name").HasMaxLength(100).IsRequired();
            entity.Property(e => e.Description).HasColumnName("description");
            entity.Property(e => e.ParentId).HasColumnName("parent_id");
            entity.Property(e => e.UserId).HasColumnName("user_id");

            entity.HasOne(e => e.Parent)
                  .WithMany(f => f.Children)
                  .HasForeignKey(e => e.ParentId)
                  .OnDelete(DeleteBehavior.SetNull);

            entity.HasOne(e => e.User)
                  .WithMany()
                  .HasForeignKey(e => e.UserId)
                  .OnDelete(DeleteBehavior.SetNull);
        });
    }

    private void ConfigureApiTokenEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<ApiToken>(entity =>
        {
            entity.ToTable("api_tokens");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id").UseIdentityColumn();
            entity.Property(e => e.Name).HasColumnName("name").HasMaxLength(50).IsRequired();
            entity.Property(e => e.TokenValue).HasColumnName("token_value").HasMaxLength(255).IsRequired();
            entity.Property(e => e.Description).HasColumnName("description").HasMaxLength(200);
            entity.Property(e => e.ExpiresAt).HasColumnName("expires_at").IsRequired();
            entity.Property(e => e.LastUsedAt).HasColumnName("last_used_at");
            entity.Property(e => e.IsActive).HasColumnName("is_active").HasDefaultValue(true);
            entity.Property(e => e.UserId).HasColumnName("user_id").IsRequired();

            entity.HasOne(e => e.User)
                  .WithMany()
                  .HasForeignKey(e => e.UserId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasIndex(e => e.TokenValue).IsUnique();
        });
    }

    private void ConfigureFieldTypeEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<FieldType>(entity =>
        {
            entity.ToTable("field_types");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id").UseIdentityColumn();
            entity.Property(e => e.FieldTypeName).HasColumnName("field_type_name").HasMaxLength(255).IsRequired();
            entity.Property(e => e.FieldTypeDesc).HasColumnName("field_type_desc");
            entity.Property(e => e.DisplayName).HasColumnName("display_name").HasMaxLength(255);
            entity.Property(e => e.HelpText).HasColumnName("help_text");
            entity.Property(e => e.LogoImagePath).HasColumnName("logo_image_path").HasMaxLength(255);
            entity.Property(e => e.IsActive).HasColumnName("is_active").HasDefaultValue(true);

            entity.HasIndex(e => e.FieldTypeName).IsUnique();
        });
    }

    private void ConfigureFieldConfigEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<FieldConfig>(entity =>
        {
            entity.ToTable("field_configs");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id").UseIdentityColumn();
            entity.Property(e => e.FieldTypeId).HasColumnName("field_type_id");
            entity.Property(e => e.ConfigTypeId).HasColumnName("config_type_id");
            entity.Property(e => e.ConfigName).HasColumnName("config_name").HasMaxLength(255);
            entity.Property(e => e.IsActive).HasColumnName("is_active").HasDefaultValue(true);
            entity.Property(e => e.ValueType).HasColumnName("value_type").HasMaxLength(50);

            entity.HasOne(e => e.FieldType)
                  .WithMany(ft => ft.FieldConfigs)
                  .HasForeignKey(e => e.FieldTypeId)
                  .OnDelete(DeleteBehavior.SetNull);

            entity.HasOne(e => e.ConfigType)
                  .WithMany(ct => ct.FieldConfigs)
                  .HasForeignKey(e => e.ConfigTypeId)
                  .OnDelete(DeleteBehavior.SetNull);
        });
    }

    private void ConfigureConfigTypeEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<ConfigType>(entity =>
        {
            entity.ToTable("config_types");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id").UseIdentityColumn();
            entity.Property(e => e.ConfigTypeName).HasColumnName("config_type_name").HasMaxLength(255).IsRequired();
            entity.Property(e => e.ConfigTypeDesc).HasColumnName("config_type_desc");
            entity.Property(e => e.DisplayName).HasColumnName("display_name").HasMaxLength(255);
            entity.Property(e => e.AdditionalInfo).HasColumnName("additional_info");
            entity.Property(e => e.DisclaimerText).HasColumnName("disclaimer_text");
            entity.Property(e => e.PlaceholderText).HasColumnName("placeholder_text");
            entity.Property(e => e.IsActive).HasColumnName("is_active").HasDefaultValue(true);

            entity.HasIndex(e => e.ConfigTypeName).IsUnique();
        });
    }

    private void ConfigureCollectionListingEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<CollectionListing>(entity =>
        {
            entity.ToTable("collection_listing");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id").UseIdentityColumn();
            entity.Property(e => e.CollectionName).HasColumnName("collection_name").HasMaxLength(255).IsRequired();
            entity.Property(e => e.CollectionDesc).HasColumnName("collection_desc");
            entity.Property(e => e.AdditionalInformation).HasColumnName("additional_information");
            entity.Property(e => e.DisclaimerText).HasColumnName("disclaimer_text");
            entity.Property(e => e.CollectionApiId).HasColumnName("collection_api_id").HasMaxLength(255).IsRequired();
            entity.Property(e => e.CategoryId).HasColumnName("category_id").IsRequired();

            entity.HasOne(e => e.Category)
                  .WithMany(c => c.Collections)
                  .HasForeignKey(e => e.CategoryId)
                  .OnDelete(DeleteBehavior.Cascade);
        });
    }

    private void ConfigureComponentListingEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<ComponentListing>(entity =>
        {
            entity.ToTable("component_listing");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id").UseIdentityColumn();
            entity.Property(e => e.ComponentName).HasColumnName("component_name").HasMaxLength(255).IsRequired();
            entity.Property(e => e.ComponentDisplayName).HasColumnName("component_display_name").HasMaxLength(255);
            entity.Property(e => e.ComponentApiId).HasColumnName("component_api_id").HasMaxLength(255);
            entity.Property(e => e.IsActive).HasColumnName("is_active").HasDefaultValue(true);
            entity.Property(e => e.GetUrl).HasColumnName("get_url");
            entity.Property(e => e.PostUrl).HasColumnName("post_url");
            entity.Property(e => e.UpdateUrl).HasColumnName("update_url");
            entity.Property(e => e.AdditionalInformation).HasColumnName("additional_information");
            entity.Property(e => e.AdditionalInfoImage).HasColumnName("additional_info_image").HasMaxLength(255);

            entity.HasIndex(e => e.ComponentName).IsUnique();
        });
    }

    private void ConfigureCollectionFieldEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<CollectionField>(entity =>
        {
            entity.ToTable("collection_fields");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id").UseIdentityColumn();
            entity.Property(e => e.CollectionId).HasColumnName("collection_id").IsRequired();
            entity.Property(e => e.FieldTypeId).HasColumnName("field_type_id").IsRequired();
            entity.Property(e => e.DisplayPreference).HasColumnName("display_preference");
            entity.Property(e => e.DependentOnId).HasColumnName("dependent_on");
            entity.Property(e => e.AdditionalInformation).HasColumnName("additional_information");

            entity.HasOne(e => e.Collection)
                  .WithMany(c => c.Fields)
                  .HasForeignKey(e => e.CollectionId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.FieldType)
                  .WithMany()
                  .HasForeignKey(e => e.FieldTypeId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.DependentOn)
                  .WithMany()
                  .HasForeignKey(e => e.DependentOnId)
                  .OnDelete(DeleteBehavior.SetNull);
        });
    }

    private void ConfigureComponentFieldEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<ComponentField>(entity =>
        {
            entity.ToTable("component_fields");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id").UseIdentityColumn();
            entity.Property(e => e.ComponentId).HasColumnName("component_id").IsRequired();
            entity.Property(e => e.FieldTypeId).HasColumnName("field_type_id").IsRequired();
            entity.Property(e => e.DisplayPreference).HasColumnName("display_preference");
            entity.Property(e => e.DependentOnId).HasColumnName("dependent_on");
            entity.Property(e => e.AdditionalInformation).HasColumnName("additional_information");

            entity.HasOne(e => e.Component)
                  .WithMany(c => c.Fields)
                  .HasForeignKey(e => e.ComponentId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.FieldType)
                  .WithMany()
                  .HasForeignKey(e => e.FieldTypeId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.DependentOn)
                  .WithMany()
                  .HasForeignKey(e => e.DependentOnId)
                  .OnDelete(DeleteBehavior.SetNull);
        });
    }

    private void ConfigureCollectionComponentEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<CollectionComponent>(entity =>
        {
            entity.ToTable("collection_components");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id").UseIdentityColumn();
            entity.Property(e => e.CollectionId).HasColumnName("collection_id").IsRequired();
            entity.Property(e => e.ComponentId).HasColumnName("component_id").IsRequired();
            entity.Property(e => e.DisplayPreference).HasColumnName("display_preference");
            entity.Property(e => e.IsRepeatable).HasColumnName("is_repeatable").HasDefaultValue(false);
            entity.Property(e => e.MinRepeatOccurrences).HasColumnName("min_repeat_occurrences");
            entity.Property(e => e.MaxRepeatOccurrences).HasColumnName("max_repeat_occurrences");
            entity.Property(e => e.IsActive).HasColumnName("is_active").HasDefaultValue(true);
            entity.Property(e => e.Name).HasColumnName("name").HasMaxLength(255);
            entity.Property(e => e.DisplayName).HasColumnName("display_name").HasMaxLength(255);
            entity.Property(e => e.AdditionalInfo).HasColumnName("additional_info");
            entity.Property(e => e.AdditionalInfoImage).HasColumnName("additional_info_image").HasMaxLength(255);

            entity.HasOne(e => e.Collection)
                  .WithMany(c => c.Components)
                  .HasForeignKey(e => e.CollectionId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.Component)
                  .WithMany(c => c.CollectionComponents)
                  .HasForeignKey(e => e.ComponentId)
                  .OnDelete(DeleteBehavior.Cascade);
        });
    }

    private void ConfigureComponentComponentEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<ComponentComponent>(entity =>
        {
            entity.ToTable("component_components");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id").UseIdentityColumn();
            entity.Property(e => e.ParentComponentId).HasColumnName("parent_component_id").IsRequired();
            entity.Property(e => e.ChildComponentId).HasColumnName("child_component_id").IsRequired();
            entity.Property(e => e.DisplayPreference).HasColumnName("display_preference");
            entity.Property(e => e.IsRepeatable).HasColumnName("is_repeatable").HasDefaultValue(false);
            entity.Property(e => e.IsActive).HasColumnName("is_active").HasDefaultValue(true);
            entity.Property(e => e.AdditionalInformation).HasColumnName("additional_information");

            entity.HasOne(e => e.ParentComponent)
                  .WithMany(c => c.ChildComponents)
                  .HasForeignKey(e => e.ParentComponentId)
                  .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(e => e.ChildComponent)
                  .WithMany(c => c.ParentComponents)
                  .HasForeignKey(e => e.ChildComponentId)
                  .OnDelete(DeleteBehavior.Restrict);
        });
    }

    private void ConfigureCollectionFieldConfigEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<CollectionFieldConfig>(entity =>
        {
            entity.ToTable("collection_field_config");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id").UseIdentityColumn();
            entity.Property(e => e.CollectionFieldId).HasColumnName("collection_field_id").IsRequired();
            entity.Property(e => e.FieldConfigId).HasColumnName("field_config_id").IsRequired();
            entity.Property(e => e.ConfigValue).HasColumnName("config_value").HasMaxLength(255);
            entity.Property(e => e.IsActive).HasColumnName("is_active").HasDefaultValue(true);

            entity.HasOne(e => e.CollectionField)
                  .WithMany(cf => cf.Configs)
                  .HasForeignKey(e => e.CollectionFieldId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.FieldConfig)
                  .WithMany()
                  .HasForeignKey(e => e.FieldConfigId)
                  .OnDelete(DeleteBehavior.Cascade);
        });
    }

    private void ConfigureComponentFieldConfigEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<ComponentFieldConfig>(entity =>
        {
            entity.ToTable("component_field_config");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id").UseIdentityColumn();
            entity.Property(e => e.ComponentFieldId).HasColumnName("component_field_id").IsRequired();
            entity.Property(e => e.FieldConfigId).HasColumnName("field_config_id").IsRequired();
            entity.Property(e => e.ConfigValue).HasColumnName("config_value").HasMaxLength(255);
            entity.Property(e => e.IsActive).HasColumnName("is_active").HasDefaultValue(true);

            entity.HasOne(e => e.ComponentField)
                  .WithMany(cf => cf.Configs)
                  .HasForeignKey(e => e.ComponentFieldId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.FieldConfig)
                  .WithMany()
                  .HasForeignKey(e => e.FieldConfigId)
                  .OnDelete(DeleteBehavior.Cascade);
        });
    }

    private void ConfigureContentEntryEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<ContentEntry>(entity =>
        {
            entity.ToTable("content_entries");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnName("id").UseIdentityColumn();
            entity.Property(e => e.CollectionId).HasColumnName("collection_id").IsRequired();
            entity.Property(e => e.DataJson).HasColumnName("data_json").HasColumnType("jsonb");

            entity.HasOne(e => e.Collection)
                  .WithMany(c => c.ContentEntries)
                  .HasForeignKey(e => e.CollectionId)
                  .OnDelete(DeleteBehavior.Cascade);
        });
    }

    private void ConfigureSequences(ModelBuilder modelBuilder)
    {
        // Configure PostgreSQL sequences
        modelBuilder.HasSequence<int>("cms_client_seq").StartsAt(100).IncrementsBy(1);
        modelBuilder.HasSequence<int>("cms_category_seq").StartsAt(100).IncrementsBy(1);
        modelBuilder.HasSequence<int>("cms_media_seq").StartsAt(100).IncrementsBy(1);
        modelBuilder.HasSequence<int>("cms_media_folder_seq").StartsAt(100).IncrementsBy(1);
        modelBuilder.HasSequence<long>("cms_user_seq").StartsAt(100).IncrementsBy(1);
        modelBuilder.HasSequence<long>("cms_api_token_seq").StartsAt(100).IncrementsBy(1);
        modelBuilder.HasSequence<int>("cms_field_type_seq").StartsAt(100).IncrementsBy(1);
        modelBuilder.HasSequence<int>("cms_field_config_seq").StartsAt(100).IncrementsBy(1);
        modelBuilder.HasSequence<int>("cms_config_type_seq").StartsAt(100).IncrementsBy(1);
        modelBuilder.HasSequence<int>("cms_collection_listing_seq").StartsAt(100).IncrementsBy(1);
        modelBuilder.HasSequence<int>("cms_component_listing_seq").StartsAt(100).IncrementsBy(1);
        modelBuilder.HasSequence<int>("cms_collection_field_seq").StartsAt(100).IncrementsBy(1);
        modelBuilder.HasSequence<int>("cms_component_field_seq").StartsAt(100).IncrementsBy(1);
        modelBuilder.HasSequence<int>("cms_collection_component_seq").StartsAt(100).IncrementsBy(1);
        modelBuilder.HasSequence<int>("cms_component_component_seq").StartsAt(100).IncrementsBy(1);
        modelBuilder.HasSequence<int>("cms_content_entry_seq").StartsAt(100).IncrementsBy(1);
    }

    private void SeedData(ModelBuilder modelBuilder)
    {
        SeedConfigTypes(modelBuilder);
        SeedFieldTypes(modelBuilder);
        SeedFieldConfigs(modelBuilder);
    }

 private void SeedConfigTypes(ModelBuilder modelBuilder)
{
   var configTypes = new[]
{
    new {
        Id = 1, Name = "properties", Desc = "Basic properties for fields", Display = "Properties",
        AdditionalInfo = (string)null, DisclaimerText = "Basic properties for fields", PlaceholderText = "Properties"
    },
    new {
        Id = 2, Name = "attributes", Desc = "UI attributes for fields", Display = "Attributes",
        AdditionalInfo = (string)null, DisclaimerText = "Attributes for fields", PlaceholderText = "Attributes"
    },
    new {
        Id = 3, Name = "validations", Desc = "Validation rules for fields", Display = "Validations",
        AdditionalInfo = (string)null, DisclaimerText = "Validation rules for fields", PlaceholderText = "Validations"
    }
};
    var seedData = configTypes.Select(ct => new ConfigType
    {
        Id = ct.Id,
        ConfigTypeName = ct.Name,
        ConfigTypeDesc = ct.Desc,
        DisplayName = ct.Display,
        AdditionalInfo = ct.AdditionalInfo,
        DisclaimerText = ct.DisclaimerText,
        PlaceholderText = ct.PlaceholderText,
        IsActive = true,
        CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
        ModifiedAt = null,
        CreatedBy = "System",
        ModifiedBy = null
    }).ToArray();

    modelBuilder.Entity<ConfigType>().HasData(seedData);
}


  private void SeedFieldTypes(ModelBuilder modelBuilder)
{
    var fieldTypes = new[]
    {
        new { Id = 1, Name = "text", Desc = "Simple text field", Display = "Text", Help = "Enter text here" },
        new { Id = 2, Name = "number", Desc = "Numeric field", Display = "Number", Help = "Enter a number" },
        new { Id = 3, Name = "date", Desc = "Date field", Display = "Date", Help = "Select a date" },
        new { Id = 4, Name = "image", Desc = "Image upload field", Display = "Image", Help = "Upload an image" },
        new { Id = 5, Name = "rich_text", Desc = "Rich text editor", Display = "Rich Text", Help = "Enter formatted text" },
        new { Id = 6, Name = "mask", Desc = "Masked input field", Display = "Masked", Help = "Enter masked value (e.g., SSN, EIN)" },
        new { Id = 8, Name = "editor", Desc = "Rich text editor", Display = "Editor", Help = "Enter formatted text" },
        new { Id = 9, Name = "password", Desc = "Password input field", Display = "Password", Help = "Enter password" },
        new { Id = 10, Name = "autocomplete", Desc = "Autocomplete suggestions", Display = "Autocomplete", Help = "Start typing for suggestions" },
        new { Id = 11, Name = "cascade_select", Desc = "Cascade selection field", Display = "Cascade Select", Help = "Select dependent options" },
        new { Id = 12, Name = "dropdown", Desc = "Dropdown selection", Display = "Dropdown", Help = "Select from dropdown" },
        new { Id = 13, Name = "file", Desc = "File upload field", Display = "File", Help = "Upload a file" },
        new { Id = 14, Name = "multi_state_checkbox", Desc = "Multi-state checkbox", Display = "Multi-State Checkbox", Help = "Select multiple states" },
        new { Id = 15, Name = "multi_select", Desc = "Multi-select field", Display = "Multi-Select", Help = "Select multiple options" },
        new { Id = 16, Name = "multi_select", Desc = "Multi-select field", Display = "Multi-Select", Help = "Select multiple options" },
        new { Id = 17, Name = "mention", Desc = "Mention users or tags", Display = "Mention", Help = "Mention users or tags" },
        new { Id = 18, Name = "textarea_extended", Desc = "Multi-line text input", Display = "Text Area Extended", Help = "Enter multi-line text" },
        new { Id = 19, Name = "otp", Desc = "One-time password input", Display = "OTP", Help = "Enter one-time password" },
        new { Id = 20, Name = "multi_checkbox", Desc = "Multiple checkbox input field", Display = "Multi Checkbox", Help = "Select one or more options" },
        new { Id = 21, Name = "radio_button", Desc = "Radio button selection field", Display = "Radio Button", Help = "Select one option" },
        new { Id = 22, Name = "input_switch", Desc = "Toggle switch input", Display = "Input Switch", Help = "Toggle the option on or off" },
        new { Id = 23, Name = "dummy", Desc = "Dummy field", Display = "Dummy", Help = "Dummy Field" },
        new { Id = 24, Name = "api_details", Desc = "API details configuration", Display = "API Details", Help = "Configure API details" },
        new { Id = 25, Name = "enumeration", Desc = "Enumeration field for fixed value lists", Display = "Enumeration", Help = "Define a list of fixed values" }
    };

    var seedData = fieldTypes.Select(ft => new FieldType
    {
        Id = ft.Id,
        FieldTypeName = ft.Name,
        FieldTypeDesc = ft.Desc,
        DisplayName = ft.Display,
        HelpText = ft.Help,
        IsActive = true,
        CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
        CreatedBy = "System"
    }).ToArray();

    modelBuilder.Entity<FieldType>().HasData(seedData);
}


    private void SeedFieldConfigs(ModelBuilder modelBuilder)
    {
        var fieldConfigs = new[]
        {
            new { Id = 1, Name = "dependOn", ValueType = "text", FieldTypeId = 1, ConfigTypeId = 1 },
            new { Id = 2, Name = "unique-id", ValueType = "text", FieldTypeId = 1, ConfigTypeId = 1 },
            new { Id = 3, Name = "display-name", ValueType = "text", FieldTypeId = 1, ConfigTypeId = 1 },
            new { Id = 4, Name = "name", ValueType = "text", FieldTypeId = 1, ConfigTypeId = 1 },
            new { Id = 5, Name = "description", ValueType = "text", FieldTypeId = 1, ConfigTypeId = 1 },
            new { Id = 6, Name = "is-visible", ValueType = "boolean", FieldTypeId = 1, ConfigTypeId = 1 },
            new { Id = 7, Name = "keyfilter", ValueType = "regex", FieldTypeId = 1, ConfigTypeId = 2 },
            new { Id = 8, Name = "placeholder", ValueType = "text", FieldTypeId = 1, ConfigTypeId = 2 },
            new { Id = 9, Name = "helpText", ValueType = "text", FieldTypeId = 1, ConfigTypeId = 2 },
            new { Id = 10, Name = "FloatLable", ValueType = "text", FieldTypeId = 1, ConfigTypeId = 2 },
            new { Id = 11, Name = "variant", ValueType = "options", FieldTypeId = 1, ConfigTypeId = 2 },
            new { Id = 12, Name = "invalid", ValueType = "boolean", FieldTypeId = 1, ConfigTypeId = 2 },
            new { Id = 13, Name = "disabled", ValueType = "boolean", FieldTypeId = 1, ConfigTypeId = 2 },
            new { Id = 14, Name = "icon", ValueType = "icon", FieldTypeId = 1, ConfigTypeId = 2 },
            new { Id = 15, Name = "tooltip", ValueType = "text", FieldTypeId = 1, ConfigTypeId = 2 },
            new { Id = 16, Name = "tooltipOptions", ValueType = "text", FieldTypeId = 1, ConfigTypeId = 2 },
            new { Id = 17, Name = "autoClear", ValueType = "boolean", FieldTypeId = 1, ConfigTypeId = 2 },
            new { Id = 18, Name = "required", ValueType = "boolean", FieldTypeId = 1, ConfigTypeId = 3 },
            new { Id = 19, Name = "minLength", ValueType = "number", FieldTypeId = 1, ConfigTypeId = 2 },
            new { Id = 20, Name = "maxLength", ValueType = "number", FieldTypeId = 1, ConfigTypeId = 2 },
            new { Id = 21, Name = "regex", ValueType = "regex", FieldTypeId = 1, ConfigTypeId = 3 },
            new { Id = 22, Name = "api-url", ValueType = "url", FieldTypeId = 1, ConfigTypeId = 1 },
            new { Id = 23, Name = "dependOn", ValueType = "text", FieldTypeId = 2, ConfigTypeId = 1 },
            new { Id = 24, Name = "unique-id", ValueType = "text", FieldTypeId = 2, ConfigTypeId = 1 },
            new { Id = 25, Name = "display-name", ValueType = "text", FieldTypeId = 2, ConfigTypeId = 1 },
            new { Id = 26, Name = "name", ValueType = "text", FieldTypeId = 2, ConfigTypeId = 1 },
            new { Id = 27, Name = "description", ValueType = "text", FieldTypeId = 2, ConfigTypeId = 1 },
            new { Id = 28, Name = "is-visible", ValueType = "boolean", FieldTypeId = 2, ConfigTypeId = 1 },
            new { Id = 29, Name = "placeholder", ValueType = "text", FieldTypeId = 2, ConfigTypeId = 2 },
            new { Id = 30, Name = "helpText", ValueType = "text", FieldTypeId = 2, ConfigTypeId = 2 },
            new { Id = 31, Name = "FloatLable", ValueType = "text", FieldTypeId = 2, ConfigTypeId = 2 },
            new { Id = 32, Name = "variant", ValueType = "options", FieldTypeId = 2, ConfigTypeId = 2 },
            new { Id = 33, Name = "invalid", ValueType = "boolean", FieldTypeId = 2, ConfigTypeId = 2 },
            new { Id = 34, Name = "disabled", ValueType = "boolean", FieldTypeId = 2, ConfigTypeId = 2 },
            new { Id = 35, Name = "useGrouping", ValueType = "boolean", FieldTypeId = 2, ConfigTypeId = 2 },
            new { Id = 36, Name = "locale", ValueType = "text", FieldTypeId = 2, ConfigTypeId = 2 },
            new { Id = 37, Name = "suffix", ValueType = "text", FieldTypeId = 2, ConfigTypeId = 2 },
            new { Id = 38, Name = "prefix", ValueType = "text", FieldTypeId = 2, ConfigTypeId = 2 },
            new { Id = 39, Name = "showButtons", ValueType = "boolean", FieldTypeId = 2, ConfigTypeId = 2 },
            new { Id = 40, Name = "mode", ValueType = "options", FieldTypeId = 2, ConfigTypeId = 2 },
            new { Id = 41, Name = "currency", ValueType = "options", FieldTypeId = 2, ConfigTypeId = 2 },
            new { Id = 42, Name = "step", ValueType = "number", FieldTypeId = 2, ConfigTypeId = 2 },
            new { Id = 43, Name = "min", ValueType = "number", FieldTypeId = 2, ConfigTypeId = 2 },
            new { Id = 44, Name = "max", ValueType = "number", FieldTypeId = 2, ConfigTypeId = 2 },
            new { Id = 45, Name = "minFractionDigits", ValueType = "number", FieldTypeId = 2, ConfigTypeId = 2 },
            new { Id = 46, Name = "maxFractionDigits", ValueType = "number", FieldTypeId = 2, ConfigTypeId = 2 },
            new { Id = 47, Name = "incrementButtonIcon", ValueType = "icon", FieldTypeId = 2, ConfigTypeId = 2 },
            new { Id = 48, Name = "decrementButtonIcon", ValueType = "icon", FieldTypeId = 2, ConfigTypeId = 2 },
            new { Id = 49, Name = "decrementButtonClassName", ValueType = "text", FieldTypeId = 2, ConfigTypeId = 2 },
            new { Id = 50, Name = "incrementButtonClassName", ValueType = "text", FieldTypeId = 2, ConfigTypeId = 2 },
            new { Id = 51, Name = "buttonLayout", ValueType = "text", FieldTypeId = 2, ConfigTypeId = 2 },
            new { Id = 52, Name = "autoClear", ValueType = "boolean", FieldTypeId = 2, ConfigTypeId = 2 },
            new { Id = 53, Name = "icon", ValueType = "icon", FieldTypeId = 2, ConfigTypeId = 2 },
            new { Id = 54, Name = "required", ValueType = "boolean", FieldTypeId = 2, ConfigTypeId = 3 },
            new { Id = 55, Name = "minValue", ValueType = "number", FieldTypeId = 2, ConfigTypeId = 3 },
            new { Id = 56, Name = "maxValue", ValueType = "number", FieldTypeId = 2, ConfigTypeId = 3 },
            new { Id = 57, Name = "minFractionDigits", ValueType = "number", FieldTypeId = 2, ConfigTypeId = 3 },
            new { Id = 58, Name = "maxFractionDigits", ValueType = "number", FieldTypeId = 2, ConfigTypeId = 3 },
            new { Id = 59, Name = "regex", ValueType = "regex", FieldTypeId = 2, ConfigTypeId = 3 },
            new { Id = 60, Name = "dependOn", ValueType = "text", FieldTypeId = 6, ConfigTypeId = 1 },
            new { Id = 61, Name = "required", ValueType = "boolean", FieldTypeId = 6, ConfigTypeId = 3 },
            new { Id = 62, Name = "placeholder", ValueType = "text", FieldTypeId = 6, ConfigTypeId = 2 },
            new { Id = 63, Name = "mask", ValueType = "regex", FieldTypeId = 6, ConfigTypeId = 2 },
            new { Id = 64, Name = "helpText", ValueType = "text", FieldTypeId = 6, ConfigTypeId = 2 },
            new { Id = 65, Name = "regex", ValueType = "regex", FieldTypeId = 6, ConfigTypeId = 3 },
            new { Id = 66, Name = "FloatLable", ValueType = "text", FieldTypeId = 6, ConfigTypeId = 2 },
            new { Id = 67, Name = "unmask", ValueType = "boolean", FieldTypeId = 6, ConfigTypeId = 3 },
            new { Id = 68, Name = "variant", ValueType = "options", FieldTypeId = 6, ConfigTypeId = 2 },
            new { Id = 69, Name = "invalid", ValueType = "boolean", FieldTypeId = 6, ConfigTypeId = 2 },
            new { Id = 70, Name = "disabled", ValueType = "boolean", FieldTypeId = 6, ConfigTypeId = 2 },
            new { Id = 71, Name = "slotChar", ValueType = "regex", FieldTypeId = 6, ConfigTypeId = 3 },
            new { Id = 72, Name = "autoClear", ValueType = "boolean", FieldTypeId = 6, ConfigTypeId = 2 },
            new { Id = 73, Name = "icon", ValueType = "icon", FieldTypeId = 6, ConfigTypeId = 2 },
            new { Id = 74, Name = "dependOn", ValueType = "text", FieldTypeId = 3, ConfigTypeId = 1 },
            new { Id = 75, Name = "dateFormat", ValueType = "date", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 76, Name = "locale", ValueType = "text", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 77, Name = "showIcon", ValueType = "boolean", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 78, Name = "minDate", ValueType = "date", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 79, Name = "maxDate", ValueType = "date", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 80, Name = "readOnlyInput", ValueType = "boolean", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 81, Name = "selectionMode", ValueType = "options", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 82, Name = "hideOnRangeSelection", ValueType = "boolean", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 83, Name = "showButtonBar", ValueType = "boolean", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 84, Name = "showTime", ValueType = "date", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 85, Name = "hourFormat", ValueType = "options", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 86, Name = "view", ValueType = "options", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 87, Name = "numberOfMonths", ValueType = "number", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 88, Name = "FloatLable", ValueType = "text", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 89, Name = "variant", ValueType = "options", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 90, Name = "invalid", ValueType = "boolean", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 91, Name = "disabled", ValueType = "boolean", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 92, Name = "timeOnly", ValueType = "date", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 93, Name = "icon", ValueType = "icon", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 94, Name = "inline", ValueType = "boolean", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 95, Name = "showWeek", ValueType = "boolean", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 96, Name = "readonly", ValueType = "boolean", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 97, Name = "dependon", ValueType = "text", FieldTypeId = 9, ConfigTypeId = 1 },
            new { Id = 98, Name = "feedback_(true/false)", ValueType = "boolean", FieldTypeId = 9, ConfigTypeId = 2 },
            new { Id = 99, Name = "required", ValueType = "boolean", FieldTypeId = 9, ConfigTypeId = 3 },
            new { Id = 100, Name = "unique-id", ValueType = "text", FieldTypeId = 9, ConfigTypeId = 1 },
            new { Id = 101, Name = "promptlabel", ValueType = "text", FieldTypeId = 9, ConfigTypeId = 2 },
            new { Id = 102, Name = "minlength", ValueType = "number", FieldTypeId = 9, ConfigTypeId = 2 },
            new { Id = 103, Name = "display-name", ValueType = "text", FieldTypeId = 9, ConfigTypeId = 1 },
            new { Id = 104, Name = "weaklabel", ValueType = "text", FieldTypeId = 9, ConfigTypeId = 2 },
            new { Id = 105, Name = "maxlength", ValueType = "number", FieldTypeId = 9, ConfigTypeId = 2 },
            new { Id = 106, Name = "name", ValueType = "text", FieldTypeId = 9, ConfigTypeId = 1 },
            new { Id = 107, Name = "mediumlabel", ValueType = "text", FieldTypeId = 9, ConfigTypeId = 2 },
            new { Id = 108, Name = "regex", ValueType = "regex", FieldTypeId = 9, ConfigTypeId = 3 },
            new { Id = 109, Name = "description", ValueType = "text", FieldTypeId = 9, ConfigTypeId = 1 },
            new { Id = 110, Name = "stronglabel", ValueType = "text", FieldTypeId = 9, ConfigTypeId = 2 },
            new { Id = 111, Name = "is-visible", ValueType = "boolean", FieldTypeId = 9, ConfigTypeId = 1 },
            new { Id = 112, Name = "toggle(true/false)", ValueType = "boolean", FieldTypeId = 9, ConfigTypeId = 2 },
            new { Id = 113, Name = "floatlable_(true/false)", ValueType = "boolean", FieldTypeId = 9, ConfigTypeId = 2 },
            new { Id = 114, Name = "variant_(filled)", ValueType = "string", FieldTypeId = 9, ConfigTypeId = 2 },
            new { Id = 115, Name = "invalid_(true/false)", ValueType = "boolean", FieldTypeId = 9, ConfigTypeId = 2 },
            new { Id = 116, Name = "disabled__(true/false)", ValueType = "boolean", FieldTypeId = 9, ConfigTypeId = 2 },
            new { Id = 117, Name = "icon", ValueType = "icon", FieldTypeId = 9, ConfigTypeId = 2 },
            new { Id = 118, Name = "dependon", ValueType = "text", FieldTypeId = 10, ConfigTypeId = 1 },
            new { Id = 119, Name = "dropdown", ValueType = "options", FieldTypeId = 10, ConfigTypeId = 2 },
            new { Id = 120, Name = "unique-id", ValueType = "text", FieldTypeId = 10, ConfigTypeId = 1 },
            new { Id = 121, Name = "display-name", ValueType = "text", FieldTypeId = 10, ConfigTypeId = 1 },
            new { Id = 122, Name = "object", ValueType = "object", FieldTypeId = 10, ConfigTypeId = 2 },
            new { Id = 123, Name = "name", ValueType = "text", FieldTypeId = 10, ConfigTypeId = 1 },
            new { Id = 124, Name = "group", ValueType = "text", FieldTypeId = 10, ConfigTypeId = 2 },
            new { Id = 125, Name = "description", ValueType = "text", FieldTypeId = 10, ConfigTypeId = 1 },
            new { Id = 126, Name = "force_selection", ValueType = "boolean", FieldTypeId = 10, ConfigTypeId = 2 },
            new { Id = 127, Name = "is-visible", ValueType = "boolean", FieldTypeId = 10, ConfigTypeId = 1 },
            new { Id = 128, Name = "multiple", ValueType = "boolean", FieldTypeId = 10, ConfigTypeId = 2 },
            new { Id = 129, Name = "floatlable_(true/false)", ValueType = "boolean", FieldTypeId = 10, ConfigTypeId = 2 },
            new { Id = 130, Name = "variant_(filled)", ValueType = "string", FieldTypeId = 10, ConfigTypeId = 2 },
            new { Id = 131, Name = "invalid_(true/false)", ValueType = "boolean", FieldTypeId = 10, ConfigTypeId = 1 },
            new { Id = 132, Name = "disabled__(true/false)", ValueType = "boolean", FieldTypeId = 10, ConfigTypeId = 2 },
            new { Id = 133, Name = "dependon", ValueType = "text", FieldTypeId = 11, ConfigTypeId = 1 },
            new { Id = 134, Name = "unique-id", ValueType = "text", FieldTypeId = 11, ConfigTypeId = 1 },
            new { Id = 135, Name = "placeholder", ValueType = "text", FieldTypeId = 11, ConfigTypeId = 1 },
            new { Id = 136, Name = "display-name", ValueType = "text", FieldTypeId = 11, ConfigTypeId = 1 },
            new { Id = 137, Name = "float_label", ValueType = "boolean", FieldTypeId = 11, ConfigTypeId = 2 },
            new { Id = 138, Name = "name", ValueType = "text", FieldTypeId = 11, ConfigTypeId = 1 },
            new { Id = 139, Name = "variant_(filled)", ValueType = "string", FieldTypeId = 11, ConfigTypeId = 2 },
            new { Id = 140, Name = "description", ValueType = "text", FieldTypeId = 11, ConfigTypeId = 1 },
            new { Id = 141, Name = "invalid", ValueType = "boolean", FieldTypeId = 11, ConfigTypeId = 2 },
            new { Id = 142, Name = "is-visible", ValueType = "boolean", FieldTypeId = 11, ConfigTypeId = 1 },
            new { Id = 143, Name = "disabled", ValueType = "boolean", FieldTypeId = 11, ConfigTypeId = 2 },
            new { Id = 144, Name = "dependOn", ValueType = "text", FieldTypeId = 12, ConfigTypeId = 1 },
            new { Id = 145, Name = "unique-id", ValueType = "text", FieldTypeId = 12, ConfigTypeId = 1 },
            new { Id = 146, Name = "display-name", ValueType = "text", FieldTypeId = 12, ConfigTypeId = 1 },
            new { Id = 147, Name = "name", ValueType = "text", FieldTypeId = 12, ConfigTypeId = 1 },
            new { Id = 148, Name = "description", ValueType = "text", FieldTypeId = 12, ConfigTypeId = 1 },
            new { Id = 149, Name = "is-visible", ValueType = "boolean", FieldTypeId = 12, ConfigTypeId = 1 },
            new { Id = 150, Name = "get-api-url", ValueType = "url", FieldTypeId = 12, ConfigTypeId = 1 },
            new { Id = 151, Name = "placeholder", ValueType = "text", FieldTypeId = 12, ConfigTypeId = 1 },
            new { Id = 152, Name = "checkmark", ValueType = "boolean", FieldTypeId = 12, ConfigTypeId = 2 },
            new { Id = 153, Name = "highlightOnSelect", ValueType = "boolean", FieldTypeId = 12, ConfigTypeId = 2 },
            new { Id = 154, Name = "editable", ValueType = "boolean", FieldTypeId = 12, ConfigTypeId = 2 },
            new { Id = 155, Name = "optionGroupLabel", ValueType = "text", FieldTypeId = 12, ConfigTypeId = 2 },
            new { Id = 156, Name = "optionGroupChildren", ValueType = "options", FieldTypeId = 12, ConfigTypeId = 2 },
            new { Id = 157, Name = "optionGroupTemplate", ValueType = "template", FieldTypeId = 12, ConfigTypeId = 2 },
            new { Id = 158, Name = "valueTemplate", ValueType = "template", FieldTypeId = 12, ConfigTypeId = 2 },
            new { Id = 159, Name = "itemTemplate", ValueType = "template", FieldTypeId = 12, ConfigTypeId = 2 },
            new { Id = 160, Name = "panelFooterTemplate", ValueType = "template", FieldTypeId = 12, ConfigTypeId = 2 },
            new { Id = 161, Name = "filter", ValueType = "boolean", FieldTypeId = 12, ConfigTypeId = 2 },
            new { Id = 162, Name = "showClear", ValueType = "boolean", FieldTypeId = 12, ConfigTypeId = 2 },
            new { Id = 163, Name = "loading", ValueType = "boolean", FieldTypeId = 12, ConfigTypeId = 2 },
            new { Id = 164, Name = "virtualScrollerOptions", ValueType = "boolean", FieldTypeId = 12, ConfigTypeId = 2 },
            new { Id = 165, Name = "FloatLable", ValueType = "text", FieldTypeId = 12, ConfigTypeId = 2 },
            new { Id = 166, Name = "variant", ValueType = "options", FieldTypeId = 12, ConfigTypeId = 2 },
            new { Id = 167, Name = "invalid", ValueType = "boolean", FieldTypeId = 12, ConfigTypeId = 2 },
            new { Id = 168, Name = "disabled", ValueType = "boolean", FieldTypeId = 12, ConfigTypeId = 2 },
            new { Id = 169, Name = "dependOn", ValueType = "text", FieldTypeId = 13, ConfigTypeId = 1 },
            new { Id = 170, Name = "unique-id", ValueType = "text", FieldTypeId = 13, ConfigTypeId = 1 },
            new { Id = 171, Name = "display-name", ValueType = "text", FieldTypeId = 13, ConfigTypeId = 1 },
            new { Id = 172, Name = "name", ValueType = "text", FieldTypeId = 13, ConfigTypeId = 1 },
            new { Id = 173, Name = "description", ValueType = "text", FieldTypeId = 13, ConfigTypeId = 1 },
            new { Id = 174, Name = "is-visible", ValueType = "boolean", FieldTypeId = 13, ConfigTypeId = 1 },
            new { Id = 175, Name = "get-api-url", ValueType = "url", FieldTypeId = 13, ConfigTypeId = 1 },
            new { Id = 176, Name = "mode", ValueType = "options", FieldTypeId = 13, ConfigTypeId = 2 },
            new { Id = 177, Name = "url", ValueType = "url", FieldTypeId = 13, ConfigTypeId = 2 },
            new { Id = 178, Name = "accept", ValueType = "upload", FieldTypeId = 13, ConfigTypeId = 2 },
            new { Id = 179, Name = "minFileSize", ValueType = "number", FieldTypeId = 13, ConfigTypeId = 2 },
            new { Id = 180, Name = "maxFileSize", ValueType = "number", FieldTypeId = 13, ConfigTypeId = 2 },
            new { Id = 181, Name = "auto", ValueType = "boolean", FieldTypeId = 13, ConfigTypeId = 2 },
            new { Id = 182, Name = "chooseLabel", ValueType = "text", FieldTypeId = 13, ConfigTypeId = 2 },
            new { Id = 183, Name = "multiple", ValueType = "boolean", FieldTypeId = 13, ConfigTypeId = 2 },
            new { Id = 184, Name = "emptyTemplate", ValueType = "template", FieldTypeId = 13, ConfigTypeId = 2 },
            new { Id = 185, Name = "customUpload", ValueType = "boolean", FieldTypeId = 13, ConfigTypeId = 2 },
            new { Id = 186, Name = "uploadHandler", ValueType = "upload", FieldTypeId = 13, ConfigTypeId = 2 },
            new { Id = 187, Name = "dependOn", ValueType = "text", FieldTypeId = 14, ConfigTypeId = 1 },
            new { Id = 188, Name = "unique-id", ValueType = "text", FieldTypeId = 14, ConfigTypeId = 1 },
            new { Id = 189, Name = "display-name", ValueType = "text", FieldTypeId = 14, ConfigTypeId = 1 },
            new { Id = 190, Name = "name", ValueType = "text", FieldTypeId = 14, ConfigTypeId = 1 },
            new { Id = 191, Name = "description", ValueType = "text", FieldTypeId = 14, ConfigTypeId = 1 },
            new { Id = 192, Name = "is-visible", ValueType = "boolean", FieldTypeId = 14, ConfigTypeId = 1 },
            new { Id = 193, Name = "options", ValueType = "array", FieldTypeId = 14, ConfigTypeId = 2 },
            new { Id = 194, Name = "iconTemplate", ValueType = "template", FieldTypeId = 14, ConfigTypeId = 2 },
            new { Id = 195, Name = "disabled", ValueType = "boolean", FieldTypeId = 14, ConfigTypeId = 2 },
            new { Id = 196, Name = "value", ValueType = "object", FieldTypeId = 14, ConfigTypeId = 2 },
            new { Id = 197, Name = "optionValue", ValueType = "options", FieldTypeId = 14, ConfigTypeId = 2 },
            new { Id = 198, Name = "required", ValueType = "boolean", FieldTypeId = 14, ConfigTypeId = 1 },
            new { Id = 199, Name = "dependOn", ValueType = "text", FieldTypeId = 15, ConfigTypeId = 1 },
            new { Id = 200, Name = "unique-id", ValueType = "text", FieldTypeId = 15, ConfigTypeId = 1 },
            new { Id = 201, Name = "display-name", ValueType = "text", FieldTypeId = 15, ConfigTypeId = 1 },
            new { Id = 202, Name = "name", ValueType = "text", FieldTypeId = 15, ConfigTypeId = 1 },
            new { Id = 203, Name = "description", ValueType = "text", FieldTypeId = 15, ConfigTypeId = 1 },
            new { Id = 204, Name = "is-visible", ValueType = "boolean", FieldTypeId = 15, ConfigTypeId = 1 },
            new { Id = 205, Name = "options", ValueType = "array", FieldTypeId = 15, ConfigTypeId = 2 },
            new { Id = 206, Name = "placeholder", ValueType = "text", FieldTypeId = 15, ConfigTypeId = 1 },
            new { Id = 207, Name = "display", ValueType = "options", FieldTypeId = 15, ConfigTypeId = 2 },
            new { Id = 208, Name = "optionLabel", ValueType = "text", FieldTypeId = 15, ConfigTypeId = 2 },
            new { Id = 209, Name = "maxSelectedLabels", ValueType = "number", FieldTypeId = 15, ConfigTypeId = 2 },
            new { Id = 210, Name = "optionGroupLabel", ValueType = "text", FieldTypeId = 15, ConfigTypeId = 2 },
            new { Id = 211, Name = "optionGroupChildren", ValueType = "options", FieldTypeId = 15, ConfigTypeId = 2 },
            new { Id = 212, Name = "optionGroupTemplate", ValueType = "template", FieldTypeId = 15, ConfigTypeId = 2 },
            new { Id = 213, Name = "panelFooterTemplate", ValueType = "template", FieldTypeId = 15, ConfigTypeId = 2 },
            new { Id = 214, Name = "itemTemplate", ValueType = "template", FieldTypeId = 15, ConfigTypeId = 2 },
            new { Id = 215, Name = "filter", ValueType = "boolean", FieldTypeId = 15, ConfigTypeId = 2 },
            new { Id = 216, Name = "loading", ValueType = "boolean", FieldTypeId = 15, ConfigTypeId = 2 },
            new { Id = 217, Name = "FloatLable", ValueType = "text", FieldTypeId = 15, ConfigTypeId = 2 },
            new { Id = 218, Name = "variant", ValueType = "options", FieldTypeId = 15, ConfigTypeId = 2 },
            new { Id = 219, Name = "invalid", ValueType = "boolean", FieldTypeId = 15, ConfigTypeId = 2 },
            new { Id = 220, Name = "disabled", ValueType = "boolean", FieldTypeId = 15, ConfigTypeId = 2 },
            new { Id = 221, Name = "required", ValueType = "boolean", FieldTypeId = 15, ConfigTypeId = 3 },
            new { Id = 222, Name = "dependOn", ValueType = "text", FieldTypeId = 16, ConfigTypeId = 1 },
            new { Id = 223, Name = "unique-id", ValueType = "text", FieldTypeId = 16, ConfigTypeId = 1 },
            new { Id = 224, Name = "display-name", ValueType = "text", FieldTypeId = 16, ConfigTypeId = 1 },
            new { Id = 225, Name = "name", ValueType = "text", FieldTypeId = 16, ConfigTypeId = 1 },
            new { Id = 226, Name = "description", ValueType = "text", FieldTypeId = 16, ConfigTypeId = 1 },
            new { Id = 227, Name = "is-visible", ValueType = "boolean", FieldTypeId = 16, ConfigTypeId = 3 },
            new { Id = 228, Name = "field", ValueType = "string", FieldTypeId = 16, ConfigTypeId = 2 },
            new { Id = 229, Name = "placeholder", ValueType = "text", FieldTypeId = 16, ConfigTypeId = 2 },
            new { Id = 230, Name = "rows", ValueType = "number", FieldTypeId = 16, ConfigTypeId = 2 },
            new { Id = 231, Name = "cols", ValueType = "number", FieldTypeId = 16, ConfigTypeId = 2 },
            new { Id = 232, Name = "trigger", ValueType = "options", FieldTypeId = 16, ConfigTypeId = 2 },
            new { Id = 233, Name = "autoResize", ValueType = "code", FieldTypeId = 16, ConfigTypeId = 2 },
            new { Id = 234, Name = "FloatLabel", ValueType = "string", FieldTypeId = 16, ConfigTypeId = 2 },
            new { Id = 235, Name = "variant (Filled)", ValueType = "string", FieldTypeId = 16, ConfigTypeId = 2 },
            new { Id = 236, Name = "Disabled", ValueType = "boolean", FieldTypeId = 16, ConfigTypeId = 2 },
            new { Id = 237, Name = "invalid (True/false)", ValueType = "boolean", FieldTypeId = 16, ConfigTypeId = 2 },
            new { Id = 238, Name = "dependOn", ValueType = "text", FieldTypeId = 17, ConfigTypeId = 1 },
            new { Id = 239, Name = "unique-id", ValueType = "text", FieldTypeId = 17, ConfigTypeId = 1 },
            new { Id = 240, Name = "display-name", ValueType = "text", FieldTypeId = 17, ConfigTypeId = 1 },
            new { Id = 241, Name = "name", ValueType = "text", FieldTypeId = 17, ConfigTypeId = 1 },
            new { Id = 242, Name = "description", ValueType = "text", FieldTypeId = 17, ConfigTypeId = 1 },
            new { Id = 243, Name = "is-visible", ValueType = "boolean", FieldTypeId = 17, ConfigTypeId = 1 },
            new { Id = 244, Name = "rows", ValueType = "number", FieldTypeId = 17, ConfigTypeId = 2 },
            new { Id = 245, Name = "cols", ValueType = "number", FieldTypeId = 17, ConfigTypeId = 2 },
            new { Id = 246, Name = "autoResize", ValueType = "code", FieldTypeId = 17, ConfigTypeId = 2 },
            new { Id = 247, Name = "placeholder", ValueType = "text", FieldTypeId = 17, ConfigTypeId = 2 },
            new { Id = 248, Name = "Key Filter", ValueType = "string", FieldTypeId = 17, ConfigTypeId = 2 },
            new { Id = 249, Name = "FloatLabel", ValueType = "boolean", FieldTypeId = 17, ConfigTypeId = 2 },
            new { Id = 250, Name = "variant (Filled)", ValueType = "string", FieldTypeId = 17, ConfigTypeId = 2 },
            // Field configs 251-300
            new { Id = 251, Name = "Disabled", ValueType = "boolean", FieldTypeId = 17, ConfigTypeId = 2 },
            new { Id = 252, Name = "invalid (True/false)", ValueType = "boolean", FieldTypeId = 17, ConfigTypeId = 2 },
            new { Id = 253, Name = "required", ValueType = "boolean", FieldTypeId = 17, ConfigTypeId = 3 },
            new { Id = 254, Name = "minLength", ValueType = "number", FieldTypeId = 17, ConfigTypeId = 2 },
            new { Id = 255, Name = "maxLength", ValueType = "number", FieldTypeId = 17, ConfigTypeId = 2 },
            new { Id = 256, Name = "dependOn", ValueType = "text", FieldTypeId = 18, ConfigTypeId = 1 },
            new { Id = 257, Name = "unique-id", ValueType = "text", FieldTypeId = 18, ConfigTypeId = 1 },
            new { Id = 258, Name = "display-name", ValueType = "text", FieldTypeId = 18, ConfigTypeId = 1 },
            new { Id = 259, Name = "name", ValueType = "text", FieldTypeId = 18, ConfigTypeId = 1 },
            new { Id = 260, Name = "description", ValueType = "text", FieldTypeId = 18, ConfigTypeId = 1 },
            new { Id = 261, Name = "is-visible", ValueType = "boolean", FieldTypeId = 18, ConfigTypeId = 1 },
            new { Id = 262, Name = "mask", ValueType = "regex", FieldTypeId = 18, ConfigTypeId = 3 },
            new { Id = 263, Name = "required", ValueType = "boolean", FieldTypeId = 18, ConfigTypeId = 3 },
            new { Id = 264, Name = "integerOnly", ValueType = "boolean", FieldTypeId = 18, ConfigTypeId = 2 },
            new { Id = 265, Name = "inputTemplate", ValueType = "template", FieldTypeId = 18, ConfigTypeId = 2 },
            new { Id = 266, Name = "optionLabel", ValueType = "text", FieldTypeId = 12, ConfigTypeId = 2 },
            new { Id = 267, Name = "optionValue", ValueType = "text", FieldTypeId = 12, ConfigTypeId = 2 },
            new { Id = 268, Name = "dependOn", ValueType = "string", FieldTypeId = 19, ConfigTypeId = 1 },
            new { Id = 269, Name = "required", ValueType = "boolean", FieldTypeId = 19, ConfigTypeId = 3 },
            new { Id = 270, Name = "unique-id", ValueType = "string", FieldTypeId = 19, ConfigTypeId = 1 },
            new { Id = 271, Name = "api-id", ValueType = "string", FieldTypeId = 19, ConfigTypeId = 2 },
            new { Id = 272, Name = "display-name", ValueType = "string", FieldTypeId = 19, ConfigTypeId = 1 },
            new { Id = 273, Name = "name", ValueType = "string", FieldTypeId = 19, ConfigTypeId = 1 },
            new { Id = 274, Name = "description", ValueType = "string", FieldTypeId = 19, ConfigTypeId = 1 },
            new { Id = 275, Name = "display-preference", ValueType = "string", FieldTypeId = 19, ConfigTypeId = 2 },
            new { Id = 276, Name = "is-visible", ValueType = "boolean", FieldTypeId = 19, ConfigTypeId = 1 },
            new { Id = 277, Name = "type", ValueType = "string", FieldTypeId = 19, ConfigTypeId = 2 },
            new { Id = 278, Name = "dependOn", ValueType = "string", FieldTypeId = 20, ConfigTypeId = 1 },
            new { Id = 279, Name = "required", ValueType = "boolean", FieldTypeId = 20, ConfigTypeId = 3 },
            new { Id = 280, Name = "unique-id", ValueType = "string", FieldTypeId = 20, ConfigTypeId = 1 },
            new { Id = 281, Name = "api-id", ValueType = "string", FieldTypeId = 20, ConfigTypeId = 2 },
            new { Id = 282, Name = "display-name", ValueType = "string", FieldTypeId = 20, ConfigTypeId = 1 },
            new { Id = 283, Name = "name", ValueType = "string", FieldTypeId = 20, ConfigTypeId = 1 },
            new { Id = 284, Name = "description", ValueType = "string", FieldTypeId = 20, ConfigTypeId = 1 },
            new { Id = 285, Name = "display-preference", ValueType = "string", FieldTypeId = 20, ConfigTypeId = 2 },
            new { Id = 286, Name = "is-visible", ValueType = "boolean", FieldTypeId = 20, ConfigTypeId = 1 },
            new { Id = 287, Name = "type", ValueType = "string", FieldTypeId = 20, ConfigTypeId = 2 },
            new { Id = 288, Name = "dependOn", ValueType = "string", FieldTypeId = 21, ConfigTypeId = 1 },
            new { Id = 289, Name = "required", ValueType = "boolean", FieldTypeId = 21, ConfigTypeId = 3 },
            new { Id = 290, Name = "unique-id", ValueType = "string", FieldTypeId = 21, ConfigTypeId = 1 },
            new { Id = 291, Name = "api-id", ValueType = "string", FieldTypeId = 21, ConfigTypeId = 2 },
            new { Id = 292, Name = "display-name", ValueType = "string", FieldTypeId = 21, ConfigTypeId = 1 },
            new { Id = 293, Name = "name", ValueType = "string", FieldTypeId = 21, ConfigTypeId = 1 },
            new { Id = 294, Name = "description", ValueType = "string", FieldTypeId = 21, ConfigTypeId = 1 },
            new { Id = 295, Name = "display-preference", ValueType = "string", FieldTypeId = 21, ConfigTypeId = 2 },
            new { Id = 296, Name = "is-visible", ValueType = "boolean", FieldTypeId = 21, ConfigTypeId = 1 },
            new { Id = 297, Name = "type", ValueType = "string", FieldTypeId = 21, ConfigTypeId = 2 },
            new { Id = 298, Name = "Group", ValueType = "string", FieldTypeId = 19, ConfigTypeId = 2 },
            new { Id = 299, Name = "Dynamic", ValueType = "boolean", FieldTypeId = 19, ConfigTypeId = 2 },
            new { Id = 300, Name = "Invalid (True/false)", ValueType = "boolean", FieldTypeId = 19, ConfigTypeId = 2 },
            // Field configs 301-369
            new { Id = 301, Name = "Filled", ValueType = "string", FieldTypeId = 19, ConfigTypeId = 2 },
            new { Id = 302, Name = "Disabled", ValueType = "boolean", FieldTypeId = 19, ConfigTypeId = 2 },
            new { Id = 303, Name = "Accessibility", ValueType = "string", FieldTypeId = 19, ConfigTypeId = 2 },
            new { Id = 304, Name = "Group", ValueType = "string", FieldTypeId = 20, ConfigTypeId = 2 },
            new { Id = 305, Name = "Dynamic", ValueType = "boolean", FieldTypeId = 20, ConfigTypeId = 2 },
            new { Id = 306, Name = "Invalid (True/false)", ValueType = "boolean", FieldTypeId = 20, ConfigTypeId = 2 },
            new { Id = 307, Name = "Filled", ValueType = "string", FieldTypeId = 20, ConfigTypeId = 2 },
            new { Id = 308, Name = "Disabled", ValueType = "boolean", FieldTypeId = 20, ConfigTypeId = 2 },
            new { Id = 309, Name = "Accessibility", ValueType = "string", FieldTypeId = 20, ConfigTypeId = 2 },
            new { Id = 310, Name = "Preselection", ValueType = "string", FieldTypeId = 21, ConfigTypeId = 2 },
            new { Id = 311, Name = "Disabled", ValueType = "boolean", FieldTypeId = 21, ConfigTypeId = 2 },
            new { Id = 312, Name = "Invalid (True/false)", ValueType = "boolean", FieldTypeId = 21, ConfigTypeId = 2 },
            new { Id = 313, Name = "Accessibility", ValueType = "string", FieldTypeId = 21, ConfigTypeId = 2 },
            new { Id = 315, Name = "display-name", ValueType = "string", FieldTypeId = 3, ConfigTypeId = 1 },
            new { Id = 316, Name = "Placeholder", ValueType = "string", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 317, Name = "Name", ValueType = "string", FieldTypeId = 3, ConfigTypeId = 1 },
            new { Id = 318, Name = "display-name", ValueType = "string", FieldTypeId = 6, ConfigTypeId = 1 },
            new { Id = 319, Name = "display-name", ValueType = "string", FieldTypeId = 8, ConfigTypeId = 1 },
            new { Id = 320, Name = "readOnly", ValueType = "boolean", FieldTypeId = 8, ConfigTypeId = 1 },
            new { Id = 321, Name = "label", ValueType = "string", FieldTypeId = 19, ConfigTypeId = 2 },
            new { Id = 322, Name = "label", ValueType = "string", FieldTypeId = 20, ConfigTypeId = 2 },
            new { Id = 323, Name = "is-visible", ValueType = "boolean", FieldTypeId = 23, ConfigTypeId = 1 },
            new { Id = 324, Name = "postapiurls", ValueType = "string", FieldTypeId = 23, ConfigTypeId = 1 },
            new { Id = 325, Name = "updateapiurls", ValueType = "string", FieldTypeId = 23, ConfigTypeId = 1 },
            new { Id = 326, Name = "postapijson", ValueType = "string", FieldTypeId = 23, ConfigTypeId = 1 },
            new { Id = 327, Name = "updateapijson", ValueType = "string", FieldTypeId = 23, ConfigTypeId = 1 },
            new { Id = 328, Name = "is-visible", ValueType = "boolean", FieldTypeId = 22, ConfigTypeId = 1 },
            new { Id = 329, Name = "showButtons", ValueType = "boolean", FieldTypeId = 2, ConfigTypeId = 2 },
            new { Id = 330, Name = "isrelative", ValueType = "boolean", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 331, Name = "previnterval", ValueType = "integer", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 332, Name = "nextinterval", ValueType = "integer", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 333, Name = "previntervalunit", ValueType = "text", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 334, Name = "nextintervalunit", ValueType = "text", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 335, Name = "days", ValueType = "text", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 336, Name = "weeks", ValueType = "text", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 337, Name = "months", ValueType = "text", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 338, Name = "year", ValueType = "text", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 339, Name = "isdefault", ValueType = "text", FieldTypeId = 3, ConfigTypeId = 2 },
            new { Id = 340, Name = "display-name", ValueType = "text", FieldTypeId = 3, ConfigTypeId = 1 },
            new { Id = 341, Name = "dependOn", ValueType = "text", FieldTypeId = 24, ConfigTypeId = 1 },
            new { Id = 342, Name = "unique-id", ValueType = "text", FieldTypeId = 24, ConfigTypeId = 1 },
            new { Id = 343, Name = "display-name", ValueType = "text", FieldTypeId = 24, ConfigTypeId = 1 },
            new { Id = 344, Name = "name", ValueType = "text", FieldTypeId = 24, ConfigTypeId = 1 },
            new { Id = 345, Name = "description", ValueType = "text", FieldTypeId = 24, ConfigTypeId = 1 },
            new { Id = 346, Name = "is-visible", ValueType = "boolean", FieldTypeId = 24, ConfigTypeId = 1 },
            new { Id = 347, Name = "get-api-url", ValueType = "url", FieldTypeId = 24, ConfigTypeId = 1 },
            new { Id = 348, Name = "placeholder", ValueType = "text", FieldTypeId = 24, ConfigTypeId = 1 },
            new { Id = 349, Name = "checkmark", ValueType = "boolean", FieldTypeId = 24, ConfigTypeId = 2 },
            new { Id = 350, Name = "highlightOnSelect", ValueType = "boolean", FieldTypeId = 24, ConfigTypeId = 2 },
            new { Id = 351, Name = "editable", ValueType = "boolean", FieldTypeId = 24, ConfigTypeId = 2 },
            new { Id = 352, Name = "optionGroupLabel", ValueType = "text", FieldTypeId = 24, ConfigTypeId = 2 },
            new { Id = 353, Name = "optionGroupChildren", ValueType = "options", FieldTypeId = 24, ConfigTypeId = 2 },
            new { Id = 354, Name = "optionGroupTemplate", ValueType = "template", FieldTypeId = 24, ConfigTypeId = 2 },
            new { Id = 355, Name = "valueTemplate", ValueType = "template", FieldTypeId = 24, ConfigTypeId = 2 },
            new { Id = 356, Name = "itemTemplate", ValueType = "template", FieldTypeId = 24, ConfigTypeId = 2 },
            new { Id = 357, Name = "panelFooterTemplate", ValueType = "template", FieldTypeId = 24, ConfigTypeId = 2 },
            new { Id = 358, Name = "filter", ValueType = "boolean", FieldTypeId = 24, ConfigTypeId = 2 },
            new { Id = 359, Name = "showClear", ValueType = "boolean", FieldTypeId = 24, ConfigTypeId = 2 },
            new { Id = 360, Name = "loading", ValueType = "boolean", FieldTypeId = 24, ConfigTypeId = 2 },
            new { Id = 361, Name = "virtualScrollerOptions", ValueType = "boolean", FieldTypeId = 24, ConfigTypeId = 2 },
            new { Id = 362, Name = "FloatLable", ValueType = "text", FieldTypeId = 24, ConfigTypeId = 2 },
            new { Id = 363, Name = "variant", ValueType = "options", FieldTypeId = 24, ConfigTypeId = 2 },
            new { Id = 364, Name = "invalid", ValueType = "boolean", FieldTypeId = 24, ConfigTypeId = 2 },
            new { Id = 365, Name = "disabled", ValueType = "boolean", FieldTypeId = 24, ConfigTypeId = 2 },
            new { Id = 366, Name = "optionLabel", ValueType = "text", FieldTypeId = 24, ConfigTypeId = 2 },
            new { Id = 367, Name = "optionValue", ValueType = "text", FieldTypeId = 24, ConfigTypeId = 2 },
            new { Id = 368, Name = "dataset", ValueType = "text", FieldTypeId = 24, ConfigTypeId = 2 },
            new { Id = 369, Name = "value", ValueType = "text", FieldTypeId = 24, ConfigTypeId = 2 }
        };

        var seedData = fieldConfigs.Select(fc => new FieldConfig
        {
            Id = fc.Id,
            ConfigName = fc.Name,
            IsActive = true,
            ValueType = fc.ValueType,
            FieldTypeId = fc.FieldTypeId,
            ConfigTypeId = fc.ConfigTypeId,
            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
            CreatedBy = "System"
        }).ToArray();

        modelBuilder.Entity<FieldConfig>().HasData(seedData);
    }

    public override int SaveChanges()
    {
        UpdateAuditFields();
        return base.SaveChanges();
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        UpdateAuditFields();
        return await base.SaveChangesAsync(cancellationToken);
    }

    private void UpdateAuditFields()
    {
        var currentUser = _auditService?.GetCurrentUser() ?? "system";
        var currentTime = DateTime.UtcNow;

        // Handle BaseEntity entities
        var baseEntityEntries = ChangeTracker.Entries<BaseEntity>();
        foreach (var entry in baseEntityEntries)
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedAt = currentTime;
                    entry.Entity.CreatedBy = currentUser;
                    break;

                case EntityState.Modified:
                    entry.Entity.ModifiedAt = currentTime;
                    entry.Entity.ModifiedBy = currentUser;
                    // Ensure CreatedAt and CreatedBy are not modified
                    entry.Property(e => e.CreatedAt).IsModified = false;
                    entry.Property(e => e.CreatedBy).IsModified = false;
                    break;
            }
        }

        // Handle User entities (which don't inherit from BaseEntity)
        var userEntries = ChangeTracker.Entries<User>();
        foreach (var entry in userEntries)
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedAt = currentTime;
                    entry.Entity.CreatedBy = currentUser;
                    break;

                case EntityState.Modified:
                    entry.Entity.ModifiedAt = currentTime;
                    entry.Entity.ModifiedBy = currentUser;
                    // Ensure CreatedAt and CreatedBy are not modified
                    entry.Property(e => e.CreatedAt).IsModified = false;
                    entry.Property(e => e.CreatedBy).IsModified = false;
                    break;
            }
        }
    }
}
