using System.ComponentModel.DataAnnotations;

namespace CMS.WebApi.Models.Requests;

public class UpdateUserProfileRequest
{
    /// <summary>
    /// User's email address
    /// </summary>
    [EmailAddress(ErrorMessage = "Invalid email format")]
    [StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
    public string? Email { get; set; }

    /// <summary>
    /// User's username
    /// </summary>
    [StringLength(50, ErrorMessage = "Username cannot exceed 50 characters")]
    public string? Username { get; set; }
}
