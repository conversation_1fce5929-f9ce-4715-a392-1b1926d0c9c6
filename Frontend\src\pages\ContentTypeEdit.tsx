
import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, Edit, FilePlus, Plus, Trash2, Layers, Eye, List, ArrowUpDown, SortAsc } from 'lucide-react';
import { collectionsApi, collectionFieldsApi, fieldTypesApi, fieldConfigsApi, collectionFieldConfigsApi, collectionComponentsApi, componentsApi, collectionOrderingApi } from '@/lib/api';
import { useCollectionStore, Field, FieldTypeEnum } from '@/lib/store';
import { useToast } from '@/hooks/use-toast';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import DirectFieldTypeSelector from '@/components/content-type/DirectFieldTypeSelector';
import FieldConfigDialog from '@/components/content-type/FieldConfigDialog';
import ComponentSelectionDialog from '@/components/content-type/ComponentSelectionDialog';
import NestedFieldsDisplay from '@/components/content-type/NestedFieldsDisplay';
import DisplayPreferencesEditor from '@/components/content-type/DisplayPreferencesEditor';
import CollectionComponentEditDialog from '@/components/content-type/CollectionComponentEditDialog';
import { CollectionFieldDisplayPreferencesEditor } from '@/components/collection-field/CollectionFieldDisplayPreferencesEditor';

export default function ContentTypeEdit() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const {
    collections,
    selectedCollection,
    setSelectedCollection,
    updateCollection,
    setLoading,
    loading
  } = useCollectionStore();

  const [fieldTypeSelectorOpen, setFieldTypeSelectorOpen] = React.useState(false);

  // Debug state changes for fieldTypeSelectorOpen
  React.useEffect(() => {
    console.log('fieldTypeSelectorOpen changed:', fieldTypeSelectorOpen);
  }, [fieldTypeSelectorOpen]);

  // Field config related state variables
  const [fieldConfigOpen, setFieldConfigOpen] = React.useState(false);
  const [selectedFieldType, setSelectedFieldType] = React.useState<FieldTypeEnum | null>(null);
  const [selectedFieldTypeId, setSelectedFieldTypeId] = React.useState<number | undefined>(undefined);
  const [fieldToEdit, setFieldToEdit] = React.useState<Field | undefined>(undefined);
  const [deleteFieldDialogOpen, setDeleteFieldDialogOpen] = React.useState(false);
  const [fieldToDelete, setFieldToDelete] = React.useState<string | null>(null);
  const [isSaving, setIsSaving] = React.useState(false);
  const [displayPreferencesOpen, setDisplayPreferencesOpen] = React.useState(false);
  const [collectionFieldDisplayPreferencesOpen, setCollectionFieldDisplayPreferencesOpen] = React.useState(false);

  // Component selection dialog state
  const [componentSelectionOpen, setComponentSelectionOpen] = React.useState(false);

  // Component edit dialog state
  const [componentEditOpen, setComponentEditOpen] = React.useState(false);
  const [componentToEdit, setComponentToEdit] = React.useState<any>(null);
  const [editNameDialogOpen, setEditNameDialogOpen] = React.useState(false);
  const [editingName, setEditingName] = React.useState('');

  // Function to fetch collection details - can be called from other functions
  const fetchCollectionDetails = React.useCallback(async (collectionId: string) => {
    if (!collectionId) return;

    setLoading(true);
    try {
      // Add a timestamp to prevent caching
      const timestamp = new Date().getTime();
      const response = await collectionsApi.getById(collectionId);

      if (!response.data) {
        throw new Error('No collection data received');
      }

      // Also fetch the ordered items to ensure we have the latest display preferences
      const orderedItemsResponse = await collectionOrderingApi.getOrderedItems(collectionId);

      // Get the ordered items data for display preferences
      const { components: orderedComponents = [], fields: orderedFields = [] } = orderedItemsResponse.data || {};

      // Create maps for quick lookup of display preferences
      const componentDisplayPrefs = new Map();
      const fieldDisplayPrefs = new Map();

      // Populate the maps
      orderedComponents.forEach((component: any) => {
        if (component.id) {
          componentDisplayPrefs.set(component.id.toString(), component.displayPreference);
        }
      });

      orderedFields.forEach((field: any) => {
        if (field.id) {
          fieldDisplayPrefs.set(field.id.toString(), field.displayPreference);
        }
      });

      // Format the collection data to match the expected structure
      const formattedCollection = {
        id: response.data.id.toString(),
        name: response.data.collectionName,
        apiId: response.data.collectionApiId,
        apiIdPlural: '',
        draftAndPublish: false,
        isInternationally: false,
        fields: [],
        isActive: true,
        createdAt: '',
        updatedAt: ''
      };

      // Try to parse additional information if available
      if (response.data.additionalInformation) {
        try {
          const additionalInfo = JSON.parse(response.data.additionalInformation);
          formattedCollection.apiIdPlural = additionalInfo.apiIdPlural || '';
          formattedCollection.draftAndPublish = additionalInfo.draftAndPublish || false;
          formattedCollection.isInternationally = additionalInfo.isInternationally || false;
        } catch (parseError) {
          console.error('Error parsing additional information:', parseError);
        }
      }

      // Fetch the fields for this collection
      try {
        console.log('Fetching fields for collection ID:', collectionId);
        const fieldsResponse = await collectionFieldsApi.getByCollectionId(collectionId);
        console.log('Fields data received:', fieldsResponse.data);

        // Debug each field's raw data
        if (fieldsResponse.data && Array.isArray(fieldsResponse.data)) {
          console.log('=== RAW FIELD DATA DEBUG ===');
          fieldsResponse.data.forEach((fieldData, index) => {
            console.log(`Raw Field ${index + 1}:`, {
              id: fieldData.id,
              fieldTypeId: fieldData.fieldTypeId,
              fieldType: fieldData.fieldType,
              additionalInformation: fieldData.additionalInformation,
              displayPreference: fieldData.displayPreference,
              rawFieldData: fieldData
            });
          });
          console.log('=== END RAW FIELD DATA DEBUG ===');
        }

        // Process fields from collection_fields table
        const processedFields = [];

        if (fieldsResponse.data && Array.isArray(fieldsResponse.data)) {
          // Process each field
          const fieldResults = await Promise.all(fieldsResponse.data.map(async (fieldData) => {
            // Get the field type information
            let fieldType = FieldTypeEnum.TEXT; // Default to TEXT
            let fieldTypeId = fieldData.fieldType?.id;

            try {
              // Parse the additional information to get field metadata
              let fieldMetadata = {};
              if (fieldData.additionalInformation) {
                fieldMetadata = JSON.parse(fieldData.additionalInformation);
                console.log(`Field ${fieldData.id} metadata:`, fieldMetadata);
              }

              // If the type is specified in the metadata, use it
              if (fieldMetadata.type) {
                fieldType = fieldMetadata.type;
                console.log(`Field ${fieldData.id} type from metadata:`, fieldType);
              }

              // Use fieldTypeId to determine the correct FieldTypeEnum if metadata doesn't have type
              if (!fieldMetadata.type && fieldData.fieldTypeId) {
                fieldType = getFieldTypeEnumFromId(fieldData.fieldTypeId);
                console.log(`Field ${fieldData.id} type from fieldTypeId ${fieldData.fieldTypeId}:`, fieldType);
              }

              // Get display preference from the ordered items if available, otherwise use the field's value
              const fieldId = fieldData.id.toString();
              const displayPref = fieldDisplayPrefs.has(fieldId)
                ? fieldDisplayPrefs.get(fieldId)
                : fieldData.displayPreference;

              const extractedName = fieldMetadata.name || 'Unnamed Field';
              console.log(`Field ${fieldData.id} extracted name:`, extractedName);

              const processedField = {
                id: fieldId,
                name: extractedName,
                apiId: fieldMetadata.apiId || `field_${fieldData.id}`,
                type: fieldType,
                fieldTypeId: fieldTypeId,
                required: fieldMetadata.required || false,
                unique: fieldMetadata.unique || false,
                description: fieldMetadata.description || '',
                attributes: fieldMetadata.attributes || {},
                validations: {
                  required: fieldMetadata.required || false,
                  ...(fieldMetadata.validations || {})
                },
                displayPreference: displayPref // Use the display preference from ordered items
              };

              console.log(`Processed field ${fieldData.id}:`, processedField);
              return processedField;
            } catch (parseError) {
              console.error('Error processing field:', parseError);
              return null;
            }
          }));

          // Filter out any null values from processing errors
          processedFields.push(...fieldResults.filter(field => field !== null));
        }

        // Now fetch components from collection_components table
        try {
          console.log('Fetching components for collection ID:', collectionId);
          // Add a timestamp to prevent caching
          const componentsTimestamp = new Date().getTime();
          const componentsResponse = await collectionComponentsApi.getByCollectionId(`${collectionId}?t=${componentsTimestamp}`);
          console.log('Components data received:', componentsResponse.data);

          if (componentsResponse.data && Array.isArray(componentsResponse.data)) {
            // Process each component
            const componentResults = await Promise.all(componentsResponse.data.map(async (componentData) => {
              try {
                // Get component details
                const componentDetails = await componentsApi.getById(componentData.component.id);
                console.log('Component details:', componentDetails.data);
                console.log('Component display preference:', componentData.displayPreference);

                // Parse additional information if available
                let componentMetadata = {};
                if (componentData.additionalInformation) {
                  try {
                    componentMetadata = JSON.parse(componentData.additionalInformation);
                  } catch (parseError) {
                    console.error('Error parsing component additional information:', parseError);
                  }
                }

                // Get display preference from the ordered items if available, otherwise use the component's value
                const componentId = componentData.id.toString();
                const displayPref = componentDisplayPrefs.has(componentId)
                  ? componentDisplayPrefs.get(componentId)
                  : componentData.displayPreference;

                // Use Display Name from entity first, then from metadata, then name, then component name
                const componentName = componentData.displayName || componentMetadata.displayName || componentMetadata.name || componentDetails.data.componentName || 'Unnamed Component';

                // Create a field-like object for the component
                return {
                  id: `component_${componentData.id}`,
                  name: componentName,
                  apiId: componentMetadata.apiId || `component_${componentDetails.data.componentApiId}`,
                  type: FieldTypeEnum.COMPONENT,
                  componentId: componentData.component.id.toString(),
                  description: componentMetadata.description || componentDetails.data.componentDesc || '',
                  attributes: {
                    componentId: componentData.component.id.toString(),
                    collectionComponentId: componentData.id.toString(),
                    isRepeatable: componentData.isRepeatable || false
                  },
                  validations: {},
                  displayPreference: displayPref // Use the display preference from ordered items
                };
              } catch (componentError) {
                console.error('Error processing component:', componentError);
                return null;
              }
            }));

            // Add valid components to the fields array
            const validComponents = componentResults.filter(component => component !== null);
            processedFields.push(...validComponents);

            // Sort all fields by display preference
            processedFields.sort((a, b) => {
              const prefA = typeof a.displayPreference === 'number' ? a.displayPreference : 999;
              const prefB = typeof b.displayPreference === 'number' ? b.displayPreference : 999;
              return prefA - prefB;
            });

            // Update display preferences to ensure they are in 10, 20, 30 format
            processedFields.forEach((field, index) => {
              field.displayPreference = (index + 1) * 10;
            });
          }
        } catch (componentsError) {
          console.error('Error fetching components:', componentsError);
          // Continue with just the fields
        }

        // Set the combined fields and components
        formattedCollection.fields = processedFields;
      } catch (fieldsError) {
        console.error('Error fetching fields:', fieldsError);
        // Continue with empty fields array
      }

      setSelectedCollection(formattedCollection);
    } catch (error) {
      console.error('Error fetching collection:', error);
      // Removed error toast notification
      navigate('/content-types');
    } finally {
      setLoading(false);
    }
  }, [setSelectedCollection, navigate, toast, setLoading]);

  // Fetch collection details on component mount
  React.useEffect(() => {
    if (!selectedCollection || selectedCollection.id !== id) {
      if (id) {
        fetchCollectionDetails(id);
      }
    }
  }, [id, selectedCollection, fetchCollectionDetails]);

  // Handle adding a new field - show the field config dialog
  const handleAddField = (fieldType: FieldTypeEnum, fieldTypeId?: number) => {
    console.log('handleAddField called with fieldType:', fieldType, 'fieldTypeId:', fieldTypeId);
    console.log('fieldTypeId type:', typeof fieldTypeId);

    setSelectedFieldType(fieldType);
    setFieldToEdit(undefined);

    // Store the field type ID if provided
    if (fieldTypeId !== undefined) {
      console.log(`Setting selected field type ID: ${fieldTypeId}`);
      setSelectedFieldTypeId(fieldTypeId);
    } else {
      console.log('No fieldTypeId provided, setting to undefined');
      setSelectedFieldTypeId(undefined);
    }

    // Log after state update using setTimeout
    setTimeout(() => {
      console.log('After state update - selectedFieldType:', selectedFieldType);
      console.log('After state update - selectedFieldTypeId:', selectedFieldTypeId);
    }, 0);

    setFieldConfigOpen(true);
  };

  // Helper function to generate a default field name based on field type
  const getDefaultFieldName = (fieldType: FieldTypeEnum): string => {
    switch (fieldType) {
      case FieldTypeEnum.TEXT:
        return 'Text Field';
      case FieldTypeEnum.NUMBER:
        return 'Number Field';
      case FieldTypeEnum.DATE:
        return 'Date Field';
      case FieldTypeEnum.BOOLEAN:
        return 'Boolean Field';
      case FieldTypeEnum.RICH_TEXT:
        return 'Rich Text Field';
      case FieldTypeEnum.EMAIL:
        return 'Email Field';
      case FieldTypeEnum.PASSWORD:
        return 'Password Field';
      case FieldTypeEnum.ENUM:
        return 'Enum Field';
      case FieldTypeEnum.JSON:
        return 'JSON Field';
      case FieldTypeEnum.MEDIA:
        return 'Media Field';
      case FieldTypeEnum.RELATION:
        return 'Relation Field';
      case FieldTypeEnum.COMPONENT:
        return 'Component Field';
      case FieldTypeEnum.DYNAMIC_ZONE:
        return 'Dynamic Zone Field';
      case FieldTypeEnum.INPUT_MASK:
        return 'Input Mask Field';
      case FieldTypeEnum.INPUT_TEXTAREA:
        return 'Textarea Field';
      default:
        return 'New Field';
    }
  };

  // Map fieldTypeId to FieldTypeEnum
  const getFieldTypeEnumFromId = (fieldTypeId: number): FieldTypeEnum => {
    switch (fieldTypeId) {
      case 1: return FieldTypeEnum.TEXT;
      case 2: return FieldTypeEnum.NUMBER;
      case 3: return FieldTypeEnum.DATE;
      case 4: return FieldTypeEnum.IMAGE;
      case 5: return FieldTypeEnum.RICH_TEXT;
      case 6: return FieldTypeEnum.MASK;
      case 7: return FieldTypeEnum.CALENDAR;
      case 8: return FieldTypeEnum.EDITOR;
      case 9: return FieldTypeEnum.PASSWORD;
      case 10: return FieldTypeEnum.AUTOCOMPLETE;
      case 11: return FieldTypeEnum.CASCADE_SELECT;
      case 12: return FieldTypeEnum.DROPDOWN;
      case 13: return FieldTypeEnum.FILE;
      case 14: return FieldTypeEnum.MULTI_STATE_CHECKBOX;
      case 15: return FieldTypeEnum.MULTI_SELECT;
      case 16: return FieldTypeEnum.MENTION;
      case 17: return FieldTypeEnum.TEXTAREA;
      case 18: return FieldTypeEnum.OTP;
      case 19: return FieldTypeEnum.CHECKBOX;
      case 20: return FieldTypeEnum.RADIO_BUTTON;
      case 21: return FieldTypeEnum.INPUT_SWITCH;
      case 22: return FieldTypeEnum.BOOLEAN;
      case 23: return FieldTypeEnum.JSON;
      case 24: return FieldTypeEnum.ENUMERATION;
      case 25: return FieldTypeEnum.EMAIL;
      case 26: return FieldTypeEnum.MEDIA;
      case 27: return FieldTypeEnum.RELATION;
      case 28: return FieldTypeEnum.UID;
      case 29: return FieldTypeEnum.COMPONENT;
      case 30: return FieldTypeEnum.DYNAMIC_ZONE;
      case 31: return FieldTypeEnum.INPUT_MASK;
      case 32: return FieldTypeEnum.INPUT_TEXTAREA;
      default:
        console.warn(`Unknown field type ID: ${fieldTypeId}, defaulting to TEXT`);
        return FieldTypeEnum.TEXT;
    }
  };

  // Handle editing a field - show the field config dialog
  const handleEditField = async (field: Field) => {
    console.log('Editing field:', field);

    // If this is a component field, open the component edit dialog instead
    if (field.type === FieldTypeEnum.COMPONENT && field.attributes?.collectionComponentId) {
      try {
        // Fetch the collection component details
        const collectionComponentId = field.attributes.collectionComponentId;
        const collectionComponentResponse = await collectionComponentsApi.getById(collectionComponentId);
        const collectionComponentData = collectionComponentResponse.data;

        console.log('Collection component data:', collectionComponentData);

        // Set the component to edit and open the component edit dialog
        setComponentToEdit(collectionComponentData);
        setComponentEditOpen(true);
        return;
      } catch (error) {
        console.error('Error fetching collection component details:', error);
      }
    }

    // For regular fields, continue with the normal field edit flow
    if (field.type === FieldTypeEnum.COMPONENT && field.componentId) {
      try {
        // Fetch component details
        const componentResponse = await componentsApi.getById(field.componentId);
        const componentData = componentResponse.data;
        console.log('Component data:', componentData);

        // Add component details to the field attributes
        field.attributes = {
          ...field.attributes,
          componentDetails: {
            id: componentData.id.toString(),
            name: componentData.componentName,
            apiId: componentData.componentApiId
          }
        };
      } catch (error) {
        console.error('Error fetching component details:', error);
      }
    }

    setFieldToEdit(field);
    // Use fieldTypeId to determine the correct FieldTypeEnum
    const correctFieldType = field.fieldTypeId ? getFieldTypeEnumFromId(field.fieldTypeId) : (field.type as FieldTypeEnum);

    console.log('handleEditField - Field data:', {
      name: field.name,
      type: field.type,
      fieldTypeId: field.fieldTypeId,
      correctFieldType: correctFieldType
    });

    setSelectedFieldType(correctFieldType);
    setSelectedFieldTypeId(field.fieldTypeId);
    setFieldConfigOpen(true);
  };

  // Handle saving a field
  const handleSaveField = async (field: Field) => {
    if (!selectedCollection) {
      console.error('Cannot save field: No collection selected');
      toast({
        title: 'Error',
        description: 'Cannot save field: No collection selected',
        variant: 'destructive',
      });
      return;
    }

    // Validate field data
    if (!field.name || field.name.trim() === '') {
      console.error('Cannot save field: Field name is required');
      toast({
        title: 'Validation Error',
        description: 'Field name is required',
        variant: 'destructive',
      });
      return;
    }

    if (!field.apiId || field.apiId.trim() === '') {
      console.error('Cannot save field: API ID is required');
      toast({
        title: 'Validation Error',
        description: 'API ID is required',
        variant: 'destructive',
      });
      return;
    }

    if (!selectedFieldTypeId) {
      console.error('Cannot save field: Field type ID is required');
      toast({
        title: 'Validation Error',
        description: 'Field type is required',
        variant: 'destructive',
      });
      return;
    }

    console.log('Saving field:', field);
    console.log('Field type:', field.type);
    console.log('Field attributes:', field.attributes);
    console.log('Field validations:', field.validations);

    try {
      // First, get the field type object from the API
      const fieldTypeResponse = await fieldTypesApi.getById(selectedFieldTypeId.toString());
      const fieldType = fieldTypeResponse.data;
      console.log('Field type from API:', fieldType);

      // Get field configurations for this field type
      const fieldConfigsResponse = await fieldConfigsApi.getByFieldType(selectedFieldTypeId.toString());
      const fieldConfigs = fieldConfigsResponse.data;
      console.log('Field configs from API:', fieldConfigs);

      let fieldId;

      if (field.id && !field.id.startsWith('temp-')) {
        // Use existing field ID for updates
        fieldId = parseInt(field.id, 10);
      } else {
        // For new fields, get the next available ID from the backend
        try {
          // This endpoint should be implemented on the backend to return the next available ID
          // If it's not available, we'll fall back to a sequential ID based on timestamp
          const nextIdResponse = await collectionFieldsApi.getNextId();
          fieldId = nextIdResponse.data.nextId;
          console.log('Got next ID from backend:', fieldId);
        } catch (error) {
          // Fallback: use a sequential ID based on timestamp
          // This is not ideal but better than random IDs
          fieldId = Math.floor(Date.now() / 1000);
          console.log('Using fallback ID generation:', fieldId);
        }
      }

      console.log('Using field ID:', fieldId);

      // Prepare field configurations for the new .NET backend format
      const configurations = [];

      console.log('Available field configs:', fieldConfigs);
      console.log('Field attributes:', field.attributes);
      console.log('Field validations:', field.validations);

      // Process attributes
      if (field.attributes) {
        for (const [key, value] of Object.entries(field.attributes)) {
          // Find the corresponding field config
          const config = fieldConfigs.find(c => c.configName === key);
          console.log(`Processing attribute ${key}:`, { value, config });
          if (config && config.id) {
            configurations.push({
              fieldConfigId: config.id,
              configValue: typeof value === 'object' ? JSON.stringify(value) : String(value),
              isActive: true
            });
            console.log(`Added config for ${key} with ID ${config.id}`);
          } else {
            console.warn(`No config found for attribute ${key} or config missing ID:`, config);
          }
        }
      }

      // Process validations
      if (field.validations) {
        for (const [key, value] of Object.entries(field.validations)) {
          // Find the corresponding field config
          const config = fieldConfigs.find(c => c.configName === key);
          console.log(`Processing validation ${key}:`, { value, config });
          if (config && config.id) {
            configurations.push({
              fieldConfigId: config.id,
              configValue: typeof value === 'object' ? JSON.stringify(value) : String(value),
              isActive: true
            });
            console.log(`Added config for ${key} with ID ${config.id}`);
          } else {
            console.warn(`No config found for validation ${key} or config missing ID:`, config);
          }
        }
      }

      // Add basic field properties as configurations
      // Add field name as a configuration
      if (field.name) {
        const nameConfig = fieldConfigs.find(c => c.configName === 'display_name' || c.configName === 'name');
        if (nameConfig) {
          configurations.push({
            fieldConfigId: nameConfig.id,
            configValue: field.name,
            isActive: true
          });
        }
      }

      // Add field description as a configuration
      if (field.description) {
        const descConfig = fieldConfigs.find(c => c.configName === 'description');
        if (descConfig) {
          configurations.push({
            fieldConfigId: descConfig.id,
            configValue: field.description,
            isActive: true
          });
        }
      }

      // Add required validation as a configuration
      if (field.required !== undefined) {
        const requiredConfig = fieldConfigs.find(c => c.configName === 'required');
        if (requiredConfig) {
          configurations.push({
            fieldConfigId: requiredConfig.id,
            configValue: field.required ? 'true' : 'false',
            isActive: true
          });
        }
      }

      console.log(`Total configurations prepared: ${configurations.length}`);

      // Create a properly formatted field object for the new .NET backend
      const collectionFieldData = {
        // Only include ID for updates, not for new fields
        ...(field.id && !field.id.startsWith('temp-') ? { id: fieldId } : {}),
        collectionId: parseInt(selectedCollection.id, 10),
        fieldTypeId: selectedFieldTypeId,
        // Don't set displayPreference - let the backend handle it automatically
        // Store additional data as JSON string
        additionalInformation: JSON.stringify({
          name: field.name,
          apiId: field.apiId,
          description: field.description || '',
          required: field.required,
          unique: field.unique,
          type: field.type, // Store the actual field type enum value
          attributes: field.attributes || {},
          validations: {
            required: field.required || false,
            ...(field.validations || {})
          }
        }),
        configurations: configurations
      };

      console.log('Collection field data for backend:', collectionFieldData);
      console.log('Field configurations to be included:', configurations);

      let response;
      if (field.id && !field.id.startsWith('temp-')) {
        // Update existing field
        console.log(`Updating existing field with ID ${field.id}`);
        response = await collectionFieldsApi.update(field.id, collectionFieldData);
      } else {
        // Create new field with configurations using the new endpoint
        console.log(`Creating new field with ${configurations.length} configurations`);
        response = await collectionFieldsApi.createWithConfigs(collectionFieldData);
      }

      // After creating/updating the field, handle component creation if needed
      if (response && response.data && response.data.id) {
        const collectionFieldId = response.data.id;
        console.log('Saved field ID:', collectionFieldId);

        // Get the ordered items to ensure we have the latest display order
        try {
          const orderedItemsResponse = await collectionOrderingApi.getOrderedItems(selectedCollection.id);
          console.log('Ordered items after field save:', orderedItemsResponse.data);
        } catch (orderError) {
          console.error('Error getting ordered items:', orderError);
          // Continue with the process even if ordering fails
        }

        // If this is a component field, create a collection component
        if (field.type === FieldTypeEnum.COMPONENT && field.componentId) {
          console.log('Creating collection component for component ID:', field.componentId);

          try {
            // Create the collection component without specifying an ID
            // Let the backend generate the ID to ensure it's valid in the tenant schema
            const collectionComponentData = {
              // Remove the id field to let the backend generate it
              collection: {
                id: parseInt(selectedCollection.id, 10)
              },
              component: {
                id: parseInt(field.componentId, 10)
              },
              // Don't set displayPreference - let the backend handle it automatically
              // The backend will set it to the max value + 10 (in the 10, 20, 30 format)
              isRepeatable: field.attributes?.isRepeatable === true,
              minRepeatOccurrences: field.attributes?.minRepeatOccurrences || null,
              maxRepeatOccurrences: field.attributes?.maxRepeatOccurrences || null,
              isActive: true
            };

            console.log('Collection component data:', collectionComponentData);

            const componentResponse = await collectionComponentsApi.create(collectionComponentData);
            console.log('Collection component created:', componentResponse.data);

            // Store the collection component ID in the field attributes
            field.attributes = {
              ...field.attributes,
              collectionComponentId: componentResponse.data.id
            };
          } catch (componentError) {
            console.error('Error creating collection component:', componentError);
            // Continue with the process even if component creation fails
          }
        }
      }

      console.log('API response:', response);

      // Create a formatted field object for the frontend state
      const formattedField = {
        ...field,
        id: response.data.id.toString(),
        fieldTypeId: selectedFieldTypeId,
        // Ensure the field type is correctly set
        type: field.type
      };

      console.log('Formatted field for frontend:', formattedField);

      // Update the local state
      let updatedFields: Field[];
      if (field.id && !field.id.startsWith('temp-')) {
        // Update existing field
        updatedFields = selectedCollection.fields.map(f =>
          f.id === field.id ? formattedField : f
        );
      } else {
        // Add new field
        updatedFields = [
          ...selectedCollection.fields,
          formattedField
        ];
      }

      // Update in state
      updateCollection(selectedCollection.id, { fields: updatedFields });

      toast({
        title: field.id && !field.id.startsWith('temp-') ? 'Field updated' : 'Field added',
        description: `Field "${field.name}" has been ${field.id && !field.id.startsWith('temp-') ? 'updated' : 'added'} successfully`,
      });
    } catch (error: any) {
      console.error('Error saving field:', error);

      // Extract more detailed error information
      let errorMessage = `Failed to ${field.id && !field.id.startsWith('temp-') ? 'update' : 'add'} field`;

      if (error.response) {
        console.error('Error response:', error.response.data);
        if (error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;

          // Check for specific deserialization errors
          if (errorMessage.includes('Cannot deserialize value of type')) {
            errorMessage = 'Type mismatch error: The field data format is incompatible with the backend. Please check numeric fields.';
          }
        } else if (error.response.status === 400) {
          errorMessage = 'Invalid field data. Please check your input.';
        } else if (error.response.status === 409) {
          errorMessage = 'A field with this name or API ID already exists.';
        } else if (error.response.status === 500) {
          errorMessage = 'Server error. Please try again later.';
        }
      } else if (error.request) {
        errorMessage = 'No response from server. Please check your connection.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  };

  // Handle deleting a field
  const confirmDeleteField = (fieldId: string) => {
    setFieldToDelete(fieldId);
    setDeleteFieldDialogOpen(true);
  };

  const handleDeleteField = async () => {
    if (!selectedCollection || !fieldToDelete) return;

    try {
      // Get the field to delete
      const fieldToDeleteObj = selectedCollection.fields.find(f => f.id === fieldToDelete);
      if (!fieldToDeleteObj) {
        throw new Error('Field not found');
      }

      // Check if this is a component field
      const isComponent = fieldToDeleteObj.type === FieldTypeEnum.COMPONENT;

      if (isComponent) {
        // For components, we need to use the collection component API
        // Get the collection component ID from the field attributes
        const collectionComponentId = fieldToDeleteObj.attributes?.collectionComponentId;

        if (!collectionComponentId) {
          throw new Error('Collection component ID not found');
        }

        console.log('Deleting component with collection component ID:', collectionComponentId);

        try {
          // Delete using the collection components API
          await collectionComponentsApi.delete(collectionComponentId.toString());

          toast({
            title: 'Component removed',
            description: `Component "${fieldToDeleteObj.name}" has been removed from the collection`,
          });
        } catch (componentError: any) {
          console.error('Error deleting component:', componentError);

          // Check if the error is related to the component_field_id constraint, verification failure, or server error
          const errorStatus = componentError?.response?.status;
          const errorMessage = componentError?.response?.data?.message || componentError.message || '';

          // Check for 500 Internal Server Error specifically
          const isServerError = errorStatus === 500;

          // Check for other known error patterns
          const isConstraintViolation = errorMessage.includes('constraint') ||
                                       errorMessage.includes('foreign key') ||
                                       errorMessage.includes('component_field_id') ||
                                       errorMessage.includes('Failed to delete collection component with ID');

          // Handle 500 Internal Server Error specifically
          if (isServerError) {
            console.log('500 Internal Server Error detected. This is likely due to the backend verification issue.');

            toast({
              title: 'Component removal issue',
              description: `The component "${fieldToDeleteObj.name}" could not be deleted in the backend due to a server error. It will be removed from the UI only.`,
              variant: 'warning',
            });
          }
          // Handle constraint violations and verification failures
          else if (isConstraintViolation) {
            // Check if it's a verification failure or a constraint violation
            if (errorMessage.includes('Failed to delete collection component with ID')) {
              console.log('Backend verification failure detected. The component may have been deleted but the verification failed.');

              // Show a more specific error message for verification failure
              toast({
                title: 'Component removal issue',
                description: `The component "${fieldToDeleteObj.name}" was removed, but the backend had an issue verifying the deletion. It will be removed from the UI.`,
                variant: 'warning',
              });
            } else {
              console.log('Foreign key constraint violation detected. This is likely due to component fields being referenced elsewhere.');

              // Show a more specific error message for constraint violation
              toast({
                title: 'Component removal issue',
                description: `The component "${fieldToDeleteObj.name}" has fields that are being used elsewhere. It will be removed from the UI only.`,
                variant: 'warning',
              });
            }
          }
          // Generic error handling for other types of errors
          else {
            console.log('Backend deletion failed, but updating UI to remove component');

            toast({
              title: 'Component removed from UI',
              description: `Component "${fieldToDeleteObj.name}" has been removed from the UI, but there was an error in the backend.`,
              variant: 'warning',
            });
          }
        }
      } else {
        // For regular fields, use the collection fields API
        await collectionFieldsApi.delete(fieldToDelete);

        toast({
          title: 'Field deleted',
          description: `Field "${fieldToDeleteObj.name}" has been deleted successfully`,
        });
      }

      // Update the local state
      const updatedFields = selectedCollection.fields.filter(f => f.id !== fieldToDelete);
      updateCollection(selectedCollection.id, { fields: updatedFields });
    } catch (error: any) {
      console.error('Error deleting field:', error);

      // Extract more detailed error information
      let errorMessage = 'Failed to delete field';

      if (error.response) {
        console.error('Error response:', error.response.data);
        if (error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        } else if (error.response.status === 404) {
          errorMessage = 'Field not found. It may have been already deleted.';
        } else if (error.response.status === 500) {
          errorMessage = 'Server error. Please try again later.';
        }
      }

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setDeleteFieldDialogOpen(false);
      setFieldToDelete(null);
    }
  };

  // Handle reordering fields and components
  const handleReorderItems = async (componentIds: number[], fieldIds: number[]) => {
    if (!selectedCollection) {
      console.error('Cannot reorder: No collection selected');
      toast({
        title: 'Error',
        description: 'Cannot reorder: No collection selected',
        variant: 'destructive',
      });
      return;
    }

    try {
      // Validate the input
      if (componentIds.length === 0 && fieldIds.length === 0) {
        console.warn('No component or field IDs provided for reordering');
        toast({
          title: 'Warning',
          description: 'No items to reorder',
          variant: 'default',
        });
        return;
      }

      // Call the ordering API
      const response = await collectionOrderingApi.reorderItems(
        selectedCollection.id,
        { componentIds, fieldIds }
      );

      // Update the local state to reflect the new order
      if (selectedCollection.fields) {
        // Create a map of display preferences
        const displayPrefs = new Map();

        // Set display preferences for components
        componentIds.forEach((id, index) => {
          displayPrefs.set(`component_${id}`, (index + 1) * 10);
        });

        // Set display preferences for fields
        fieldIds.forEach((id, index) => {
          displayPrefs.set(id.toString(), (componentIds.length + index + 1) * 10);
        });

        // Update the fields with new display preferences
        const updatedFields = selectedCollection.fields.map(field => {
          const key = field.type === FieldTypeEnum.COMPONENT && field.attributes?.collectionComponentId
            ? `component_${field.attributes.collectionComponentId}`
            : field.id;

          if (key && displayPrefs.has(key)) {
            return { ...field, displayPreference: displayPrefs.get(key) };
          }
          return field;
        });

        // Sort the fields by display preference
        updatedFields.sort((a, b) => {
          const prefA = typeof a.displayPreference === 'number' ? a.displayPreference : 999;
          const prefB = typeof b.displayPreference === 'number' ? b.displayPreference : 999;
          return prefA - prefB;
        });

        // Update the collection with the sorted fields
        updateCollection(selectedCollection.id, { fields: updatedFields });
      }

      // Add a timestamp to localStorage to force refresh when dialog reopens
      localStorage.setItem(`display_prefs_updated_${selectedCollection.id}`, Date.now().toString());

      // Refresh the collection data to get the updated display preferences
      fetchCollectionDetails(selectedCollection.id);

      // Show success toast
      toast({
        title: 'Success',
        description: 'Display preferences updated successfully',
        variant: 'default',
      });

    } catch (error) {
      console.error('Error reordering items:', error);
      toast({
        title: 'Error',
        description: 'Failed to reorder items',
        variant: 'destructive',
      });
    }
  };

  // Handle saving the collection
  const handleSaveCollection = async () => {
    if (!selectedCollection) return;

    try {
      setIsSaving(true);

      // Prepare the collection data for saving
      const collectionData = {
        id: selectedCollection.id,
        collectionName: selectedCollection.name,
        collectionDesc: selectedCollection.description || `${selectedCollection.name} collection`,
        collectionApiId: selectedCollection.apiId,
        categoryId: selectedCollection.categoryId || 1, // Ensure categoryId is included
        additionalInformation: JSON.stringify({
          apiIdPlural: selectedCollection.apiIdPlural,
          draftAndPublish: selectedCollection.draftAndPublish,
          isInternationally: selectedCollection.isInternationally
        }),
      };

      // Update the collection
      const response = await collectionsApi.update(selectedCollection.id, collectionData);
      console.log('Collection updated successfully:', response.data);

      // Show success toast
      toast({
        title: 'Success',
        description: `Your data has been saved to collection "${selectedCollection.name}"`,
        variant: 'default',
      });

    } catch (error) {
      console.error('Error saving collection:', error);
      toast({
        title: 'Error',
        description: 'Failed to save collection',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle component edit save
  const handleComponentEditSave = async (componentData: any) => {
    try {
      // Update the collection component
      await collectionComponentsApi.update(componentData.id.toString(), componentData);

      toast({
        title: 'Component updated',
        description: 'Component details have been updated successfully',
      });

      // Close the dialog
      setComponentEditOpen(false);
      setComponentToEdit(null);

      // Refresh the collection data to show updated component info
      if (id) {
        fetchCollectionDetails(id);
      }
    } catch (error) {
      console.error('Error updating component:', error);
      // Error handling is done in the dialog component
    }
  };

  // Handle edit collection name
  const handleEditName = () => {
    setEditingName(collectionName);
    setEditNameDialogOpen(true);
  };

  // Handle save collection name
  const handleSaveCollectionName = async () => {
    if (!editingName.trim() || !selectedCollection) {
      toast({
        title: 'Error',
        description: 'Collection name cannot be empty',
        variant: 'destructive',
      });
      return;
    }

    try {
      // Update the collection name
      const updatedCollection = {
        id: selectedCollection.id,
        collectionName: editingName.trim(),
        collectionDesc: selectedCollection.description || `${editingName.trim()} collection`,
        collectionApiId: selectedCollection.apiId,
        categoryId: selectedCollection.categoryId || 1, // Ensure categoryId is included
        additionalInformation: JSON.stringify({
          apiIdPlural: selectedCollection.apiIdPlural,
          draftAndPublish: selectedCollection.draftAndPublish,
          isInternationally: selectedCollection.isInternationally
        }),
      };

      await collectionsApi.update(selectedCollection.id.toString(), updatedCollection);

      // Update local state
      updateCollection(selectedCollection.id, updatedCollection);

      toast({
        title: 'Success',
        description: 'Collection name updated successfully',
      });

      setEditNameDialogOpen(false);
    } catch (error) {
      console.error('Error updating collection name:', error);
      toast({
        title: 'Error',
        description: 'Failed to update collection name',
        variant: 'destructive',
      });
    }
  };

  // Handle component selection
  const handleComponentSelect = async (component: any, isRepeatable: boolean = false, minRepeatOccurrences?: number, maxRepeatOccurrences?: number) => {
    console.log('Selected component:', component);
    console.log('isRepeatable:', isRepeatable);
    console.log('minRepeatOccurrences:', minRepeatOccurrences);
    console.log('maxRepeatOccurrences:', maxRepeatOccurrences);

    if (!selectedCollection) {
      console.error('Cannot add component: No collection selected');
      toast({
        title: 'Error',
        description: 'Cannot add component: No collection selected',
        variant: 'destructive',
      });
      return;
    }

    try {
      // Create the collection component without specifying an ID
      // Let the backend generate the ID to ensure it's valid in the tenant schema
      const collectionComponentData = {
        // Remove the id field to let the backend generate it
        collection: {
          id: parseInt(selectedCollection.id, 10)
        },
        component: {
          id: parseInt(component.id, 10)
        },
        // Don't set displayPreference - let the backend handle it automatically
        isRepeatable: isRepeatable,
        minRepeatOccurrences: isRepeatable ? minRepeatOccurrences : null,
        maxRepeatOccurrences: isRepeatable ? maxRepeatOccurrences : null,
        isActive: true,
        // Add the new collection component fields
        name: component.name,
        displayName: component.displayName || '',
        additionalInfo: component.additionalInfo || '',
        additionalInfoImage: component.additionalInfoImage || null,
        // Add metadata about the component for UI display
        additionalInformation: JSON.stringify({
          name: component.name,
          apiId: `component_${component.apiId}`,
          description: component.description || '',
        })
      };

      console.log('Collection component data:', collectionComponentData);

      // Save the collection component
      const componentResponse = await collectionComponentsApi.create(collectionComponentData);
      console.log('Collection component created:', componentResponse.data);

      // Get the ordered items to ensure we have the latest display order
      const orderedItemsResponse = await collectionOrderingApi.getOrderedItems(selectedCollection.id);
      console.log('Ordered items:', orderedItemsResponse.data);

      // Create a formatted field object for the frontend state only
      // This doesn't create a database entry, just updates the UI
      const formattedField = {
        id: `component_${componentResponse.data.id}`,
        name: component.name,
        apiId: `component_${component.apiId}`,
        type: FieldTypeEnum.COMPONENT,
        required: false,
        unique: false,
        description: component.description || '',
        componentId: component.id,
        attributes: {
          componentId: component.id,
          collectionComponentId: componentResponse.data.id,
          isRepeatable: isRepeatable,
          minRepeatOccurrences: isRepeatable ? minRepeatOccurrences : undefined,
          maxRepeatOccurrences: isRepeatable ? maxRepeatOccurrences : undefined
        },
        validations: {}
      };

      // Update the local state
      const updatedFields = [
        ...selectedCollection.fields,
        formattedField
      ];

      // Update in state
      updateCollection(selectedCollection.id, { fields: updatedFields });

      toast({
        title: 'Component added',
        description: `Component "${component.name}" has been added successfully`,
      });

      // Close the component selection dialog
      setComponentSelectionOpen(false);
    } catch (error: any) {
      console.error('Error adding component:', error);

      let errorMessage = 'Failed to add component';
      let errorDetails = '';

      if (error.response) {
        console.error('Error response:', error.response.data);
        if (error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;

          // Check for specific error types
          if (errorMessage.includes('does not exist in tenant')) {
            errorDetails = 'The collection or component may not exist in the current tenant schema. Try refreshing the page or creating a new collection.';
          } else if (errorMessage.includes('foreign key')) {
            errorDetails = 'There was an issue with the relationship between the collection and component. Try refreshing the page.';
          }
        } else if (error.response.status === 400) {
          errorMessage = 'Invalid component data. Please check your input.';
        } else if (error.response.status === 500) {
          errorMessage = 'Server error. Please try again later.';
          errorDetails = 'There might be an issue with the tenant schema or database connection.';
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: 'Error',
        description: (
          <div>
            <p>{errorMessage}</p>
            {errorDetails && <p className="text-sm mt-2">{errorDetails}</p>}
          </div>
        ),
        variant: 'destructive',
      });
    }
  };

  // Render field type icon
  const renderFieldTypeIcon = (type: FieldTypeEnum) => {
    switch (type) {
      // New field types
      case FieldTypeEnum.TEXT:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">Aa</div>;
      case FieldTypeEnum.NUMBER:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">123</div>;
      case FieldTypeEnum.DATE:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">📅</div>;
      case FieldTypeEnum.IMAGE:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">🖼️</div>;
      case FieldTypeEnum.RICH_TEXT:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">∷</div>;
      case FieldTypeEnum.MASK:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">##</div>;
      case FieldTypeEnum.CALENDAR:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">📆</div>;
      case FieldTypeEnum.EDITOR:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">≡</div>;
      case FieldTypeEnum.PASSWORD:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">🔒</div>;
      case FieldTypeEnum.AUTOCOMPLETE:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">⌨️</div>;
      case FieldTypeEnum.CASCADE_SELECT:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">📂</div>;
      case FieldTypeEnum.DROPDOWN:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">▼</div>;
      case FieldTypeEnum.FILE:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">📄</div>;
      case FieldTypeEnum.MULTI_STATE_CHECKBOX:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">☑️</div>;
      case FieldTypeEnum.MULTI_SELECT:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">⊠</div>;
      case FieldTypeEnum.MENTION:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">@</div>;
      case FieldTypeEnum.TEXTAREA:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">¶</div>;
      case FieldTypeEnum.OTP:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">#</div>;

      // Legacy field types
      case FieldTypeEnum.RICH_TEXT_BLOCKS:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">∷</div>;
      case FieldTypeEnum.RICH_TEXT_MARKDOWN:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">≡</div>;
      case FieldTypeEnum.BOOLEAN:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">⚪</div>;
      case FieldTypeEnum.EMAIL:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">@</div>;
      case FieldTypeEnum.MEDIA:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">🖼️</div>;
      case FieldTypeEnum.ENUMERATION:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">≣</div>;
      case FieldTypeEnum.RELATION:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">🔗</div>;
      case FieldTypeEnum.UID:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">🔑</div>;
      case FieldTypeEnum.JSON:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">{}</div>;
      case FieldTypeEnum.COMPONENT:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">⚙️</div>;
      case FieldTypeEnum.DYNAMIC_ZONE:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">∞</div>;
      case FieldTypeEnum.INPUT_MASK:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">##</div>;
      case FieldTypeEnum.INPUT_TEXTAREA:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">¶</div>;
      default:
        return <div className="w-8 h-8 bg-primary/10 text-primary flex items-center justify-center rounded border border-border">?</div>;
    }
  };

  // Get field type label
  const getFieldTypeLabel = (type: FieldTypeEnum) => {
    switch (type) {
      // New field types
      case FieldTypeEnum.TEXT:
        return 'Text';
      case FieldTypeEnum.NUMBER:
        return 'Number';
      case FieldTypeEnum.DATE:
        return 'Date';
      case FieldTypeEnum.IMAGE:
        return 'Image';
      case FieldTypeEnum.RICH_TEXT:
        return 'Rich Text';
      case FieldTypeEnum.MASK:
        return 'Masked Input';
      case FieldTypeEnum.CALENDAR:
        return 'Calendar';
      case FieldTypeEnum.EDITOR:
        return 'Editor';
      case FieldTypeEnum.PASSWORD:
        return 'Password';
      case FieldTypeEnum.AUTOCOMPLETE:
        return 'Autocomplete';
      case FieldTypeEnum.CASCADE_SELECT:
        return 'Cascade Select';
      case FieldTypeEnum.DROPDOWN:
        return 'Dropdown';
      case FieldTypeEnum.FILE:
        return 'File';
      case FieldTypeEnum.MULTI_STATE_CHECKBOX:
        return 'Multi-State Checkbox';
      case FieldTypeEnum.MULTI_SELECT:
        return 'Multi-Select';
      case FieldTypeEnum.MENTION:
        return 'Mention';
      case FieldTypeEnum.TEXTAREA:
        return 'Textarea';
      case FieldTypeEnum.OTP:
        return 'OTP';

      // Legacy field types
      case FieldTypeEnum.RICH_TEXT_BLOCKS:
        return 'Rich Text (Blocks)';
      case FieldTypeEnum.RICH_TEXT_MARKDOWN:
        return 'Rich Text (Markdown)';
      case FieldTypeEnum.BOOLEAN:
        return 'Boolean';
      case FieldTypeEnum.EMAIL:
        return 'Email';
      case FieldTypeEnum.MEDIA:
        return 'Media';
      case FieldTypeEnum.ENUMERATION:
        return 'Enumeration';
      case FieldTypeEnum.RELATION:
        return 'Relation';
      case FieldTypeEnum.UID:
        return 'UID';
      case FieldTypeEnum.JSON:
        return 'JSON';
      case FieldTypeEnum.COMPONENT:
        return 'Component';
      case FieldTypeEnum.DYNAMIC_ZONE:
        return 'Dynamic Zone';
      case FieldTypeEnum.INPUT_MASK:
        return 'Input Mask';
      case FieldTypeEnum.INPUT_TEXTAREA:
        return 'Input Textarea';
      default:
        return 'Unknown';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" disabled>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="h-8 w-48 bg-gray-200 rounded animate-pulse ml-2"></div>
        </div>
        <Card>
          <CardHeader>
            <div className="h-6 w-3/4 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-4 w-1/2 bg-gray-200 rounded animate-pulse mt-2"></div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-16 bg-gray-200 rounded animate-pulse"></div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!selectedCollection && !loading) {
    return (
      <div className="flex justify-center items-center h-[50vh]">
        <Card className="w-[500px]">
          <CardHeader>
            <CardTitle>Collection not found</CardTitle>
            <CardDescription>
              The collection you are looking for does not exist or has been deleted.
              This could be due to an issue with the database connection or the collection ID.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Collection ID: {id}
            </p>
            <p className="text-sm text-muted-foreground">
              Try creating a new collection or check the database connection.
            </p>
          </CardContent>
          <CardFooter>
            <Button onClick={() => navigate('/content-types')}>
              Go back to Content Types
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  // Create a default title if collection name is missing
  const collectionName = selectedCollection.name || 'New Collection';

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" onClick={() => navigate('/content-types')}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex flex-col ml-2">
            <div className="flex items-center">
              <h1 className="text-3xl font-bold">{collectionName}</h1>
              <Button variant="ghost" size="icon" className="ml-2" onClick={handleEditName}>
                <Edit className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-sm text-muted-foreground">
              Build the data architecture of your content
            </p>
          </div>
        </div>
        <Button onClick={handleSaveCollection} disabled={isSaving}>
          {isSaving ? 'Saving...' : 'Save'}
        </Button>
      </div>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Content Fields</CardTitle>
            <CardDescription>
              Build the data architecture of your content
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setCollectionFieldDisplayPreferencesOpen(true)}
              className="flex items-center bg-blue-500/5 hover:bg-blue-500/10 border-blue-500/20"
            >
              <SortAsc className="mr-2 h-4 w-4 text-blue-600" />
              <span className="text-blue-600 font-medium">Edit Display Preferences</span>
            </Button>
            <Button
              variant="outline"
              onClick={() => setComponentSelectionOpen(true)}
            >
              <Layers className="mr-2 h-4 w-4" />
              Add Component
            </Button>
            <Button onClick={() => {
              console.log('Add another field button clicked');
              setFieldTypeSelectorOpen(true);
              console.log('setFieldTypeSelectorOpen called with true');
            }}>
              <Plus className="mr-2 h-4 w-4" />
              Add Field
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {selectedCollection.fields.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-10 border border-dashed rounded-md">
              <div className="text-center mb-6">
                <div className="flex justify-center mb-4">
                  <img src="/document-icon.svg" alt="Document" className="w-16 h-16" />
                </div>
                <h3 className="text-lg font-medium mb-2">Add your first field to this Collection-Type</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Fields are used to structure your content and store data
                </p>
              </div>
              <div className="flex flex-col gap-4 items-center">
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => setComponentSelectionOpen(true)}
                >
                  <Layers className="mr-2 h-4 w-4" />
                  Add Component
                </Button>
                <Button onClick={() => {
                  console.log('Add new field button clicked');
                  console.log('Current fieldTypeSelectorOpen state:', fieldTypeSelectorOpen);
                  setFieldTypeSelectorOpen(true);
                  // Force a re-render by using setTimeout
                  setTimeout(() => {
                    console.log('After setState, fieldTypeSelectorOpen should be true:', fieldTypeSelectorOpen);
                    // Force another update if needed
                    if (!fieldTypeSelectorOpen) {
                      console.log('fieldTypeSelectorOpen still false, forcing update');
                      setFieldTypeSelectorOpen(true);
                    }
                  }, 100);
                }}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Field
                </Button>
              </div>
            </div>
          ) : (
            <div>
              <NestedFieldsDisplay
                fields={selectedCollection.fields}
                onEditField={handleEditField}
                onDeleteField={confirmDeleteField}
                onAddField={() => setFieldTypeSelectorOpen(true)}
                onAddFieldToComponent={(componentId) => {
                  // TODO: Implement adding a field to a specific component
                  console.log('Add field to component:', componentId);
                  // For now, just open the regular field selector
                  setFieldTypeSelectorOpen(true);
                }}
                onReorderFields={handleReorderItems}
                hideAddFieldToComponent={true} // Hide the "Add field to component" button in collection view
                hideDisplayPreferences={true} // Hide the "Display Preferences" buttons in collection view
              />

              <div className="pt-4 flex justify-center gap-4">
                <Button
                  variant="outline"
                  className="max-w-xs"
                  onClick={() => setComponentSelectionOpen(true)}
                >
                  <Layers className="mr-2 h-4 w-4" />
                  Add Component
                </Button>
                <Button
                  variant="outline"
                  className="max-w-xs"
                  onClick={() => setFieldTypeSelectorOpen(true)}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Add Field
                </Button>

                <Button
                  variant="outline"
                  className="max-w-xs bg-blue-500/5 hover:bg-blue-500/10 border-blue-500/20"
                  onClick={() => setCollectionFieldDisplayPreferencesOpen(true)}
                >
                  <ArrowUpDown className="mr-2 h-4 w-4 text-blue-600" />
                  <span className="text-blue-600">Display Preferences</span>
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Field Type Selector */}
      {console.log('Rendering DirectFieldTypeSelector with open:', fieldTypeSelectorOpen)}
      <DirectFieldTypeSelector
        open={fieldTypeSelectorOpen}
        onClose={() => {
          console.log('DirectFieldTypeSelector onClose called');
          setFieldTypeSelectorOpen(false);
        }}
        onSelect={(type, id) => {
          console.log('DirectFieldTypeSelector onSelect called with type:', type, 'id:', id);
          handleAddField(type, id);
        }}
      />

      {/* Field Configuration Dialog */}
      {selectedFieldType && (
        <FieldConfigDialog
          open={fieldConfigOpen}
          onClose={() => setFieldConfigOpen(false)}
          onSave={handleSaveField}
          field={fieldToEdit}
          fieldType={selectedFieldType}
          fieldTypeId={selectedFieldTypeId}
          onAddAnother={() => {
            setFieldConfigOpen(false);
            setFieldTypeSelectorOpen(true);
          }}
          collectionName={collectionName}
        />
      )}

      {/* Delete Field Confirmation */}
      <Dialog open={deleteFieldDialogOpen} onOpenChange={setDeleteFieldDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete field</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this field? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteFieldDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteField}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Display Preferences Editor */}
      {selectedCollection && (
        <DisplayPreferencesEditor
          isOpen={displayPreferencesOpen}
          onClose={() => setDisplayPreferencesOpen(false)}
          collectionId={selectedCollection.id}
          fields={selectedCollection.fields}
          onSuccess={() => {
            // Refresh the collection data
            if (id) {
              console.log('Refreshing collection data after updating display preferences');
              fetchCollectionDetails(id);

              // Show a toast to confirm the refresh
              toast({
                title: 'Collection Updated',
                description: 'Display preferences have been applied',
                variant: 'default',
              });
            }
          }}
        />
      )}

      {/* Collection Field Display Preferences Editor */}
      {selectedCollection && (
        <CollectionFieldDisplayPreferencesEditor
          open={collectionFieldDisplayPreferencesOpen}
          onOpenChange={setCollectionFieldDisplayPreferencesOpen}
          collectionId={selectedCollection.id}
          fields={selectedCollection.fields}
          onFieldsReordered={() => {
            // Refresh the collection data
            if (id) {
              console.log('Refreshing collection data after updating collection field display preferences');
              fetchCollectionDetails(id);

              // Show a toast to confirm the refresh
              toast({
                title: 'Collection Fields Updated',
                description: 'Field display preferences have been applied',
                variant: 'default',
              });
            }
          }}
        />
      )}

      {/* Component Selection Dialog */}
      <ComponentSelectionDialog
        open={componentSelectionOpen}
        onClose={() => setComponentSelectionOpen(false)}
        onSelect={handleComponentSelect}
      />

      {/* Component Edit Dialog */}
      <CollectionComponentEditDialog
        open={componentEditOpen}
        onClose={() => {
          setComponentEditOpen(false);
          setComponentToEdit(null);
        }}
        onSave={handleComponentEditSave}
        collectionComponent={componentToEdit}
        isEditing={true}
      />

      {/* Edit Collection Name Dialog */}
      <Dialog open={editNameDialogOpen} onOpenChange={setEditNameDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Collection Name</DialogTitle>
            <DialogDescription>
              Update the name of your collection type.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="collection-name">Collection Name</Label>
              <Input
                id="collection-name"
                value={editingName}
                onChange={(e) => setEditingName(e.target.value)}
                placeholder="Enter collection name"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleSaveCollectionName();
                  }
                }}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditNameDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveCollectionName}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
