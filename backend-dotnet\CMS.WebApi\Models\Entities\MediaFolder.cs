using System.ComponentModel.DataAnnotations;

namespace CMS.WebApi.Models.Entities;

public class MediaFolder : BaseEntity
{
    public int Id { get; set; }

    [Required(ErrorMessage = "Folder name is required")]
    [StringLength(100)]
    public string FolderName { get; set; } = string.Empty;

    public string? Description { get; set; }

    public int? ParentId { get; set; }
    public MediaFolder? Parent { get; set; }

    public long? UserId { get; set; }
    public User? User { get; set; }

    public ICollection<MediaFolder> Children { get; set; } = new List<MediaFolder>();
    public ICollection<Media> MediaFiles { get; set; } = new List<Media>();
    public ICollection<Media> Media { get; set; } = new List<Media>();
}
