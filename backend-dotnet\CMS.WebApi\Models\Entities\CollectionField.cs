using System.ComponentModel.DataAnnotations;

namespace CMS.WebApi.Models.Entities;

public class CollectionField : BaseEntity
{
    public int Id { get; set; }

    [Required]
    public int CollectionId { get; set; }
    public CollectionListing Collection { get; set; } = null!;

    [Required]
    public int FieldTypeId { get; set; }
    public FieldType FieldType { get; set; } = null!;

    public int? DisplayPreference { get; set; }

    public int? DependentOnId { get; set; }
    public CollectionField? DependentOn { get; set; }

    public string? AdditionalInformation { get; set; }

    public ICollection<CollectionFieldConfig> Configs { get; set; } = new List<CollectionFieldConfig>();
}
