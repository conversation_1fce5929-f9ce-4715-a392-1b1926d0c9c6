using System.ComponentModel.DataAnnotations;

namespace CMS.WebApi.Models.Requests;

public class CollectionFieldCreateRequest
{
    /// <summary>
    /// Collection ID
    /// </summary>
    [Required]
    public int CollectionId { get; set; }

    /// <summary>
    /// Field type ID
    /// </summary>
    [Required]
    public int FieldTypeId { get; set; }

    /// <summary>
    /// Display preference for ordering
    /// </summary>
    public int? DisplayPreference { get; set; }

    /// <summary>
    /// ID of the field this depends on
    /// </summary>
    public int? DependentOnId { get; set; }

    /// <summary>
    /// Additional information about the field
    /// </summary>
    public string? AdditionalInformation { get; set; }

    /// <summary>
    /// Collection reference (alternative to CollectionId)
    /// </summary>
    public CollectionReference? Collection { get; set; }

    /// <summary>
    /// Field type reference (alternative to FieldTypeId)
    /// </summary>
    public FieldTypeReference? FieldType { get; set; }
}

public class CollectionReference
{
    public int Id { get; set; }
}

public class FieldTypeReference
{
    public int Id { get; set; }
}

public class ComponentReference
{
    public int Id { get; set; }
}
