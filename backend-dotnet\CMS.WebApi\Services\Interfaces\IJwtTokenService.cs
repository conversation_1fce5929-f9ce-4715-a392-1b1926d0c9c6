using System.Security.Claims;
using CMS.WebApi.Models.Entities;

namespace CMS.WebApi.Services.Interfaces;

public interface IJwtTokenService
{
    string GenerateToken(User user, string tenantId);
    ClaimsPrincipal? ValidateToken(string token);
    string? GetUsernameFromToken(string token);
    string? GetTenantFromToken(string token);
    bool IsTokenExpired(string token);
    DateTime GetTokenExpiration(string token);
}
