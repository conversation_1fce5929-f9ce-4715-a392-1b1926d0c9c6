using System.Net;
using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using CMS.WebApi.Models.Responses;
using CMS.WebApi.Exceptions;

namespace CMS.WebApi.Middleware;

public class ExceptionHandlingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ExceptionHandlingMiddleware> _logger;
    private readonly IWebHostEnvironment _environment;

    public ExceptionHandlingMiddleware(RequestDelegate next, ILogger<ExceptionHandlingMiddleware> logger, IWebHostEnvironment environment)
    {
        _next = next;
        _logger = logger;
        _environment = environment;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An unhandled exception occurred. Request: {Method} {Path}",
                context.Request.Method, context.Request.Path);
            await HandleExceptionAsync(context, ex);
        }
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        context.Response.ContentType = "application/json";

        var (statusCode, error, message, details, validationErrors) = GetErrorDetails(exception);

        var response = new ErrorResponse
        {
            Status = statusCode,
            Error = error,
            Message = message,
            Path = context.Request.Path,
            Timestamp = DateTime.UtcNow,
            TraceId = context.TraceIdentifier,
            Details = details,
            ValidationErrors = validationErrors
        };

        // Add detailed error information in development environment
        if (_environment.IsDevelopment())
        {
            response.StackTrace = exception.StackTrace;
        }

        context.Response.StatusCode = statusCode;

        var jsonResponse = JsonSerializer.Serialize(response, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = _environment.IsDevelopment()
        });

        await context.Response.WriteAsync(jsonResponse);
    }

    private (int statusCode, string error, string message, object? details, Dictionary<string, string[]>? validationErrors) GetErrorDetails(Exception exception)
    {
        return exception switch
        {
            // Custom business exceptions
            NotFoundException ex => (404, "Not Found", ex.Message, ex.Details, null),
            ValidationException ex => (400, "Validation Error", ex.Message, null, ex.ValidationErrors),
            UnauthorizedException ex => (401, "Unauthorized", ex.Message, null, null),
            ForbiddenException ex => (403, "Forbidden", ex.Message, null, null),
            ConflictException ex => (409, "Conflict", ex.Message, ex.Details, null),
            BusinessRuleException ex => (400, "Business Rule Violation", ex.Message, new { RuleCode = ex.RuleCode }, null),
            ServiceUnavailableException ex => (503, "Service Unavailable", ex.Message, new { ServiceName = ex.ServiceName }, null),
            TenantException ex => (400, "Tenant Error", ex.Message, new { TenantId = ex.TenantId }, null),

            // Entity Framework exceptions
            DbUpdateConcurrencyException => (409, "Conflict", "The record was modified by another user. Please refresh and try again.", null, null),
            DbUpdateException ex when ex.InnerException?.Message.Contains("duplicate key") == true =>
                (409, "Conflict", "A record with the same key already exists.", null, null),
            DbUpdateException => (400, "Database Error", "Failed to save changes to the database.", null, null),

            // Argument exceptions
            ArgumentNullException ex => (400, "Bad Request", $"Required parameter '{ex.ParamName}' is missing.", null, null),
            ArgumentException ex => (400, "Bad Request", ex.Message, null, null),

            // File operation exceptions
            FileNotFoundException => (404, "Not Found", "The requested file was not found.", null, null),
            DirectoryNotFoundException => (404, "Not Found", "The requested directory was not found.", null, null),
            UnauthorizedAccessException => (403, "Forbidden", "Access to the resource is denied.", null, null),

            // Timeout exceptions
            TimeoutException => (408, "Request Timeout", "The request timed out. Please try again.", null, null),
            TaskCanceledException => (408, "Request Timeout", "The request was cancelled due to timeout.", null, null),

            // Invalid operation
            InvalidOperationException ex => (409, "Conflict", ex.Message, null, null),

            // Default case
            _ => (500, "Internal Server Error",
                _environment.IsDevelopment() ? exception.Message : "An unexpected error occurred.",
                _environment.IsDevelopment() ? new { Type = exception.GetType().Name } : null, null)
        };
    }
}
