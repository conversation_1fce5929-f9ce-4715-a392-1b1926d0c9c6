import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { categoriesApi, clientsApi } from '@/lib/api';
import { useClickedClient, useClickedParentCategory } from '@/lib/store';

interface BasicCollectionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: any) => void;
  initialCategoryId?: string;
}

export default function BasicCollectionDialog({ isOpen, onClose, onSave, initialCategoryId }: BasicCollectionDialogProps) {
  const [name, setName] = useState('');
  const [apiIdSingular, setApiIdSingular] = useState('');
  const [apiIdPlural, setApiIdPlural] = useState('');
  const [categoryId, setCategoryId] = useState('');
  const [categories, setCategories] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingCategories, setLoadingCategories] = useState(false);

  // Get client and parent category from stores
  const { clientId } = useClickedClient();
  const { parentCategoryId } = useClickedParentCategory();

  // State for actual client and parent category names
  const [clientName, setClientName] = useState('');
  const [parentCategoryName, setParentCategoryName] = useState('');
  const [contextLoaded, setContextLoaded] = useState(false);

  // Function to generate API ID using actual context
  const generateApiId = (collectionName: string) => {
    const cleanCollectionName = collectionName ? collectionName.toLowerCase().replace(/[^a-z0-9]/g, '_') : '';
    const cleanClientName = clientName ? clientName.toLowerCase().replace(/[^a-z0-9]/g, '_') : '';
    const cleanParentCategoryName = parentCategoryName ? parentCategoryName.toLowerCase().replace(/[^a-z0-9]/g, '_') : '';

    // Build API ID based on available context
    if (cleanClientName && cleanParentCategoryName && cleanCollectionName) {
      return `${cleanClientName}_${cleanParentCategoryName}_${cleanCollectionName}`;
    } else if (cleanClientName && cleanParentCategoryName) {
      return `${cleanClientName}_${cleanParentCategoryName}_`;
    } else if (cleanClientName && cleanCollectionName) {
      return `${cleanClientName}_${cleanCollectionName}`;
    } else if (cleanParentCategoryName && cleanCollectionName) {
      return `${cleanParentCategoryName}_${cleanCollectionName}`;
    } else if (cleanClientName) {
      return `${cleanClientName}_`;
    } else if (cleanParentCategoryName) {
      return `${cleanParentCategoryName}_`;
    } else {
      return cleanCollectionName;
    }
  };

  // Update API IDs when context is loaded or name changes
  useEffect(() => {
    if (contextLoaded) {
      const apiId = generateApiId(name);
      console.log('🎯 Updating API IDs:', apiId);
      setApiIdSingular(apiId);
      setApiIdPlural(apiId + 's');
    }
  }, [contextLoaded, clientName, parentCategoryName, name]);

  // Load context when dialog opens
  useEffect(() => {
    if (isOpen) {
      console.log('🚀 Dialog opening - loading context...');
      console.log('Store values:', { clientId, parentCategoryId });

      // Reset form
      setName('');
      setContextLoaded(false);
      setApiIdSingular('');
      setApiIdPlural('');

      // Set category ID if provided
      if (initialCategoryId) {
        setCategoryId(initialCategoryId);
      } else {
        setCategoryId('');
      }

      // Load actual context from APIs
      const loadContext = async () => {
        try {
          let fetchedClientName = '';
          let fetchedParentCategoryName = '';

          // Fetch parent category first (which includes client relationship)
          if (parentCategoryId) {
            console.log('📞 Fetching parent category with ID:', parentCategoryId);
            try {
              const parentResponse = await categoriesApi.getById(parentCategoryId);
              fetchedParentCategoryName = parentResponse.data.categoryName || '';

              // Get client name from parent category's client relationship
              if (parentResponse.data.client && parentResponse.data.client.name) {
                fetchedClientName = parentResponse.data.client.name;
                console.log('✅ Client name from parent category:', fetchedClientName);
              }

              console.log('✅ Parent category fetched:', fetchedParentCategoryName);
              console.log('📋 Full parent category data:', parentResponse.data);
            } catch (error) {
              console.error('❌ Error fetching parent category:', error);
            }
          } else {
            console.log('⚠️ No parent category ID in store');
          }

          // Fallback: Fetch client directly if not found in parent category
          if (!fetchedClientName && clientId) {
            console.log('📞 Fallback: Fetching client directly with ID:', clientId);
            try {
              const clientResponse = await clientsApi.getById(clientId);
              fetchedClientName = clientResponse.data.name || '';
              console.log('✅ Client fetched directly:', fetchedClientName);
            } catch (error) {
              console.error('❌ Error fetching client directly:', error);
            }
          }

          // Update state with fetched names
          setClientName(fetchedClientName);
          setParentCategoryName(fetchedParentCategoryName);
          setContextLoaded(true);

          console.log('📋 Final context loaded:', {
            client: fetchedClientName,
            parentCategory: fetchedParentCategoryName
          });

        } catch (error) {
          console.error('❌ Error loading context:', error);
          setContextLoaded(true); // Still mark as loaded to prevent infinite loading
        }
      };

      loadContext();

      // Fetch categories from database using the API service
      setLoadingCategories(true);
      console.log('Fetching categories from database using API service...');

      // Use the categoriesApi service which handles authentication
      categoriesApi.getAll()
        .then(response => {
          console.log('Categories API response:', response);
          const data = response.data;

          if (Array.isArray(data)) {
            // Filter to show only child categories (those with parentCategory)
            const childCategories = data.filter(category =>
              category.parentCategory && category.parentCategory !== null
            );

            // Format categories for display - specifically looking for category_name field
            const formattedCategories = childCategories.map(category => ({
              id: category.id,
              categoryName: category.categoryName || category.category_name || category.name || `Category ${category.id}`
            }));

            console.log(`Found ${formattedCategories.length} child categories in category table:`, formattedCategories);

            // Log each category individually for debugging
            formattedCategories.forEach((cat, index) => {
              console.log(`Child Category ${index + 1}: ID=${cat.id}, Name=${cat.categoryName}`);
            });

            setCategories(formattedCategories);
          } else {
            console.warn('Categories data is not in expected format:', data);
            // Try to use mock data for testing
            const mockCategories = [
              { id: 1, categoryName: 'Basic' },
              { id: 2, categoryName: 'Advanced' },
              { id: 3, categoryName: 'Media' }
            ];
            console.log('Using mock categories for testing:', mockCategories);
            setCategories(mockCategories);
          }
        })
        .catch(error => {
          console.error('Error fetching categories from database:', error);
          // Use mock data for testing
          const mockCategories = [
            { id: 1, categoryName: 'Basic' },
            { id: 2, categoryName: 'Advanced' },
            { id: 3, categoryName: 'Media' }
          ];
          console.log('Using mock categories due to error:', mockCategories);
          setCategories(mockCategories);
        })
        .finally(() => {
          setLoadingCategories(false);
        });
    }
  }, [isOpen]);

  // Handle name change to auto-generate API IDs
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setName(value);
    console.log('📝 Collection name changed to:', value);
    // API IDs will be updated automatically by useEffect
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!name || !apiIdSingular || !apiIdPlural) {
      console.log('❌ Form validation failed:', { name, apiIdSingular, apiIdPlural });
      return;
    }

    setIsLoading(true);

    const data = {
      name,
      apiIdSingular,
      apiIdPlural,
      categoryId,
      draftAndPublish: false,
      isInternationally: false
    };

    console.log('🚀 Submitting collection data:', data);
    onSave(data);
  };

  if (!isOpen) return null;

  return createPortal(
    <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/50">
      <div className="bg-card rounded-lg shadow-lg w-full max-w-md p-6 relative">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
        >
          <X size={20} />
        </button>

        <h2 className="text-xl font-bold mb-6">Create Collection</h2>

        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div className="mb-4">
              <Label htmlFor="name" className="block mb-2 font-medium">Display Name</Label>
              <Input
                id="name"
                value={name}
                onChange={handleNameChange}
                placeholder="e.g. Restaurant, Article, Product..."
                className="w-full h-10 px-3 border border-input bg-background rounded-md"
                required
              />
            </div>

            <div className="mb-4">
              <Label htmlFor="category" className="block mb-2 font-medium">Category</Label>
              {loadingCategories ? (
                <div className="flex items-center gap-2 p-2 border border-input bg-background rounded-md">
                  <div className="h-4 w-4 rounded-full border-2 border-t-transparent border-primary animate-spin"></div>
                  <span className="text-sm text-muted-foreground">Loading categories from database...</span>
                </div>
              ) : (
                <>
                  <select
                    id="category"
                    value={categoryId}
                    onChange={(e) => setCategoryId(e.target.value)}
                    className="w-full border border-input bg-background text-foreground rounded-md p-2 h-10 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                  >
                    <option value="">None</option>
                    {categories.length > 0 ? (
                      categories.map((category) => (
                        <option key={category.id} value={category.id.toString()}>
                          {category.categoryName || category.category_name || category.name || `Category ${category.id}`}
                        </option>
                      ))
                    ) : (
                      <option value="" disabled>No categories found in database</option>
                    )}
                  </select>
                  <div className="mt-1 text-xs text-muted-foreground">
                    {categories.length > 0 ?
                      `${categories.length} categories found in database` :
                      "No categories found in the database"}
                  </div>
                </>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4 mb-6">
              <div>
                <Label htmlFor="apiIdSingular" className="block mb-2 font-medium">API ID (Singular)</Label>
                <Input
                  id="apiIdSingular"
                  value={apiIdSingular}
                  onChange={(e) => setApiIdSingular(e.target.value)}
                  placeholder="e.g. restaurant"
                  className="w-full h-10 px-3 border border-input bg-background rounded-md"
                  required
                />
              </div>

              <div>
                <Label htmlFor="apiIdPlural" className="block mb-2 font-medium">API ID (Plural)</Label>
                <Input
                  id="apiIdPlural"
                  value={apiIdPlural}
                  onChange={(e) => setApiIdPlural(e.target.value)}
                  placeholder="e.g. restaurants"
                  className="w-full h-10 px-3 border border-input bg-background rounded-md"
                  required
                />
              </div>
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
                className="w-24"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isLoading || !name || !apiIdSingular || !apiIdPlural}
                className="w-36 bg-indigo-400 hover:bg-indigo-500"
              >
                {isLoading ? 'Creating...' : 'Create Collection'}
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>,
    document.body
  );
}
