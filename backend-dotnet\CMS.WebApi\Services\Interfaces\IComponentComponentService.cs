using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.Requests;
using CMS.WebApi.Models.Responses;

namespace CMS.WebApi.Services.Interfaces;

public interface IComponentComponentService
{
    Task<IEnumerable<ComponentComponent>> GetAllComponentComponentsAsync();
    Task<ComponentComponent?> GetComponentComponentByIdAsync(int id);
    Task<IEnumerable<ComponentComponent>> GetComponentComponentsByParentIdAsync(int parentComponentId);
    Task<ComponentComponent> CreateComponentComponentAsync(CreateComponentComponentRequest request);
    Task<ComponentComponent> UpdateComponentComponentAsync(int id, UpdateComponentComponentRequest request);
    Task DeleteComponentComponentAsync(int id);
    Task ReorderComponentComponentsAsync(int parentComponentId, List<int> componentComponentIds);
}
