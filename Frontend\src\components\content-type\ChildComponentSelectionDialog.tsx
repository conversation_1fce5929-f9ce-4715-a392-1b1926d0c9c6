import React, { useEffect, useState } from 'react';
import { componentsApi, componentComponentsApi } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';
import { Component } from '@/lib/store';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Layers, Search } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

interface ChildComponentSelectionDialogProps {
  open: boolean;
  onClose: () => void;
  onSelect: (component: Component, isRepeatable: boolean) => void;
  parentComponentId: string;
}

export default function ChildComponentSelectionDialog({
  open,
  onClose,
  onSelect,
  parentComponentId,
}: ChildComponentSelectionDialogProps) {
  const { toast } = useToast();
  const [components, setComponents] = useState<Component[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isRepeatable, setIsRepeatable] = useState(false);

  useEffect(() => {
    if (open) {
      fetchComponents();
    }
  }, [open]);

  const fetchComponents = async () => {
    setLoading(true);
    try {
      const response = await componentsApi.getActive();
      console.log('Components data:', response.data);

      // Transform the data to match our Component interface
      const formattedComponents = response.data
        .map((component: any) => ({
          id: component.id.toString(),
          name: component.componentName || 'Unnamed Component',
          apiId: component.componentApiId || '',
          description: component.componentDesc || '',
          fields: [],
          isActive: component.isActive !== false, // Default to true if not specified
          createdAt: '',
          updatedAt: ''
        }))
        // Filter out the parent component itself to prevent direct circular references
        .filter((component: Component) => component.id !== parentComponentId);

      setComponents(formattedComponents);
    } catch (error) {
      console.error('Error fetching components:', error);
      toast({
        title: 'Error',
        description: 'Failed to load components',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Filter components based on search term
  const filteredComponents = components.filter(component =>
    component.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    component.apiId.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (component.description && component.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const [selectedComponent, setSelectedComponent] = useState<Component | null>(null);
  const [componentName, setComponentName] = useState('');
  const [componentDisplayName, setComponentDisplayName] = useState('');
  const [additionalInfo, setAdditionalInfo] = useState('');
  const [additionalInfoImage, setAdditionalInfoImage] = useState('');

  // Update component details when a component is selected
  const handleComponentSelect = (component: Component) => {
    setSelectedComponent(component);
    setComponentName(component.name);
    setComponentDisplayName(''); // Don't auto-populate display name
    setAdditionalInfo('');
    setAdditionalInfoImage('');
  };

  const handleAddComponent = () => {
    if (selectedComponent) {
      // Include the additional fields in the component data
      const componentWithDetails = {
        ...selectedComponent,
        name: componentName,
        displayName: componentDisplayName,
        additionalInfo,
        additionalInfoImage
      };
      onSelect(componentWithDetails, isRepeatable);
    }
  };

  return (
    <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>Add Child Component</DialogTitle>
          <DialogDescription>
            Select a component to add as a child to this component
          </DialogDescription>
        </DialogHeader>

        <div className="py-4 flex-1 overflow-hidden flex flex-col min-h-[400px]">
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search components..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          <div className="flex-1 overflow-y-auto pr-2 mb-4">
            {loading ? (
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="p-4 border rounded-md">
                    <Skeleton className="h-5 w-1/2 mb-2" />
                    <Skeleton className="h-4 w-3/4 mb-2" />
                    <Skeleton className="h-4 w-1/3" />
                  </div>
                ))}
              </div>
            ) : filteredComponents.length === 0 ? (
              <div className="text-center py-8">
                <Layers className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No components found</h3>
                <p className="text-sm text-muted-foreground">
                  {searchTerm ? 'Try a different search term' : 'Create components first'}
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredComponents.map((component) => (
                  <div
                    key={component.id}
                    className={`p-4 border rounded-md cursor-pointer transition-colors ${selectedComponent?.id === component.id ? 'border-primary bg-primary/10' : 'hover:border-primary hover:bg-primary/5'}`}
                    onClick={() => handleComponentSelect(component)}
                  >
                    <div className="flex items-center justify-between mb-1">
                      <h3 className="font-medium text-lg">{component.name}</h3>
                      <div className="p-1 rounded-full bg-primary/10">
                        <Layers className="h-4 w-4 text-primary" />
                      </div>
                    </div>
                    {component.description && (
                      <p className="text-sm text-muted-foreground mb-2">{component.description}</p>
                    )}
                    <div className="text-xs text-muted-foreground">
                      <span className="font-medium">API ID:</span> {component.apiId}
                    </div>
                    <div className="text-xs text-muted-foreground mt-1">
                      <span className="font-medium">{component.fields.length}</span> fields included
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {selectedComponent && (
            <div className="border-t pt-4 mb-4 space-y-4">
              <h4 className="font-medium text-sm">Collection Component Details</h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="componentName" className="text-sm font-medium">Name</Label>
                  <Input
                    id="componentName"
                    value={componentName}
                    onChange={(e) => setComponentName(e.target.value)}
                    placeholder="Component name in collection"
                    className="h-9"
                  />
                  <p className="text-xs text-muted-foreground">
                    Name for this component in the collection
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="componentDisplayName" className="text-sm font-medium">Display Name</Label>
                  <Input
                    id="componentDisplayName"
                    value={componentDisplayName}
                    onChange={(e) => setComponentDisplayName(e.target.value)}
                    placeholder="Display name for users"
                    className="h-9"
                  />
                  <p className="text-xs text-muted-foreground">
                    Display name shown to users
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="additionalInfo" className="text-sm font-medium">Additional Info (Optional)</Label>
                <Textarea
                  id="additionalInfo"
                  value={additionalInfo}
                  onChange={(e) => setAdditionalInfo(e.target.value)}
                  placeholder="Additional information about this component"
                  className="min-h-[60px]"
                />
                <p className="text-xs text-muted-foreground">
                  Extra details or instructions for this component
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="additionalInfoImage" className="text-sm font-medium">Additional Info Image (Optional)</Label>
                <Input
                  id="additionalInfoImage"
                  value={additionalInfoImage}
                  onChange={(e) => setAdditionalInfoImage(e.target.value)}
                  placeholder="Image URL or path"
                  className="h-9"
                />
                <p className="text-xs text-muted-foreground">
                  URL or path to an image for this component
                </p>
              </div>
            </div>
          )}

          <div className="border-t pt-4 mb-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="isRepeatable"
                checked={isRepeatable}
                onCheckedChange={(checked) => setIsRepeatable(checked as boolean)}
              />
              <Label htmlFor="isRepeatable">Make this component repeatable</Label>
            </div>
            <p className="text-xs text-muted-foreground mt-1 ml-6">
              Repeatable components can have multiple instances in the content
            </p>
          </div>
        </div>

        <DialogFooter className="flex justify-between border-t pt-4">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleAddComponent}
            disabled={!selectedComponent}
          >
            Add Component
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
