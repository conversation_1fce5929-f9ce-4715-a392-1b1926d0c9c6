using System.ComponentModel.DataAnnotations;

namespace CMS.WebApi.Models.Entities;

public class ComponentField : BaseEntity
{
    public int Id { get; set; }

    [Required]
    public int ComponentId { get; set; }
    public ComponentListing Component { get; set; } = null!;

    [Required]
    public int FieldTypeId { get; set; }
    public FieldType FieldType { get; set; } = null!;

    public int? DisplayPreference { get; set; }

    public int? DependentOnId { get; set; }
    public ComponentField? DependentOn { get; set; }

    public string? AdditionalInformation { get; set; }

    public ICollection<ComponentFieldConfig> Configs { get; set; } = new List<ComponentFieldConfig>();
}
