using CMS.WebApi.Models.Entities;

namespace CMS.WebApi.Services.Interfaces;

public interface IMediaFolderService
{
    Task<IEnumerable<MediaFolder>> GetAllMediaFoldersAsync();
    Task<MediaFolder?> GetMediaFolderByIdAsync(int id);
    Task<IEnumerable<MediaFolder>> GetMediaFoldersByParentAsync(int? parentId);
    Task<MediaFolder> CreateMediaFolderAsync(MediaFolder mediaFolder);
    Task<MediaFolder> UpdateMediaFolderAsync(int id, MediaFolder mediaFolder);
    Task DeleteMediaFolderAsync(int id);
    Task<bool> MediaFolderExistsAsync(string name, int? parentId);
    Task<bool> HasChildrenOrMediaAsync(int folderId);
}
