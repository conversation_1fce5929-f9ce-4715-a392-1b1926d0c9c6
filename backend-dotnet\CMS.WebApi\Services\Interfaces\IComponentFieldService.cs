using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.Requests;

namespace CMS.WebApi.Services.Interfaces;

public interface IComponentFieldService
{
    /// <summary>
    /// Get component field by ID
    /// </summary>
    /// <param name="id">Component field ID</param>
    /// <returns>Component field</returns>
    Task<ComponentField?> GetComponentFieldByIdAsync(int id);

    /// <summary>
    /// Get all component fields
    /// </summary>
    /// <returns>List of all component fields</returns>
    Task<IEnumerable<ComponentField>> GetAllComponentFieldsAsync();

    /// <summary>
    /// Get component fields by component ID
    /// </summary>
    /// <param name="componentId">Component ID</param>
    /// <returns>List of component fields for the component</returns>
    Task<IEnumerable<ComponentField>> GetComponentFieldsByComponentIdAsync(int componentId);

    /// <summary>
    /// Create a new component field
    /// </summary>
    /// <param name="componentField">Component field to create</param>
    /// <returns>Created component field</returns>
    Task<ComponentField> CreateComponentFieldAsync(ComponentField componentField);

    /// <summary>
    /// Create a new component field with configurations
    /// </summary>
    /// <param name="request">Component field creation request with configurations</param>
    /// <returns>Created component field</returns>
    Task<ComponentField> CreateComponentFieldWithConfigsAsync(CreateComponentFieldRequest request);

    /// <summary>
    /// Update an existing component field
    /// </summary>
    /// <param name="id">Component field ID</param>
    /// <param name="componentField">Updated component field data</param>
    /// <returns>Updated component field</returns>
    Task<ComponentField> UpdateComponentFieldAsync(int id, ComponentField componentField);

    /// <summary>
    /// Update an existing component field with configurations
    /// </summary>
    /// <param name="id">Component field ID</param>
    /// <param name="request">Updated component field data with configurations</param>
    /// <returns>Updated component field</returns>
    Task<ComponentField> UpdateComponentFieldWithConfigsAsync(int id, UpdateComponentFieldRequest request);

    /// <summary>
    /// Delete a component field
    /// </summary>
    /// <param name="id">Component field ID</param>
    /// <returns>Task</returns>
    Task DeleteComponentFieldAsync(int id);

    /// <summary>
    /// Get the next available ID for a component field
    /// </summary>
    /// <returns>Next available ID</returns>
    Task<int> GetNextAvailableIdAsync();

    /// <summary>
    /// Reorder component fields for a component
    /// </summary>
    /// <param name="componentId">Component ID</param>
    /// <param name="fieldIds">List of field IDs in the desired order</param>
    /// <returns>List of reordered fields</returns>
    Task<IEnumerable<ComponentField>> ReorderComponentFieldsAsync(int componentId, List<int> fieldIds);
}
