using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace CMS.WebApi.Models.Requests;

public class UpdateCollectionFieldRequest
{
    public int? Id { get; set; }

    public int CollectionId { get; set; }

    public int FieldTypeId { get; set; }

    public int? DisplayPreference { get; set; }

    public int? DependentOnId { get; set; }

    public string? AdditionalInformation { get; set; }

    // Support for Java backend format
    public CollectionReference? Collection { get; set; }
    public FieldTypeReference? FieldType { get; set; }

    // Support both "configs" and "configurations" property names
    [JsonPropertyName("configs")]
    public List<CollectionFieldConfigRequest> Configs { get; set; } = new();

    [JsonPropertyName("configurations")]
    public List<CollectionFieldConfigRequest> Configurations
    {
        get => Configs;
        set => Configs = value ?? new();
    }
}
