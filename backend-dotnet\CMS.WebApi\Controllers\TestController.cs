using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;

namespace CMS.WebApi.Controllers;

[ApiController]
[Route("api/[controller]")]
[Tags("Test")]
public class TestController : ControllerBase
{
    /// <summary>
    /// Public test endpoint
    /// </summary>
    /// <returns>Public message</returns>
    [HttpGet("public")]
    [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
    public ActionResult<string> GetPublic()
    {
        return Ok("This is a public endpoint");
    }

    /// <summary>
    /// Private test endpoint (requires authentication)
    /// </summary>
    /// <returns>Private message</returns>
    [HttpGet("private")]
    [Authorize]
    [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public ActionResult<string> GetPrivate()
    {
        return Ok("This is a private endpoint that requires authentication");
    }
}
