using System.Diagnostics;
using System.Text;
using CMS.WebApi.Services.Interfaces;

namespace CMS.WebApi.Middleware;

public class RequestLoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<RequestLoggingMiddleware> _logger;

    public RequestLoggingMiddleware(RequestDelegate next, ILogger<RequestLoggingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        var requestId = Guid.NewGuid().ToString();
        
        // Add request ID to response headers
        context.Response.Headers.Append("X-Request-ID", requestId);
        
        // Log request
        await LogRequestAsync(context, requestId);
        
        // Capture response
        var originalBodyStream = context.Response.Body;
        using var responseBody = new MemoryStream();
        context.Response.Body = responseBody;
        
        try
        {
            await _next(context);
        }
        finally
        {
            stopwatch.Stop();

            // Log response
            await LogResponseAsync(context, requestId, stopwatch.ElapsedMilliseconds);

            // Copy response back to original stream only if there's content
            // 204 No Content responses should not have a body
            if (context.Response.StatusCode != 204 && responseBody.Length > 0)
            {
                responseBody.Seek(0, SeekOrigin.Begin);
                await responseBody.CopyToAsync(originalBodyStream);
            }
        }
    }

    private async Task LogRequestAsync(HttpContext context, string requestId)
    {
        var request = context.Request;

        // No tenant context needed in simplified system
        var tenantId = "default";

        var requestInfo = new
        {
            RequestId = requestId,
            Method = request.Method,
            Path = request.Path.Value,
            QueryString = request.QueryString.Value,
            Headers = GetSafeHeaders(request.Headers),
            TenantId = tenantId,
            UserAgent = request.Headers["User-Agent"].FirstOrDefault(),
            RemoteIpAddress = context.Connection.RemoteIpAddress?.ToString(),
            ContentType = request.ContentType,
            ContentLength = request.ContentLength
        };

        _logger.LogInformation("HTTP Request: {RequestInfo}", requestInfo);

        // Log request body for POST/PUT requests (excluding sensitive endpoints)
        if (ShouldLogRequestBody(request))
        {
            var body = await ReadRequestBodyAsync(request);
            if (!string.IsNullOrEmpty(body))
            {
                _logger.LogDebug("Request Body for {RequestId}: {Body}", requestId, body);
            }
        }
    }

    private async Task LogResponseAsync(HttpContext context, string requestId, long elapsedMilliseconds)
    {
        var response = context.Response;

        // No tenant context needed in simplified system
        var tenantId = "default";

        var responseInfo = new
        {
            RequestId = requestId,
            StatusCode = response.StatusCode,
            ContentType = response.ContentType,
            ContentLength = response.ContentLength,
            ElapsedMilliseconds = elapsedMilliseconds,
            TenantId = tenantId
        };

        var logLevel = response.StatusCode >= 400 ? LogLevel.Warning : LogLevel.Information;
        _logger.Log(logLevel, "HTTP Response: {ResponseInfo}", responseInfo);

        // Log response body for errors in development
        if (response.StatusCode >= 400 && ShouldLogResponseBody())
        {
            var body = await ReadResponseBodyAsync(context.Response);
            if (!string.IsNullOrEmpty(body))
            {
                _logger.LogDebug("Response Body for {RequestId}: {Body}", requestId, body);
            }
        }
    }

    private static Dictionary<string, string> GetSafeHeaders(IHeaderDictionary headers)
    {
        var safeHeaders = new Dictionary<string, string>();
        var sensitiveHeaders = new[] { "authorization", "x-api-key", "cookie", "set-cookie" };

        foreach (var header in headers)
        {
            var key = header.Key.ToLowerInvariant();
            if (sensitiveHeaders.Contains(key))
            {
                safeHeaders[header.Key] = "[REDACTED]";
            }
            else
            {
                safeHeaders[header.Key] = string.Join(", ", header.Value.ToArray() ?? Array.Empty<string>());
            }
        }

        return safeHeaders;
    }

    private static bool ShouldLogRequestBody(HttpRequest request)
    {
        if (request.Method != "POST" && request.Method != "PUT" && request.Method != "PATCH")
            return false;

        var sensitiveEndpoints = new[] { "/api/auth/login", "/api/auth/register", "/api/users/change-password" };
        return !sensitiveEndpoints.Any(endpoint => request.Path.StartsWithSegments(endpoint));
    }

    private static bool ShouldLogResponseBody()
    {
        return Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development";
    }

    private static async Task<string> ReadRequestBodyAsync(HttpRequest request)
    {
        request.EnableBuffering();
        var buffer = new byte[Convert.ToInt32(request.ContentLength ?? 0)];
        await request.Body.ReadAsync(buffer, 0, buffer.Length);
        request.Body.Position = 0;
        return Encoding.UTF8.GetString(buffer);
    }

    private static async Task<string> ReadResponseBodyAsync(HttpResponse response)
    {
        response.Body.Seek(0, SeekOrigin.Begin);
        var text = await new StreamReader(response.Body).ReadToEndAsync();
        response.Body.Seek(0, SeekOrigin.Begin);
        return text;
    }
}
