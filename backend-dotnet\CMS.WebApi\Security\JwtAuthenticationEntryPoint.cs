using Microsoft.AspNetCore.Authentication;
using System.Text.Json;
using CMS.WebApi.Models.Responses;

namespace CMS.WebApi.Security;

public class JwtAuthenticationEntryPoint : IAuthenticationHandler
{
    private readonly ILogger<JwtAuthenticationEntryPoint> _logger;
    private AuthenticationScheme? _scheme;
    private HttpContext? _context;

    public JwtAuthenticationEntryPoint(ILogger<JwtAuthenticationEntryPoint> logger)
    {
        _logger = logger;
    }

    public Task InitializeAsync(AuthenticationScheme scheme, HttpContext context)
    {
        _scheme = scheme;
        _context = context;
        return Task.CompletedTask;
    }

    public Task<AuthenticateResult> AuthenticateAsync()
    {
        return Task.FromResult(AuthenticateResult.NoResult());
    }

    public async Task ChallengeAsync(AuthenticationProperties? properties)
    {
        if (_context == null) return;

        _context.Response.ContentType = "application/json";
        _context.Response.StatusCode = StatusCodes.Status401Unauthorized;

        var response = new ErrorResponse
        {
            Status = StatusCodes.Status401Unauthorized,
            Error = "Unauthorized",
            Message = "Authentication failed: Invalid or missing token",
            Path = _context.Request.Path,
            Timestamp = DateTime.UtcNow,
            Help = "Use /api/auth/login endpoint to get a token, then include it in the Authorization header as 'Bearer YOUR_TOKEN'"
        };

        var jsonResponse = JsonSerializer.Serialize(response, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        await _context.Response.WriteAsync(jsonResponse);
    }

    public Task ForbidAsync(AuthenticationProperties? properties)
    {
        if (_context == null) return Task.CompletedTask;

        _context.Response.StatusCode = StatusCodes.Status403Forbidden;
        return Task.CompletedTask;
    }
}

public static class JwtAuthenticationEntryPointExtensions
{
    public static IServiceCollection AddJwtAuthenticationEntryPoint(this IServiceCollection services)
    {
        services.AddSingleton<JwtAuthenticationEntryPoint>();
        return services;
    }

    public static void UseJwtAuthenticationEntryPoint(this IApplicationBuilder app)
    {
        app.Use(async (context, next) =>
        {
            try
            {
                await next();
            }
            catch (Exception)
            {
                if (context.Response.StatusCode == StatusCodes.Status401Unauthorized)
                {
                    var entryPoint = context.RequestServices.GetRequiredService<JwtAuthenticationEntryPoint>();
                    await entryPoint.ChallengeAsync(null);
                }
                else
                {
                    throw;
                }
            }
        });
    }
}
