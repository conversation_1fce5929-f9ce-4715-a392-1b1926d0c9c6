using Microsoft.EntityFrameworkCore;
using CMS.WebApi.Data;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Services.Interfaces;

namespace CMS.WebApi.Services.Implementations;

public class CollectionOrderingService : ICollectionOrderingService
{
    private readonly CmsDbContext _context;
    private readonly ILogger<CollectionOrderingService> _logger;

    public CollectionOrderingService(CmsDbContext context, ILogger<CollectionOrderingService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<int> GetMaxDisplayPreferenceAsync(int collectionId)
    {
        // Get max display preference from components
        var maxComponentPreference = await _context.CollectionComponents
            .Where(cc => cc.CollectionId == collectionId)
            .MaxAsync(cc => (int?)cc.DisplayPreference);

        // Get max display preference from fields
        var maxFieldPreference = await _context.CollectionFields
            .Where(cf => cf.CollectionId == collectionId)
            .MaxAsync(cf => (int?)cf.DisplayPreference);

        // Return the maximum of the two, or 0 if both are null
        if (maxComponentPreference == null && maxFieldPreference == null)
        {
            return 0;
        }
        else if (maxComponentPreference == null)
        {
            return maxFieldPreference ?? 0;
        }
        else if (maxFieldPreference == null)
        {
            return maxComponentPreference ?? 0;
        }
        else
        {
            return Math.Max(maxComponentPreference ?? 0, maxFieldPreference ?? 0);
        }
    }

    public async Task<int> GetNextDisplayPreferenceAsync(int collectionId)
    {
        // Get the current max display preference and add 10
        var maxPreference = await GetMaxDisplayPreferenceAsync(collectionId);
        return maxPreference + 10;
    }

    public async Task<object> GetOrderedCollectionItemsAsync(int collectionId)
    {
        // Verify collection exists
        var collectionExists = await _context.CollectionListings
            .AnyAsync(c => c.Id == collectionId);

        if (!collectionExists)
        {
            throw new ArgumentException($"Collection not found with id: {collectionId}");
        }

        // Get components and fields
        var components = await GetOrderedComponentsAsync(collectionId);
        var fields = await GetOrderedFieldsAsync(collectionId);

        // Create result object
        return new
        {
            components = components,
            fields = fields
        };
    }

    public async Task<IEnumerable<CollectionComponent>> GetOrderedComponentsAsync(int collectionId)
    {
        // Get collection
        var collection = await _context.CollectionListings
            .FirstOrDefaultAsync(c => c.Id == collectionId);

        if (collection == null)
        {
            throw new ArgumentException($"Collection not found with id: {collectionId}");
        }

        // Get components and sort by display preference
        var components = await _context.CollectionComponents
            .Include(cc => cc.Component)
            .Where(cc => cc.CollectionId == collectionId && cc.IsActive == true)
            .OrderBy(cc => cc.DisplayPreference ?? int.MaxValue)
            .ToListAsync();

        // Normalize display preferences to 10, 20, 30 format
        for (int i = 0; i < components.Count; i++)
        {
            components[i].DisplayPreference = (i + 1) * 10;
        }

        return components;
    }

    public async Task<IEnumerable<CollectionField>> GetOrderedFieldsAsync(int collectionId)
    {
        // Get fields and sort by display preference
        var fields = await _context.CollectionFields
            .Include(cf => cf.FieldType)
            .Where(cf => cf.CollectionId == collectionId)
            .OrderBy(cf => cf.DisplayPreference ?? int.MaxValue)
            .ToListAsync();

        // Normalize display preferences to 10, 20, 30 format
        for (int i = 0; i < fields.Count; i++)
        {
            fields[i].DisplayPreference = (i + 1) * 10;
        }

        return fields;
    }

    public async Task<object> ReorderCollectionItemsAsync(int collectionId, List<int> componentIds, List<int> fieldIds)
    {
        // Verify collection exists
        var collectionExists = await _context.CollectionListings
            .AnyAsync(c => c.Id == collectionId);

        if (!collectionExists)
        {
            throw new ArgumentException($"Collection not found with id: {collectionId}");
        }

        _logger.LogInformation("Reordering collection items for collection {CollectionId}: {ComponentCount} components and {FieldCount} fields",
            collectionId, componentIds.Count, fieldIds.Count);

        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            // Get all components for this collection
            var components = await _context.CollectionComponents
                .Where(cc => cc.CollectionId == collectionId && cc.IsActive == true)
                .ToListAsync();

            var fields = await _context.CollectionFields
                .Where(cf => cf.CollectionId == collectionId)
                .ToListAsync();

            // Create maps for quick lookup
            var componentMap = components.ToDictionary(c => c.Id, c => c);
            var fieldMap = fields.ToDictionary(f => f.Id, f => f);

            // Update display preferences for components
            var updatedComponents = new List<CollectionComponent>();
            for (int i = 0; i < componentIds.Count; i++)
            {
                var componentId = componentIds[i];
                if (componentMap.TryGetValue(componentId, out var component))
                {
                    // Set display preference based on position (multiply by 10 to leave room for insertion)
                    var displayPreference = (i + 1) * 10;
                    _logger.LogInformation("Setting component {ComponentId} display preference to {DisplayPreference}",
                        componentId, displayPreference);
                    component.DisplayPreference = displayPreference;
                    updatedComponents.Add(component);
                }
                else
                {
                    _logger.LogWarning("Component with ID {ComponentId} not found for collection {CollectionId}",
                        componentId, collectionId);
                }
            }

            // Update display preferences for fields
            var updatedFields = new List<CollectionField>();
            for (int i = 0; i < fieldIds.Count; i++)
            {
                var fieldId = fieldIds[i];
                if (fieldMap.TryGetValue(fieldId, out var field))
                {
                    // Set display preference based on position (multiply by 10 to leave room for insertion)
                    // Start after the components to maintain proper ordering
                    var displayPreference = (componentIds.Count + i + 1) * 10;
                    _logger.LogInformation("Setting field {FieldId} display preference to {DisplayPreference}",
                        fieldId, displayPreference);
                    field.DisplayPreference = displayPreference;
                    updatedFields.Add(field);
                }
                else
                {
                    _logger.LogWarning("Field with ID {FieldId} not found for collection {CollectionId}",
                        fieldId, collectionId);
                }
            }

            // Save changes
            await _context.SaveChangesAsync();
            await transaction.CommitAsync();

            _logger.LogInformation("Successfully reordered {ComponentCount} components and {FieldCount} fields for collection {CollectionId}",
                updatedComponents.Count, updatedFields.Count, collectionId);

            // Return updated items
            return new
            {
                components = updatedComponents,
                fields = updatedFields
            };
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, "Failed to reorder collection items for collection {CollectionId}", collectionId);
            throw;
        }
    }
}
