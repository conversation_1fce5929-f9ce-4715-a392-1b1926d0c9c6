namespace CMS.WebApi.Models.DTOs;

public class SimplifiedCollectionDto
{
    public int Id { get; set; }
    public string CollectionName { get; set; } = string.Empty;
    public string? CollectionDesc { get; set; }
    public string CollectionApiId { get; set; } = string.Empty;
    public List<SimplifiedComponentDto> Components { get; set; } = new List<SimplifiedComponentDto>();
    public List<SimplifiedFieldDto> Fields { get; set; } = new List<SimplifiedFieldDto>();
}
