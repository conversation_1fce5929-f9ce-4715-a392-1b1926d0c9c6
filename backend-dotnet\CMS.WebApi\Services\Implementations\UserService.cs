using CMS.WebApi.Data;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.Requests;
using CMS.WebApi.Models.Responses;
using CMS.WebApi.Services.Interfaces;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace CMS.WebApi.Services.Implementations;

public class UserService : IUserService
{
    private readonly CmsDbContext _context;
    private readonly UserManager<User> _userManager;
    private readonly ILogger<UserService> _logger;

    public UserService(CmsDbContext context, UserManager<User> userManager, ILogger<UserService> logger)
    {
        _context = context;
        _userManager = userManager;
        _logger = logger;
    }

    public async Task<User?> GetUserByIdAsync(long id)
    {
        return await _userManager.FindByIdAsync(id.ToString());
    }

    public async Task<User?> GetUserByUsernameAsync(string username)
    {
        return await _userManager.FindByNameAsync(username);
    }

    public async Task<User?> GetUserByEmailAsync(string email)
    {
        return await _userManager.FindByEmailAsync(email);
    }

    public async Task<IEnumerable<User>> GetAllUsersAsync()
    {
        return await _userManager.Users.ToListAsync();
    }

    public async Task<User> CreateUserAsync(User user, string password)
    {
        var result = await _userManager.CreateAsync(user, password);
        if (!result.Succeeded)
        {
            var errors = string.Join(", ", result.Errors.Select(e => e.Description));
            throw new InvalidOperationException($"Failed to create user: {errors}");
        }
        return user;
    }

    public async Task<User> UpdateUserAsync(long id, User user)
    {
        var existingUser = await _userManager.FindByIdAsync(id.ToString());
        if (existingUser == null)
            throw new ArgumentException($"User with ID {id} not found");

        existingUser.UserName = user.UserName;
        existingUser.Email = user.Email;
        existingUser.IsActive = user.IsActive;

        var result = await _userManager.UpdateAsync(existingUser);
        if (!result.Succeeded)
        {
            var errors = string.Join(", ", result.Errors.Select(e => e.Description));
            throw new InvalidOperationException($"Failed to update user: {errors}");
        }

        return existingUser;
    }

    public async Task DeleteUserAsync(long id)
    {
        var user = await _userManager.FindByIdAsync(id.ToString());
        if (user != null)
        {
            await _userManager.DeleteAsync(user);
        }
    }

    public async Task<bool> ValidatePasswordAsync(User user, string password)
    {
        return await _userManager.CheckPasswordAsync(user, password);
    }

    public async Task<bool> UserExistsAsync(string username)
    {
        var user = await _userManager.FindByNameAsync(username);
        return user != null;
    }

    public async Task<bool> EmailExistsAsync(string email)
    {
        var user = await _userManager.FindByEmailAsync(email);
        return user != null;
    }

    public async Task AddUserToTenantAsync(string username, string tenantSchema)
    {
        // TODO: Implement tenant-specific user management
        _logger.LogInformation("Adding user {Username} to tenant {TenantSchema}", username, tenantSchema);
        await Task.CompletedTask;
    }

    public async Task<IEnumerable<User>> GetUsersInTenantAsync(string tenantSchema)
    {
        // TODO: Implement tenant-specific user retrieval
        _logger.LogInformation("Getting users in tenant {TenantSchema}", tenantSchema);
        return await _userManager.Users.ToListAsync();
    }
}
