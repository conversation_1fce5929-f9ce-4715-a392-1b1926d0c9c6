using CMS.WebApi.Models.Entities;

namespace CMS.WebApi.Services.Interfaces;

public interface IComponentService
{
    Task<IEnumerable<ComponentListing>> GetAllComponentsAsync();
    Task<IEnumerable<ComponentListing>> GetActiveComponentsAsync();
    Task<ComponentListing?> GetComponentByIdAsync(int id);
    Task<ComponentListing?> GetComponentByNameAsync(string name);
    Task<ComponentListing?> GetComponentByApiIdAsync(string apiId);
    Task<ComponentListing> CreateComponentAsync(ComponentListing component);
    Task<ComponentListing> UpdateComponentAsync(int id, ComponentListing component);
    Task DeleteComponentAsync(int id);
    Task<bool> ComponentExistsAsync(string name);
    Task<IEnumerable<ComponentField>> GetComponentFieldsAsync(int componentId);
    Task<ComponentField> AddFieldToComponentAsync(int componentId, ComponentField field);
    Task RemoveFieldFromComponentAsync(int componentId, int fieldId);
}
