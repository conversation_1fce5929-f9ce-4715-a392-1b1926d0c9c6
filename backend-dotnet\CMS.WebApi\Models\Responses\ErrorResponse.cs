namespace CMS.WebApi.Models.Responses;

public class ErrorResponse
{
    public int Status { get; set; }
    public string Error { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string Path { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string? Help { get; set; }
    public string? TraceId { get; set; }
    public object? Details { get; set; }
    public string? StackTrace { get; set; }
    public Dictionary<string, string[]>? ValidationErrors { get; set; }
}
