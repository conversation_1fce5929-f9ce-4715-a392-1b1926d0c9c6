using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.Requests;

namespace CMS.WebApi.Services.Interfaces;

public interface ICollectionFieldService
{
    /// <summary>
    /// Get collection field by ID
    /// </summary>
    /// <param name="id">Collection field ID</param>
    /// <returns>Collection field</returns>
    Task<CollectionField?> GetCollectionFieldByIdAsync(int id);

    /// <summary>
    /// Get all collection fields
    /// </summary>
    /// <returns>List of all collection fields</returns>
    Task<IEnumerable<CollectionField>> GetAllCollectionFieldsAsync();

    /// <summary>
    /// Get collection fields by collection ID
    /// </summary>
    /// <param name="collectionId">Collection ID</param>
    /// <returns>List of collection fields for the collection</returns>
    Task<IEnumerable<CollectionField>> GetCollectionFieldsByCollectionIdAsync(int collectionId);

    /// <summary>
    /// Create a new collection field
    /// </summary>
    /// <param name="collectionField">Collection field to create</param>
    /// <returns>Created collection field</returns>
    Task<CollectionField> CreateCollectionFieldAsync(CollectionField collectionField);

    /// <summary>
    /// Create a new collection field with configurations
    /// </summary>
    /// <param name="request">Collection field creation request with configurations</param>
    /// <returns>Created collection field with configurations</returns>
    Task<CollectionField> CreateCollectionFieldWithConfigsAsync(CreateCollectionFieldWithConfigsRequest request);

    /// <summary>
    /// Update an existing collection field
    /// </summary>
    /// <param name="id">Collection field ID</param>
    /// <param name="collectionField">Updated collection field data</param>
    /// <returns>Updated collection field</returns>
    Task<CollectionField> UpdateCollectionFieldAsync(int id, CollectionField collectionField);

    /// <summary>
    /// Update an existing collection field with configurations
    /// </summary>
    /// <param name="id">Collection field ID</param>
    /// <param name="request">Updated collection field data with configurations</param>
    /// <returns>Updated collection field</returns>
    Task<CollectionField> UpdateCollectionFieldWithConfigsAsync(int id, UpdateCollectionFieldRequest request);

    /// <summary>
    /// Update display preference for a collection field
    /// </summary>
    /// <param name="id">Collection field ID</param>
    /// <param name="displayPreference">New display preference</param>
    /// <returns>Updated collection field</returns>
    Task<CollectionField> UpdateDisplayPreferenceAsync(int id, int displayPreference);

    /// <summary>
    /// Delete a collection field
    /// </summary>
    /// <param name="id">Collection field ID</param>
    /// <returns>Task</returns>
    Task DeleteCollectionFieldAsync(int id);

    /// <summary>
    /// Get the next available ID for a collection field
    /// </summary>
    /// <returns>Next available ID</returns>
    Task<int> GetNextAvailableIdAsync();
}
