using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;
using System.Security.Claims;
using System.Text.Encodings.Web;
using CMS.WebApi.Services.Interfaces;

namespace CMS.WebApi.Security;

public class ApiTokenAuthenticationOptions : AuthenticationSchemeOptions
{
    public const string DefaultScheme = "ApiToken";
    public string Scheme => DefaultScheme;
}

public class ApiTokenAuthenticationHandler : AuthenticationHandler<ApiTokenAuthenticationOptions>
{
    public ApiTokenAuthenticationHandler(
        IOptionsMonitor<ApiTokenAuthenticationOptions> options,
        ILoggerFactory logger,
        UrlEncoder encoder)
        : base(options, logger, encoder)
    {
    }

    protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        try
        {
            // Resolve scoped services from the request scope
            var apiTokenService = Context.RequestServices.GetRequiredService<IApiTokenService>();
            var userService = Context.RequestServices.GetRequiredService<IUserService>();

            // Check if already authenticated by JWT
            if (Context.User.Identity?.IsAuthenticated == true)
            {
                return AuthenticateResult.NoResult();
            }

            // Get API token from X-API-Key header
            var apiToken = Request.Headers["X-API-Key"].FirstOrDefault();
            if (string.IsNullOrEmpty(apiToken))
            {
                return AuthenticateResult.NoResult();
            }

            // Find the token in database
            var token = await apiTokenService.GetApiTokenByValueAsync(apiToken);
            if (token == null)
            {
                Logger.LogDebug("API token not found: {Token}", apiToken);
                return AuthenticateResult.Fail("Invalid API token");
            }

            // Check if token is active and not expired
            if (!token.IsActive || token.ExpiresAt < DateTime.UtcNow)
            {
                Logger.LogDebug("API token is expired or inactive");
                return AuthenticateResult.Fail("API token is expired or inactive");
            }

            // Get the user associated with the token
            var user = await userService.GetUserByIdAsync(token.UserId);
            if (user == null)
            {
                Logger.LogWarning("User not found for API token: {UserId}", token.UserId);
                return AuthenticateResult.Fail("User not found");
            }

            // Check if user is active
            if (!user.IsActive)
            {
                Logger.LogWarning("User is inactive: {Username}", user.UserName);
                return AuthenticateResult.Fail("User is inactive");
            }

            // Create claims for the user
            var claims = new[]
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(ClaimTypes.Name, user.UserName!),
                new Claim(ClaimTypes.Email, user.Email!),
                new Claim("auth_method", "api_token"),
                new Claim("token_id", token.Id.ToString())
            };

            var identity = new ClaimsIdentity(claims, ApiTokenAuthenticationOptions.DefaultScheme);
            var principal = new ClaimsPrincipal(identity);

            // Update last used timestamp
            await apiTokenService.UpdateLastUsedAsync(token.Id);

            Logger.LogDebug("API token authentication successful for user: {Username}", user.UserName);

            var ticket = new AuthenticationTicket(principal, ApiTokenAuthenticationOptions.DefaultScheme);
            return AuthenticateResult.Success(ticket);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "API token authentication failed");
            return AuthenticateResult.Fail("Authentication failed");
        }
    }
}
