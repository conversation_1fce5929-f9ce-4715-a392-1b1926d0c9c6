namespace CMS.WebApi.Exceptions;

/// <summary>
/// Base exception class for all custom application exceptions
/// </summary>
public abstract class ApplicationException : Exception
{
    protected ApplicationException(string message) : base(message) { }
    protected ApplicationException(string message, Exception innerException) : base(message, innerException) { }
}

/// <summary>
/// Exception thrown when a requested resource is not found
/// </summary>
public class NotFoundException : ApplicationException
{
    public object? Details { get; }

    public NotFoundException(string message) : base(message) { }
    
    public NotFoundException(string message, object? details) : base(message)
    {
        Details = details;
    }
}

/// <summary>
/// Exception thrown when validation fails
/// </summary>
public class ValidationException : ApplicationException
{
    public Dictionary<string, string[]>? ValidationErrors { get; }

    public ValidationException(string message) : base(message) { }
    
    public ValidationException(string message, Dictionary<string, string[]> validationErrors) : base(message)
    {
        ValidationErrors = validationErrors;
    }
}

/// <summary>
/// Exception thrown when authentication fails
/// </summary>
public class UnauthorizedException : ApplicationException
{
    public UnauthorizedException(string message) : base(message) { }
}

/// <summary>
/// Exception thrown when authorization fails
/// </summary>
public class ForbiddenException : ApplicationException
{
    public ForbiddenException(string message) : base(message) { }
}

/// <summary>
/// Exception thrown when there's a conflict (e.g., duplicate resource)
/// </summary>
public class ConflictException : ApplicationException
{
    public object? Details { get; }

    public ConflictException(string message) : base(message) { }
    
    public ConflictException(string message, object? details) : base(message)
    {
        Details = details;
    }
}

/// <summary>
/// Exception thrown when a business rule is violated
/// </summary>
public class BusinessRuleException : ApplicationException
{
    public string RuleCode { get; }
    
    public BusinessRuleException(string ruleCode, string message) : base(message)
    {
        RuleCode = ruleCode;
    }
}

/// <summary>
/// Exception thrown when external service is unavailable
/// </summary>
public class ServiceUnavailableException : ApplicationException
{
    public string ServiceName { get; }
    
    public ServiceUnavailableException(string serviceName, string message) : base(message)
    {
        ServiceName = serviceName;
    }
    
    public ServiceUnavailableException(string serviceName, string message, Exception innerException) 
        : base(message, innerException)
    {
        ServiceName = serviceName;
    }
}

/// <summary>
/// Exception thrown when tenant context is invalid or missing
/// </summary>
public class TenantException : ApplicationException
{
    public string? TenantId { get; }
    
    public TenantException(string message) : base(message) { }
    
    public TenantException(string message, string? tenantId) : base(message)
    {
        TenantId = tenantId;
    }
}
