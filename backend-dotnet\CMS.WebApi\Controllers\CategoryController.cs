using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.Responses;
using CMS.WebApi.Services.Interfaces;
using CMS.WebApi.Security;

namespace CMS.WebApi.Controllers;

[ApiController]
[Route("api/categories")]
[Authorize(Policy = AuthorizationPolicies.JwtOrApiToken)]
[Tags("Category Management")]
public class CategoryController : ControllerBase
{
    private readonly ICategoryService _categoryService;
    private readonly ILogger<CategoryController> _logger;

    public CategoryController(ICategoryService categoryService, ILogger<CategoryController> logger)
    {
        _categoryService = categoryService;
        _logger = logger;
    }

    /// <summary>
    /// Get all categories
    /// </summary>
    /// <returns>List of categories</returns>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<Category>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status204NoContent)]
    public async Task<ActionResult<IEnumerable<Category>>> GetAllCategories()
    {
        var categories = await _categoryService.GetAllCategoriesAsync();
        if (!categories.Any())
        {
            return NoContent();
        }
        return Ok(categories);
    }

    /// <summary>
    /// Get category by ID
    /// </summary>
    /// <param name="id">Category ID</param>
    /// <returns>Category details</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(Category), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<Category>> GetCategoryById(int id)
    {
        var category = await _categoryService.GetCategoryByIdAsync(id);
        if (category == null)
        {
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = $"Category not found with id: {id}",
                Path = Request.Path
            });
        }
        return Ok(category);
    }

    /// <summary>
    /// Get categories by client ID
    /// </summary>
    /// <param name="clientId">Client ID</param>
    /// <returns>List of categories for the client</returns>
    [HttpGet("client/{clientId}")]
    [ProducesResponseType(typeof(IEnumerable<Category>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status204NoContent)]
    public async Task<ActionResult<IEnumerable<Category>>> GetCategoriesByClientId(int clientId)
    {
        var categories = await _categoryService.GetCategoriesByClientIdAsync(clientId);
        if (!categories.Any())
        {
            return NoContent();
        }
        return Ok(categories);
    }

    /// <summary>
    /// Get categories by parent category ID
    /// </summary>
    /// <param name="parentId">Parent category ID</param>
    /// <returns>List of categories with the specified parent</returns>
    [HttpGet("parent/{parentId}")]
    [ProducesResponseType(typeof(IEnumerable<Category>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status204NoContent)]
    public async Task<ActionResult<IEnumerable<Category>>> GetCategoriesByParentId(int parentId)
    {
        var categories = await _categoryService.GetCategoriesByParentIdAsync(parentId);
        if (!categories.Any())
        {
            return NoContent();
        }
        return Ok(categories);
    }

    /// <summary>
    /// Create a new category
    /// </summary>
    /// <param name="category">Category details</param>
    /// <returns>Created category</returns>
    [HttpPost]
    [ProducesResponseType(typeof(Category), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<Category>> CreateCategory([FromBody] Category category)
    {
        try
        {
            // Check if category with same name already exists for the client
            if (category.ClientId.HasValue && 
                await _categoryService.CategoryExistsAsync(category.CategoryName, category.ClientId.Value))
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = $"Category with name '{category.CategoryName}' already exists for this client",
                    Path = Request.Path
                });
            }

            var createdCategory = await _categoryService.CreateCategoryAsync(category);
            _logger.LogInformation("Category created successfully: {CategoryName}", createdCategory.CategoryName);

            return CreatedAtAction(nameof(GetCategoryById), new { id = createdCategory.Id }, createdCategory);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create category: {CategoryName}", category.CategoryName);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while creating the category",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Update an existing category
    /// </summary>
    /// <param name="id">Category ID</param>
    /// <param name="category">Updated category details</param>
    /// <returns>Updated category</returns>
    [HttpPut("{id}")]
    [ProducesResponseType(typeof(Category), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<Category>> UpdateCategory(int id, [FromBody] Category category)
    {
        try
        {
            var updatedCategory = await _categoryService.UpdateCategoryAsync(id, category);
            _logger.LogInformation("Category updated successfully: {CategoryId}", id);
            return Ok(updatedCategory);
        }
        catch (ArgumentException ex)
        {
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = ex.Message,
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update category: {CategoryId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while updating the category",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Delete a category
    /// </summary>
    /// <param name="id">Category ID</param>
    /// <returns>No content</returns>
    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult> DeleteCategory(int id)
    {
        try
        {
            var category = await _categoryService.GetCategoryByIdAsync(id);
            if (category == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Category with ID {id} not found",
                    Path = Request.Path
                });
            }

            await _categoryService.DeleteCategoryAsync(id);
            _logger.LogInformation("Category deleted successfully: {CategoryId}", id);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete category: {CategoryId}", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while deleting the category",
                Path = Request.Path
            });
        }
    }
}
