using CMS.WebApi.Data;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace CMS.WebApi.Services.Implementations;

public class FieldTypeService : IFieldTypeService
{
    private readonly CmsDbContext _context;
    private readonly ILogger<FieldTypeService> _logger;

    public FieldTypeService(CmsDbContext context, ILogger<FieldTypeService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<IEnumerable<FieldType>> GetAllFieldTypesAsync()
    {
        return await _context.FieldTypes
            .OrderBy(ft => ft.FieldTypeName)
            .ToListAsync();
    }

    public async Task<IEnumerable<FieldType>> GetActiveFieldTypesAsync()
    {
        return await _context.FieldTypes
            .Where(ft => ft.IsActive)
            .OrderBy(ft => ft.FieldTypeName)
            .ToListAsync();
    }

    public async Task<FieldType?> GetFieldTypeByIdAsync(int id)
    {
        return await _context.FieldTypes.FindAsync(id);
    }

    public async Task<FieldType?> GetFieldTypeByNameAsync(string fieldTypeName)
    {
        return await _context.FieldTypes
            .FirstOrDefaultAsync(ft => ft.FieldTypeName == fieldTypeName);
    }

    public async Task<FieldType> CreateFieldTypeAsync(FieldType fieldType)
    {
        fieldType.CreatedAt = DateTime.UtcNow;
        fieldType.IsActive = fieldType.IsActive; // Keep the provided value or default

        _context.FieldTypes.Add(fieldType);
        await _context.SaveChangesAsync();
        return fieldType;
    }

    public async Task<FieldType> UpdateFieldTypeAsync(int id, FieldType fieldType)
    {
        var existingFieldType = await _context.FieldTypes.FindAsync(id);
        if (existingFieldType == null)
            throw new ArgumentException($"Field type with ID {id} not found");

        existingFieldType.FieldTypeName = fieldType.FieldTypeName;
        existingFieldType.FieldTypeDesc = fieldType.FieldTypeDesc;
        existingFieldType.DisplayName = fieldType.DisplayName;
        existingFieldType.HelpText = fieldType.HelpText;
        existingFieldType.LogoImagePath = fieldType.LogoImagePath;
        existingFieldType.IsActive = fieldType.IsActive;
        existingFieldType.ModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return existingFieldType;
    }

    public async Task DeleteFieldTypeAsync(int id)
    {
        var fieldType = await _context.FieldTypes.FindAsync(id);
        if (fieldType != null)
        {
            _context.FieldTypes.Remove(fieldType);
            await _context.SaveChangesAsync();
        }
    }

    public async Task<bool> FieldTypeExistsAsync(string fieldTypeName)
    {
        return await _context.FieldTypes
            .AnyAsync(ft => ft.FieldTypeName == fieldTypeName);
    }
}
