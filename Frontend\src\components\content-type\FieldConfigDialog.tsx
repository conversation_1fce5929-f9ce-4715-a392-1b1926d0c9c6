import React, { useState, useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { useToast } from '@/hooks/use-toast';
import ComponentSelector from './ComponentSelector';
import {
  Dialog,
  DialogContent,
  DialogClose
} from '@/components/ui/dialog';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Field, FieldTypeEnum } from '@/lib/store';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { ArrowLeft } from 'lucide-react';
import { FieldConfig as FieldConfigType } from '../../types';
import FieldConfigItem from './FieldConfigItem';
import { getBaseUrl } from '@/lib/api';

// Schema for field validation - simplified for basic requirements
const fieldSchema = z.object({
  name: z.string().min(2, 'Name is required and must be at least 2 characters'),
  apiId: z.string().min(2, 'API ID is required and must be at least 2 characters')
    .regex(/^[a-zA-Z0-9_]+$/, 'API ID can only contain letters, numbers, and underscores'),
  type: z.string(),
  required: z.boolean().default(false),
  unique: z.boolean().default(false),
  description: z.string().optional(),
  // Field configuration
  attributes: z.record(z.any()).optional(),
  validations: z.record(z.any()).optional(),
}).partial();

type FieldFormValues = z.infer<typeof fieldSchema>;

interface FieldConfigDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (field: Field) => void;
  field?: Field;
  fieldType: FieldTypeEnum;
  fieldTypeId?: number;
  onAddAnother?: () => void;
  collectionName?: string;
}

// Define the local field config structure
interface FieldConfig extends FieldConfigType {
  configType?: string;
}

export default function FieldConfigDialog({
  open,
  onClose,
  onSave,
  field,
  fieldType,
  fieldTypeId,
  onAddAnother,
  collectionName = "New Collection"
}: FieldConfigDialogProps) {
  const { toast } = useToast();
  // Debug props
  console.log('FieldConfigDialog props:', {
    open,
    fieldType,
    fieldTypeId,
    fieldTypeIdType: typeof fieldTypeId,
    field: field ? 'provided' : 'not provided',
    collectionName
  });

  // Debug field data
  if (field) {
    console.log('Field data in FieldConfigDialog:', {
      name: field.name,
      type: field.type,
      fieldTypeId: field.fieldTypeId,
      attributes: field.attributes
    });
  }
  // State for field configurations
  const [fieldConfigs, setFieldConfigs] = useState<FieldConfig[]>([]);
  const [configsLoading, setConfigsLoading] = useState<boolean>(false);
  const [configsError, setConfigsError] = useState<string | null>(null);

  // State to track form values for field configurations
  const [attributesState, setAttributesState] = useState<Record<string, any>>({});
  const [validationsState, setValidationsState] = useState<Record<string, any>>({});

  // State for required and unique fields
  const [isRequired, setIsRequired] = useState(field?.required || false);
  const [isUnique, setIsUnique] = useState(field?.unique || false);

  // Component selector state
  const [componentSelectorOpen, setComponentSelectorOpen] = useState(false);
  const [selectedComponent, setSelectedComponent] = useState<any>(null);

  // Field configurations will be fetched from the API

  // Fetch field configurations when component mounts or fieldTypeId changes
  useEffect(() => {
    const fetchFieldConfigs = async () => {
      console.log('FieldConfigDialog: fetchFieldConfigs called with fieldTypeId:', fieldTypeId);
      if (fieldTypeId === undefined || fieldTypeId === null) {
        console.log('FieldConfigDialog: No fieldTypeId provided, skipping API call');
        return;
      }

      setConfigsLoading(true);
      setConfigsError(null);

      try {
        // Add a timestamp to prevent caching
        const timestamp = new Date().getTime();
        // Use the getBaseUrl function to ensure consistent API URL handling
        const baseUrl = getBaseUrl();
        const url = `${baseUrl}/field-configs/getByFieldTypeId/${fieldTypeId}?t=${timestamp}`;
        console.log(`FieldConfigDialog: Making API call to ${url}`);

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Cache-Control': 'no-cache',
            'Authorization': `Bearer ${localStorage.getItem('cms_token')}`
          },
          credentials: 'omit' // Disable credentials to avoid CORS issues
        });

        if (!response.ok) {
          throw new Error(`API call failed with status: ${response.status}`);
        }

        const data = await response.json();
        console.log('FieldConfigDialog: Field configurations loaded:', data);

        if (data && Array.isArray(data)) {
          console.log(`FieldConfigDialog: Received ${data.length} field configurations`);
          setFieldConfigs(data);

          // If we're editing a field, make sure to set the attributes and validations
          // after the field configs are loaded
          if (field) {
            console.log('Setting field data after loading configs:', field);
            console.log('Setting attributes from field:', field.attributes);
            console.log('Setting validations from field:', field.validations);
            setAttributesState(field.attributes || {});
            setValidationsState(field.validations || {});
          }
        } else {
          console.error('FieldConfigDialog: Invalid response data format:', data);
          setConfigsError('Invalid response data format');
        }
      } catch (error) {
        console.error('FieldConfigDialog: Error fetching field configurations:', error);
        setConfigsError(`Failed to load field configurations: ${error.message}`);
      } finally {
        setConfigsLoading(false);
      }
    };

    fetchFieldConfigs();
  }, [fieldTypeId, field]);

  // Handle tab change - fetch configs when Advanced tab is clicked
  const handleTabChange = (value: string) => {
    console.log(`Tab changed to ${value}`);

    // If Advanced tab is clicked and we have a fieldTypeId but no configs, fetch them
    if (value === 'advanced' && fieldTypeId !== undefined && fieldConfigs.length === 0) {
      console.log('Advanced tab clicked, making direct API call to fetch field configs');

      // Make a direct API call to fetch field configs
      const fetchConfigs = async () => {
        setConfigsLoading(true);
        try {
          // Add a timestamp to prevent caching
          const timestamp = new Date().getTime();
          // Use the getBaseUrl function to ensure consistent API URL handling
          const baseUrl = getBaseUrl();
          const url = `${baseUrl}/field-configs/getByFieldTypeId/${fieldTypeId}?t=${timestamp}`;
          console.log(`Making API call to ${url}`);

          const response = await fetch(url, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'Cache-Control': 'no-cache',
              'Authorization': `Bearer ${localStorage.getItem('cms_token')}`
            },
            credentials: 'omit' // Disable credentials to avoid CORS issues
          });

          if (!response.ok) {
            throw new Error(`API call failed with status: ${response.status}`);
          }

          const data = await response.json();
          console.log('Direct API call response:', data);

          if (data && Array.isArray(data)) {
            setFieldConfigs(data);

            // If we're editing a field, make sure to set the attributes and validations
            // after the field configs are loaded
            if (field) {
              console.log('Setting field data after loading configs in tab change:', field);
              console.log('Setting attributes from field:', field.attributes);
              console.log('Setting validations from field:', field.validations);
              setAttributesState(field.attributes || {});
              setValidationsState(field.validations || {});
            }
          }
        } catch (error) {
          console.error('Error in direct API call:', error);
          setConfigsError(`Failed to load field configurations: ${error.message}`);
        } finally {
          setConfigsLoading(false);
        }
      };

      fetchConfigs();
    }
  };

  // Function to generate API ID from name
  const generateApiId = (name: string): string => {
    return name
      .toLowerCase()
      .replace(/\s+/g, '_') // Replace spaces with underscores
      .replace(/[^a-z0-9_]/g, '') // Remove any characters that aren't lowercase letters, numbers, or underscores
      .replace(/^[^a-z]+/, ''); // Ensure it starts with a letter
  };

  // Form initialization with react-hook-form
  const form = useForm<FieldFormValues>({
    resolver: zodResolver(fieldSchema),
    defaultValues: {
      name: field?.name || '',
      apiId: field?.apiId || '',
      type: field?.type || fieldType,
      required: field?.required || false,
      unique: field?.unique || false,
      description: field?.description || '',
      // Field configuration
      attributes: field?.attributes || {},
      validations: field?.validations || {},
    },
  });

  // Watch for changes to the name field and update apiId
  const watchedName = form.watch('name');
  const [isApiIdAutoGenerated, setIsApiIdAutoGenerated] = useState(!field);

  useEffect(() => {
    // Only auto-generate API ID if it's a new field or if auto-generation is enabled
    if (isApiIdAutoGenerated && watchedName) {
      const generatedApiId = generateApiId(watchedName);
      if (generatedApiId) {
        form.setValue('apiId', generatedApiId, { shouldValidate: true });
      }
    }

    // Also ensure properties are synced when name changes
    if (watchedName) {
      const displayNameFromName = watchedName.replace(/_/g, ' ');
      const currentAttributes = attributesState;

      // Only update if the values are different to avoid infinite loops
      if (currentAttributes['name'] !== watchedName || currentAttributes['display-name'] !== displayNameFromName) {
        const newAttributes = {
          ...currentAttributes,
          'name': watchedName,
          'display-name': displayNameFromName
        };
        setAttributesState(newAttributes);
        form.setValue('attributes', newAttributes);
      }
    }
  }, [watchedName, isApiIdAutoGenerated, form, attributesState]);

  // Initialize state with form values
  useEffect(() => {
    console.log('Field data received in FieldConfigDialog:', field);

    // Initialize attributes and validations from field data
    if (field) {
      console.log('Setting attributes from field:', field.attributes);
      console.log('Setting validations from field:', field.validations);
      setAttributesState(field.attributes || {});

      // Make sure validations include the required field
      const validations = field.validations || {};
      if (field.required) {
        validations.required = true;
      }
      setValidationsState(validations);

      setIsRequired(field.required || false);
      setIsUnique(field.unique || false);

      // Reset form values with field data
      // For the name field, use the 'name' attribute if available, otherwise convert display name to underscore format
      let formName = field.name || '';
      if (field.attributes && field.attributes['name']) {
        formName = field.attributes['name'];
      } else if (field.attributes && field.attributes['display-name']) {
        formName = field.attributes['display-name'].replace(/\s+/g, '_');
      }

      form.reset({
        name: formName,
        apiId: field.apiId || '',
        type: field.type || fieldType,
        required: field.required || false,
        unique: field.unique || false,
        description: field.description || '',
        attributes: field.attributes || {},
        validations: field.validations || {}
      });
    } else {
      // If no field data, initialize with empty objects
      setAttributesState({});
      setValidationsState({});
      setIsRequired(false);
      setIsUnique(false);
    }

    // If this is a component field and we have a componentId, initialize the component
    if (fieldType === FieldTypeEnum.COMPONENT && field?.componentId) {
      // Set the componentId in attributes if not already there
      if (!field.attributes?.componentId) {
        setAttributesState(prev => ({ ...prev, componentId: field.componentId }));
      }

      // If we have component details, set the selected component
      if (field.attributes?.componentDetails) {
        setSelectedComponent(field.attributes.componentDetails);
      } else {
        // Otherwise, we'll need to fetch the component details from the API
        // This would typically be done in a real implementation
        console.log('Would fetch component details for ID:', field.componentId);
      }
    }
  }, [field, fieldType, form]);

  const onSubmit = (data: FieldFormValues) => {
    // Validate required fields
    if (!data.name || data.name.trim() === '') {
      form.setError('name', {
        type: 'manual',
        message: 'Field name is required'
      });
      return;
    }

    if (!data.apiId || data.apiId.trim() === '') {
      form.setError('apiId', {
        type: 'manual',
        message: 'API ID is required'
      });
      return;
    }

    // For component fields, validate that a component is selected
    if (fieldType === FieldTypeEnum.COMPONENT && !attributesState.componentId) {
      toast({
        title: 'Component Required',
        description: 'Please select a component for this field',
        variant: 'destructive'
      });
      return;
    }

    // Show loading state
    setConfigsLoading(true);

    try {
      // Ensure validations.required is synchronized with isRequired
      const synchronizedValidations = {
        ...validationsState,
        required: isRequired
      };

      const fieldData: Field = {
        id: field?.id,
        name: data.name,
        apiId: data.apiId,
        type: data.type as FieldTypeEnum,
        required: isRequired,
        unique: isUnique,
        description: data.description,
        // Field configuration
        attributes: attributesState,
        validations: synchronizedValidations,
      };

      // For component fields, add the component ID
      if (fieldType === FieldTypeEnum.COMPONENT) {
        fieldData.componentId = attributesState.componentId;
      }

      console.log('Submitting field data:', fieldData);
      console.log('Field configurations:', fieldConfigs);

      // Log the field configuration values that will be saved
      console.log('Attributes to save:', attributesState);
      console.log('Validations to save:', validationsState);

      // Save the field data
      onSave(fieldData);

      // Reset form after successful save
      form.reset({
        name: '',
        apiId: '',
        type: fieldType,
        required: false,
        unique: false,
        description: '',
        attributes: {},
        validations: {},
      });

      // Reset state
      setAttributesState({});
      setValidationsState({});
      setIsRequired(false);
      setIsUnique(false);

      // Hide loading state
      setConfigsLoading(false);
      onClose();
    } catch (error) {
      console.error('Error in field submission:', error);
      setConfigsLoading(false);
      // Don't close the dialog so the user can see what went wrong
    }
  };

  // Handle name change to auto-generate API ID and convert spaces to underscores
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    // Convert spaces to underscores in the name field
    const nameWithUnderscores = inputValue.replace(/\s+/g, '_');

    // Update the form value
    form.setValue('name', nameWithUnderscores, { shouldValidate: true });

    // Also update the Properties section fields
    const newAttributes = {
      ...attributesState,
      'name': nameWithUnderscores,
      'display-name': inputValue  // Keep original text with spaces for display name
    };

    setAttributesState(newAttributes);
    form.setValue('attributes', newAttributes);

    // Auto-generation is now handled by the useEffect hook that watches the name field
  };

  // API ID auto-generation is now handled directly in the Switch component

  // Keep validations.required in sync with isRequired
  useEffect(() => {
    // Update validations.required when isRequired changes
    setValidationsState(prev => ({
      ...prev,
      required: isRequired
    }));
  }, [isRequired]);

  // Derive the field type label for display
  const getFieldTypeLabel = () => {
    switch (fieldType) {
      case FieldTypeEnum.TEXT:
        return 'Text';
      case FieldTypeEnum.NUMBER:
        return 'Number';
      case FieldTypeEnum.DATE:
        return 'Date';
      case FieldTypeEnum.IMAGE:
        return 'Image';
      case FieldTypeEnum.RICH_TEXT:
        return 'Rich Text';
      case FieldTypeEnum.MASK:
        return 'Mask';
      case FieldTypeEnum.CALENDAR:
        return 'Calendar';
      case FieldTypeEnum.EDITOR:
        return 'Editor';
      case FieldTypeEnum.PASSWORD:
        return 'Password';
      case FieldTypeEnum.AUTOCOMPLETE:
        return 'Autocomplete';
      case FieldTypeEnum.CASCADE_SELECT:
        return 'Cascade Select';
      case FieldTypeEnum.DROPDOWN:
        return 'Dropdown';
      case FieldTypeEnum.FILE:
        return 'File';
      case FieldTypeEnum.MULTI_STATE_CHECKBOX:
        return 'Multi State Checkbox';
      case FieldTypeEnum.MULTI_SELECT:
        return 'Multi Select';
      case FieldTypeEnum.MENTION:
        return 'Mention';
      case FieldTypeEnum.TEXTAREA:
        return 'Textarea';
      case FieldTypeEnum.OTP:
        return 'OTP';
      case FieldTypeEnum.CHECKBOX:
        return 'Checkbox';
      case FieldTypeEnum.RADIO_BUTTON:
        return 'Radio Button';
      case FieldTypeEnum.INPUT_SWITCH:
        return 'Input Switch';
      case FieldTypeEnum.BOOLEAN:
        return 'Boolean';
      case FieldTypeEnum.JSON:
        return 'JSON';
      case FieldTypeEnum.EMAIL:
        return 'Email';
      case FieldTypeEnum.MEDIA:
        return 'Media';
      case FieldTypeEnum.ENUMERATION:
        return 'Enumeration';
      case FieldTypeEnum.RELATION:
        return 'Relation';
      case FieldTypeEnum.UID:
        return 'UID';
      case FieldTypeEnum.COMPONENT:
        return 'Component';
      case FieldTypeEnum.DYNAMIC_ZONE:
        return 'Dynamic Zone';
      case FieldTypeEnum.INPUT_MASK:
        return 'Input Mask';
      case FieldTypeEnum.INPUT_TEXTAREA:
        return 'Textarea';
      default:
        return fieldType;
    }
  };

  return (
    <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[800px] p-0 overflow-hidden max-h-[90vh]">
        <div className="flex flex-col h-full max-h-[90vh]">
          <div className="flex items-center justify-between p-6 border-b">
            <div className="flex items-center gap-2">
              <DialogClose asChild>
                <button className="rounded-full p-2.5 hover:bg-muted border border-border">
                  <ArrowLeft className="h-5 w-5 text-foreground" />
                </button>
              </DialogClose>
              <div>
                <h2 className="text-xl font-semibold">{field ? 'Edit Field' : 'Add New Field'}</h2>
                <p className="text-sm text-muted-foreground">
                  {field ? field.name : collectionName} - {getFieldTypeLabel()}
                </p>
              </div>
            </div>

          </div>

          <Tabs defaultValue="basic" className="flex-1 overflow-hidden" onValueChange={handleTabChange}>
            <div className="border-b px-6">
              <TabsList className="h-12">
                <TabsTrigger value="basic" className="data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:text-primary rounded-none">
                  Basic
                </TabsTrigger>
                <TabsTrigger value="advanced" className="data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:text-primary rounded-none">
                  Advanced
                </TabsTrigger>
              </TabsList>
            </div>

            <form onSubmit={form.handleSubmit(onSubmit)} className="flex-1 p-6">
              <TabsContent value="basic" className="space-y-6 mt-0">
                {/* Basic Settings - Only Name field */}
                <div>
                  <Label htmlFor="name">
                    Name
                  </Label>
                  <Input
                    id="name"
                    placeholder="e.g., full_name, email_address, phone_number"
                    className="mt-2 border-blue-200 dark:border-blue-800 focus:border-blue-400 dark:focus:border-blue-600"
                    autoComplete="off"
                    value={watchedName || ''}
                    onChange={handleNameChange}
                  />

                  {form.formState.errors.name && (
                    <p className="text-sm text-destructive mt-1">{form.formState.errors.name.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Description"
                    className="mt-2"
                    {...form.register('description')}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Description will be displayed below the field in the content form
                  </p>
                </div>

                {/* Component Selector for Component field type */}
                {fieldType === FieldTypeEnum.COMPONENT && (
                  <div className="space-y-2">
                    <Label htmlFor="component">Select Component</Label>
                    <Button
                      type="button"
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                      onClick={() => setComponentSelectorOpen(true)}
                    >
                      {selectedComponent ? (
                        <span>{selectedComponent.name}</span>
                      ) : (
                        <span className="text-muted-foreground">Select a component</span>
                      )}
                    </Button>
                    <p className="text-xs text-muted-foreground">
                      Choose a reusable component to use in this field
                    </p>

                    {/* Component Selector Dialog */}
                    <ComponentSelector
                      open={componentSelectorOpen}
                      onClose={() => setComponentSelectorOpen(false)}
                      onSelect={(component) => {
                        setSelectedComponent(component);
                        setComponentSelectorOpen(false);
                        // Store the component ID in attributes
                        const newAttributes = {
                          ...attributesState,
                          componentId: component.id
                        };
                        setAttributesState(newAttributes);
                        form.setValue('attributes', newAttributes);
                      }}
                    />
                  </div>
                )}
              </TabsContent>

              <TabsContent value="advanced" className="overflow-y-auto max-h-[50vh] pr-2 scrollbar-thin">
                <div>
                  <div className="flex justify-between items-center">
                    <Label htmlFor="apiId">API ID</Label>
                    <div className="flex items-center gap-2">
                      <Label htmlFor="autoGenerate" className="text-xs cursor-pointer mr-2">
                        Auto-generate
                      </Label>
                      <Switch
                        id="autoGenerate"
                        checked={isApiIdAutoGenerated}
                        onCheckedChange={(checked) => {
                          setIsApiIdAutoGenerated(checked);
                          if (checked && watchedName) {
                            const generatedApiId = generateApiId(watchedName);
                            if (generatedApiId) {
                              form.setValue('apiId', generatedApiId, { shouldValidate: true });
                            }
                          }
                        }}
                        className="cursor-pointer"
                      />
                    </div>
                  </div>
                  <Input
                    id="apiId"
                    placeholder="API ID"
                    className="mt-2"
                    readOnly={isApiIdAutoGenerated}
                    {...form.register('apiId')}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    API identifier for this field {isApiIdAutoGenerated ? '(auto-generated from name)' : ''}
                  </p>
                  {form.formState.errors.apiId && (
                    <p className="text-sm text-destructive mt-1">{form.formState.errors.apiId.message}</p>
                  )}
                </div>

                {/* Debug information */}
                <div className="mb-4 mt-4 p-2 bg-muted rounded">
                  <p className="text-xs">Field Type ID: {fieldTypeId || 'Not provided'}</p>
                  <p className="text-xs">Field Configs Count: {fieldConfigs.length}</p>
                  <p className="text-xs">Loading: {configsLoading ? 'Yes' : 'No'}</p>
                  <p className="text-xs">Error: {configsError || 'None'}</p>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => {
                      if (fieldTypeId !== undefined) {
                        console.log('Manual fetch button clicked');
                        const fetchConfigs = async () => {
                          setConfigsLoading(true);
                          try {
                            // Add a timestamp to prevent caching
                            const timestamp = new Date().getTime();
                            // Use the getBaseUrl function to ensure consistent API URL handling
                            const baseUrl = getBaseUrl();
                            const url = `${baseUrl}/field-configs/getByFieldTypeId/${fieldTypeId}?t=${timestamp}`;
                            console.log(`Manual API call to ${url}`);

                            const response = await fetch(url, {
                              method: 'GET',
                              headers: {
                                'Content-Type': 'application/json',
                                'Accept': 'application/json',
                                'Cache-Control': 'no-cache',
                                'Authorization': `Bearer ${localStorage.getItem('cms_token')}`
                              },
                              credentials: 'omit' // Disable credentials to avoid CORS issues
                            });

                            if (!response.ok) {
                              throw new Error(`API call failed with status: ${response.status}`);
                            }

                            const data = await response.json();
                            console.log('Manual API call response:', data);

                            if (data && Array.isArray(data)) {
                              setFieldConfigs(data);
                            }
                          } catch (error) {
                            console.error('Error in manual API call:', error);
                            setConfigsError(`Failed to load field configurations: ${error.message}`);
                          } finally {
                            setConfigsLoading(false);
                          }
                        };

                        fetchConfigs();
                      }
                    }}
                  >
                    Manually Fetch Configs
                  </Button>
                </div>

                {/* Basic field settings */}
                <div className="mb-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="required-field" className="font-medium">Required field</Label>
                      <p className="text-xs text-muted-foreground">
                        You won't be able to create an entry if this field is empty
                      </p>
                    </div>
                    <Switch
                      id="required-field"
                      checked={isRequired}
                      onCheckedChange={(checked) => {
                        setIsRequired(checked);
                        form.setValue('required', checked);
                        // Also update the required validation
                        const newValidations = { ...validationsState, required: checked };
                        setValidationsState(newValidations);
                        form.setValue('validations', newValidations);
                      }}
                    />
                  </div>
                </div>

                <div className="mb-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="unique-field" className="font-medium">Unique field</Label>
                      <p className="text-xs text-muted-foreground">
                        You won't be able to create an entry if there is an existing entry with identical content
                      </p>
                    </div>
                    <Switch
                      id="unique-field"
                      checked={isUnique}
                      onCheckedChange={(checked) => {
                        setIsUnique(checked);
                        form.setValue('unique', checked);
                      }}
                    />
                  </div>
                </div>

                {/* Nested tabs for field configuration categories */}
                {configsLoading ? (
                  <div className="flex items-center justify-center h-32">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    <span className="ml-2">Loading field configurations...</span>
                  </div>
                ) : configsError ? (
                  <div className="p-4 border border-red-200 bg-red-50 rounded-md text-red-700">
                    <p>Error loading field configurations. Please try again.</p>
                  </div>
                ) : fieldConfigs.length > 0 ? (
                  <Tabs defaultValue="properties" className="mt-6">
                    <TabsList className="w-full grid grid-cols-3 p-1 bg-muted/30 border border-border rounded-md">
                      <TabsTrigger value="properties" className="text-sm flex items-center gap-2 data-[state=active]:bg-background">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-settings-2"><path d="M20 7h-9"/><path d="M14 17H5"/><circle cx="17" cy="17" r="3"/><circle cx="7" cy="7" r="3"/></svg>
                        Properties
                      </TabsTrigger>
                      <TabsTrigger value="validations" className="text-sm flex items-center gap-2 data-[state=active]:bg-background">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-check-circle"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/><path d="m9 11 3 3L22 4"/></svg>
                        Validations
                      </TabsTrigger>
                      <TabsTrigger value="attributes" className="text-sm flex items-center gap-2 data-[state=active]:bg-background">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-tag"><path d="M12 2H2v10l9.29 9.29c.94.94 2.48.94 3.42 0l6.58-6.58c.94-.94.94-2.48 0-3.42L12 2Z"/><path d="M7 7h.01"/></svg>
                        Attributes
                      </TabsTrigger>
                    </TabsList>

                    {/* Properties Tab */}
                    <TabsContent value="properties" className="mt-4 space-y-6">
                      <div className="bg-muted/10 rounded-md border border-border p-4">
                        <div className="flex items-center gap-2 mb-3 pb-2 border-b border-border">
                          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary"><path d="M20 7h-9"/><path d="M14 17H5"/><circle cx="17" cy="17" r="3"/><circle cx="7" cy="7" r="3"/></svg>
                          <h3 className="text-sm font-medium">Basic Properties</h3>
                        </div>

                        {fieldConfigs
                          .filter(config =>
                            ['name', 'display-name', 'description'].includes(config.configName)
                          )
                          .map((config, index) => (
                            <FieldConfigItem
                              key={`property-${config.configName}-${index}`}
                              config={config}
                              fieldData={attributesState}
                              isLoading={configsLoading}
                              onChange={(configName: string, value: any) => {
                                const newAttributes = { ...attributesState, [configName]: value };

                                // If display-name is being changed, also update the main name field
                                if (configName === 'display-name') {
                                  const nameWithUnderscores = value.replace(/\s+/g, '_');
                                  newAttributes['name'] = nameWithUnderscores;
                                  form.setValue('name', nameWithUnderscores, { shouldValidate: true });
                                }

                                // If name is being changed, also update display-name
                                if (configName === 'name') {
                                  // Convert underscores back to spaces for display name
                                  const displayName = value.replace(/_/g, ' ');
                                  newAttributes['display-name'] = displayName;
                                }

                                setAttributesState(newAttributes);
                                form.setValue('attributes', newAttributes);
                              }}
                            />
                          ))
                        }
                      </div>

                      <div className="bg-muted/10 rounded-md border border-border p-4">
                        <div className="flex items-center gap-2 mb-3 pb-2 border-b border-border">
                          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"/><path d="m2 12 5-5"/><path d="M5 12H2"/><path d="m12 2 5 5"/><path d="M12 5V2"/><path d="m22 12-5 5"/><path d="M19 12h3"/><path d="m12 22-5-5"/><path d="M12 19v3"/></svg>
                          <h3 className="text-sm font-medium">Advanced Properties</h3>
                        </div>

                        {fieldConfigs
                          .filter(config =>
                            ['dependOn', 'unique-id', 'is-visible', 'get-api-url'].includes(config.configName)
                          )
                          .map((config, index) => (
                            <FieldConfigItem
                              key={`property-${config.configName}-${index}`}
                              config={config}
                              fieldData={attributesState}
                              isLoading={configsLoading}
                              onChange={(configName: string, value: any) => {
                                const newAttributes = { ...attributesState, [configName]: value };
                                setAttributesState(newAttributes);
                                form.setValue('attributes', newAttributes);
                              }}
                            />
                          ))
                        }
                      </div>

                      {fieldConfigs.filter(config =>
                        ['dependOn', 'unique-id', 'display-name', 'name', 'description', 'is-visible', 'get-api-url'].includes(config.configName)
                      ).length === 0 && (
                        <div className="bg-muted/10 rounded-md border border-border p-4">
                          <p className="text-sm text-muted-foreground italic">No property configurations available for this field type</p>
                        </div>
                      )}
                    </TabsContent>

                    {/* Validations Tab */}
                    <TabsContent value="validations" className="mt-4 space-y-6">
                      <div className="bg-muted/10 rounded-md border border-border p-4">
                        <div className="flex items-center gap-2 mb-3 pb-2 border-b border-border">
                          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/><path d="m9 11 3 3L22 4"/></svg>
                          <h3 className="text-sm font-medium">Required Validations</h3>
                        </div>

                        {fieldConfigs
                          .filter(config =>
                            ['required'].includes(config.configName)
                          )
                          .map((config, index) => (
                            <FieldConfigItem
                              key={`validation-${config.configName}-${index}`}
                              config={config}
                              fieldData={validationsState}
                              isLoading={configsLoading}
                              onChange={(configName: string, value: any) => {
                                const newValidations = { ...validationsState, [configName]: value };
                                setValidationsState(newValidations);
                                form.setValue('validations', newValidations);

                                // If this is the required validation, also update the main isRequired state
                                if (configName === 'required') {
                                  setIsRequired(value);
                                  form.setValue('required', value);
                                }
                              }}
                            />
                          ))
                        }
                      </div>

                      <div className="bg-muted/10 rounded-md border border-border p-4">
                        <div className="flex items-center gap-2 mb-3 pb-2 border-b border-border">
                          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"/><path d="m15 9-6 6"/><path d="m9 9 6 6"/></svg>
                          <h3 className="text-sm font-medium">Constraints</h3>
                        </div>

                        {fieldConfigs
                          .filter(config =>
                            ['minLength', 'maxLength', 'minValue', 'maxValue', 'minFractionDigits', 'maxFractionDigits'].includes(config.configName)
                          )
                          .map((config, index) => (
                            <FieldConfigItem
                              key={`validation-${config.configName}-${index}`}
                              config={config}
                              fieldData={validationsState}
                              isLoading={configsLoading}
                              onChange={(configName: string, value: any) => {
                                const newValidations = { ...validationsState, [configName]: value };
                                setValidationsState(newValidations);
                                form.setValue('validations', newValidations);
                              }}
                            />
                          ))
                        }
                      </div>

                      <div className="bg-muted/10 rounded-md border border-border p-4">
                        <div className="flex items-center gap-2 mb-3 pb-2 border-b border-border">
                          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/></svg>
                          <h3 className="text-sm font-medium">Format Validations</h3>
                        </div>

                        {fieldConfigs
                          .filter(config =>
                            ['regex', 'api-url'].includes(config.configName)
                          )
                          .map((config, index) => (
                            <FieldConfigItem
                              key={`validation-${config.configName}-${index}`}
                              config={config}
                              fieldData={validationsState}
                              isLoading={configsLoading}
                              onChange={(configName: string, value: any) => {
                                const newValidations = { ...validationsState, [configName]: value };
                                setValidationsState(newValidations);
                                form.setValue('validations', newValidations);
                              }}
                            />
                          ))
                        }
                      </div>

                      {fieldConfigs.filter(config =>
                        ['required', 'minLength', 'maxLength', 'regex', 'api-url', 'minValue', 'maxValue', 'minFractionDigits', 'maxFractionDigits'].includes(config.configName)
                      ).length === 0 && (
                        <div className="bg-muted/10 rounded-md border border-border p-4">
                          <p className="text-sm text-muted-foreground italic">No validation configurations available for this field type</p>
                        </div>
                      )}
                    </TabsContent>

                    {/* Attributes Tab */}
                    <TabsContent value="attributes" className="mt-4 space-y-6">
                      <div className="bg-muted/10 rounded-md border border-border p-4">
                        <div className="flex items-center gap-2 mb-3 pb-2 border-b border-border">
                          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary"><path d="M12 2H2v10l9.29 9.29c.94.94 2.48.94 3.42 0l6.58-6.58c.94-.94.94-2.48 0-3.42L12 2Z"/><path d="M7 7h.01"/></svg>
                          <h3 className="text-sm font-medium">Display Attributes</h3>
                        </div>

                        {fieldConfigs
                          .filter(config =>
                            ['placeholder', 'helpText', 'FloatLable', 'tooltip', 'tooltipOptions'].includes(config.configName)
                          )
                          .map((config, index) => (
                            <FieldConfigItem
                              key={`attribute-${config.configName}-${index}`}
                              config={config}
                              fieldData={attributesState}
                              isLoading={configsLoading}
                              onChange={(configName: string, value: any) => {
                                const newAttributes = { ...attributesState, [configName]: value };
                                setAttributesState(newAttributes);
                                form.setValue('attributes', newAttributes);
                              }}
                            />
                          ))
                        }
                      </div>

                      <div className="bg-muted/10 rounded-md border border-border p-4">
                        <div className="flex items-center gap-2 mb-3 pb-2 border-b border-border">
                          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/></svg>
                          <h3 className="text-sm font-medium">Behavior Attributes</h3>
                        </div>

                        {fieldConfigs
                          .filter(config =>
                            ['variant', 'invalid', 'disabled', 'autoClear', 'keyfilter', 'icon'].includes(config.configName)
                          )
                          .map((config, index) => (
                            <FieldConfigItem
                              key={`attribute-${config.configName}-${index}`}
                              config={config}
                              fieldData={attributesState}
                              isLoading={configsLoading}
                              onChange={(configName: string, value: any) => {
                                const newAttributes = { ...attributesState, [configName]: value };
                                setAttributesState(newAttributes);
                                form.setValue('attributes', newAttributes);
                              }}
                            />
                          ))
                        }
                      </div>

                      <div className="bg-muted/10 rounded-md border border-border p-4">
                        <div className="flex items-center gap-2 mb-3 pb-2 border-b border-border">
                          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary"><path d="M21 7 6.82 21.18a2.83 2.83 0 0 1-3.99-.01v0a2.83 2.83 0 0 1 0-4L17 3"/><path d="m16 2 6 6"/><path d="M12 16H4"/><path d="M4 10v6"/></svg>
                          <h3 className="text-sm font-medium">Other Attributes</h3>
                        </div>

                        {fieldConfigs
                          .filter(config =>
                            !['dependOn', 'unique-id', 'display-name', 'name', 'description', 'is-visible', 'get-api-url',
                              'required', 'minLength', 'maxLength', 'regex', 'api-url', 'minValue', 'maxValue', 'minFractionDigits', 'maxFractionDigits',
                              'placeholder', 'helpText', 'FloatLable', 'tooltip', 'tooltipOptions',
                              'variant', 'invalid', 'disabled', 'autoClear', 'keyfilter', 'icon'].includes(config.configName)
                          )
                          .map((config, index) => (
                            <FieldConfigItem
                              key={`attribute-${config.configName}-${index}`}
                              config={config}
                              fieldData={attributesState}
                              isLoading={configsLoading}
                              onChange={(configName: string, value: any) => {
                                const newAttributes = { ...attributesState, [configName]: value };
                                setAttributesState(newAttributes);
                                form.setValue('attributes', newAttributes);
                              }}
                            />
                          ))
                        }
                      </div>

                      {fieldConfigs.filter(config =>
                        !['dependOn', 'unique-id', 'display-name', 'name', 'description', 'is-visible', 'get-api-url',
                          'required', 'minLength', 'maxLength', 'regex', 'api-url', 'minValue', 'maxValue', 'minFractionDigits', 'maxFractionDigits'].includes(config.configName)
                      ).length === 0 && (
                        <div className="bg-muted/10 rounded-md border border-border p-4">
                          <p className="text-sm text-muted-foreground italic">No attribute configurations available for this field type</p>
                        </div>
                      )}
                    </TabsContent>
                  </Tabs>
                ) : (
                  <div className="p-4 border border-yellow-500/20 bg-yellow-500/10 rounded-md text-yellow-600 dark:text-yellow-400 mb-4">
                    <p>No field configurations found for this field type. Please check the console for more information.</p>
                  </div>
                )}
              </TabsContent>

              <div className="flex justify-between items-center pt-6 mt-6 border-t">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  className="text-gray-700 hover:text-gray-900 hover:bg-gray-100 border-gray-300 px-5 py-2 h-10 text-base font-medium"
                >
                  Cancel
                </Button>
                <div className="flex gap-3">
                  {!field && onAddAnother && (
                    <Button
                      type="button"
                      variant="outline"
                      className="flex items-center gap-1 h-10 px-5 py-2 text-base font-medium"
                      disabled={configsLoading}
                      onClick={() => {
                        // Validate the form first
                        const values = form.getValues();
                        let isValid = true;

                        if (!values.name || values.name.trim() === '') {
                          form.setError('name', {
                            type: 'manual',
                            message: 'Field name is required'
                          });
                          isValid = false;
                        }

                        if (!values.apiId || values.apiId.trim() === '') {
                          form.setError('apiId', {
                            type: 'manual',
                            message: 'API ID is required'
                          });
                          isValid = false;
                        }

                        if (isValid) {
                          onSubmit(values);

                          // Reset form after successful save
                          form.reset({
                            name: '',
                            apiId: '',
                            type: fieldType,
                            required: false,
                            unique: false,
                            description: '',
                            attributes: {},
                            validations: {},
                          });

                          // Reset state
                          setAttributesState({});
                          setValidationsState({});

                          if (onAddAnother) onAddAnother();
                        }
                      }}
                    >
                      {configsLoading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
                          Adding...
                        </>
                      ) : (
                        <>
                          <span>+</span> Add another field
                        </>
                      )}
                    </Button>
                  )}
                  <Button
                    type="submit"
                    className="bg-indigo-600 hover:bg-indigo-700 text-white h-10 px-5 py-2 text-base font-medium"
                    disabled={configsLoading}
                  >
                    {configsLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Saving...
                      </>
                    ) : (
                      field ? 'Update field' : 'Create field'
                    )}
                  </Button>
                </div>
              </div>
            </form>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
}