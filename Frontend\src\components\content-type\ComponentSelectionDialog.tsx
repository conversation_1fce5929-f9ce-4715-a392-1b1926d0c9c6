import React, { useEffect, useState } from 'react';
import { componentsApi } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';
import { Component } from '@/lib/store';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Layers, Search, Repeat } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
  Form,
} from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

interface ComponentSelectionDialogProps {
  open: boolean;
  onClose: () => void;
  onSelect: (component: Component, isRepeatable: boolean, minRepeatOccurrences?: number, maxRepeatOccurrences?: number) => void;
}

const formSchema = z.object({
  isRepeatable: z.boolean().default(false),
  minRepeatOccurrences: z.number().min(0).optional(),
  maxRepeatOccurrences: z.number().min(1).optional(),
  componentName: z.string().min(1, "Component name is required"),
  componentDisplayName: z.string().min(1, "Display name is required"),
  additionalInfo: z.string().optional(),
  additionalInfoImage: z.string().optional(),
});

export default function ComponentSelectionDialog({
  open,
  onClose,
  onSelect,
}: ComponentSelectionDialogProps) {
  const { toast } = useToast();
  const [components, setComponents] = useState<Component[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedComponent, setSelectedComponent] = useState<Component | null>(null);

  // Initialize form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      isRepeatable: false,
      minRepeatOccurrences: 0,
      maxRepeatOccurrences: 10,
      componentName: '',
      componentDisplayName: '',
      additionalInfo: '',
      additionalInfoImage: '',
    },
  });

  // Watch for changes to isRepeatable
  const isRepeatable = form.watch('isRepeatable');

  useEffect(() => {
    if (open) {
      fetchComponents();
      // Reset form when dialog opens
      form.reset({
        isRepeatable: false,
        minRepeatOccurrences: 0,
        maxRepeatOccurrences: 10,
        componentName: '',
        componentDisplayName: '',
        additionalInfo: '',
        additionalInfoImage: '',
      });
      setSelectedComponent(null);
    }
  }, [open, form]);

  const fetchComponents = async () => {
    setLoading(true);
    try {
      const response = await componentsApi.getActive();
      console.log('Components data:', response.data);

      // Transform the data to match our Component interface
      const formattedComponents = response.data.map((component: any) => ({
        id: component.id.toString(),
        name: component.componentName || 'Unnamed Component',
        apiId: component.componentApiId || '',
        description: component.componentDesc || '',
        fields: [],
        isActive: component.isActive !== false, // Default to true if not specified
        createdAt: '',
        updatedAt: ''
      }));

      setComponents(formattedComponents);
    } catch (error) {
      console.error('Error fetching components:', error);
      toast({
        title: 'Error',
        description: 'Failed to load components',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Filter components based on search term
  const filteredComponents = components.filter(component =>
    component.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    component.apiId.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (component.description && component.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Handle component selection
  const handleComponentSelect = (component: Component) => {
    setSelectedComponent(component);

    // Update form values
    form.setValue('componentName', component.name);
    form.setValue('componentDisplayName', '');
    form.setValue('additionalInfo', '');
    form.setValue('additionalInfoImage', '');
  };

  // Handle form submission
  const onSubmit = (values: z.infer<typeof formSchema>) => {
    if (selectedComponent) {
      // Include the additional fields in the component data
      const componentWithDetails = {
        ...selectedComponent,
        name: values.componentName,
        displayName: values.componentDisplayName,
        additionalInfo: values.additionalInfo || '',
        additionalInfoImage: values.additionalInfoImage || ''
      };

      onSelect(
        componentWithDetails,
        values.isRepeatable,
        values.isRepeatable ? values.minRepeatOccurrences : undefined,
        values.isRepeatable ? values.maxRepeatOccurrences : undefined
      );
    }
  };

  return (
    <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>Add Component</DialogTitle>
          <DialogDescription>
            Select a component to add its fields to your collection
          </DialogDescription>
        </DialogHeader>

        <div className="py-4 flex-1 overflow-hidden flex flex-col min-h-[400px]">
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search components..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          <div className="flex-1 overflow-y-auto pr-2 mb-4">
            {loading ? (
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="p-4 border rounded-md">
                    <Skeleton className="h-5 w-1/2 mb-2" />
                    <Skeleton className="h-4 w-3/4 mb-2" />
                    <Skeleton className="h-4 w-1/3" />
                  </div>
                ))}
              </div>
            ) : filteredComponents.length === 0 ? (
              <div className="text-center py-8">
                <Layers className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No components found</h3>
                <p className="text-sm text-muted-foreground">
                  {searchTerm ? 'Try a different search term' : 'Create components first'}
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredComponents.map((component) => (
                  <div
                    key={component.id}
                    className={`p-4 border rounded-md cursor-pointer transition-colors ${selectedComponent?.id === component.id ? 'border-primary bg-primary/10' : 'hover:border-primary hover:bg-primary/5'}`}
                    onClick={() => handleComponentSelect(component)}
                  >
                    <div className="flex items-center justify-between mb-1">
                      <h3 className="font-medium text-lg">{component.name}</h3>
                      <div className="p-1 rounded-full bg-primary/10">
                        <Layers className="h-4 w-4 text-primary" />
                      </div>
                    </div>
                    {component.description && (
                      <p className="text-sm text-muted-foreground mb-2">{component.description}</p>
                    )}
                    <div className="text-xs text-muted-foreground">
                      <span className="font-medium">API ID:</span> {component.apiId}
                    </div>
                    <div className="text-xs text-muted-foreground mt-1">
                      <span className="font-medium">{component.fields.length}</span> fields included
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              {selectedComponent && (
                <div className="border-t pt-4 mb-4 space-y-4">
                  <h4 className="font-medium text-sm">Collection Component Details</h4>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="componentName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium">Name</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="Component name in collection"
                            className="h-9"
                          />
                        </FormControl>
                        <FormDescription className="text-xs">
                          Name for this component in the collection
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="componentDisplayName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium">Display Name *</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="Display name for users"
                            className="h-9"
                          />
                        </FormControl>
                        <FormDescription className="text-xs">
                          Display name shown to users
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="additionalInfo"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">Additional Info (Optional)</FormLabel>
                      <FormControl>
                        <Textarea
                          {...field}
                          placeholder="Additional information about this component"
                          className="min-h-[60px]"
                        />
                      </FormControl>
                      <FormDescription className="text-xs">
                        Extra details or instructions for this component
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="additionalInfoImage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">Additional Info Image (Optional)</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Image URL or path"
                          className="h-9"
                        />
                      </FormControl>
                      <FormDescription className="text-xs">
                        URL or path to an image for this component
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                </div>
              )}

              <div className="border-t pt-4">
              <FormField
                control={form.control}
                name="isRepeatable"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>
                        Make this component repeatable
                      </FormLabel>
                      <FormDescription>
                        Allow multiple instances of this component
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              {isRepeatable && (
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="minRepeatOccurrences"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Minimum Occurrences</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min={0}
                            {...field}
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormDescription>
                          Minimum number of instances required
                        </FormDescription>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="maxRepeatOccurrences"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Maximum Occurrences</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min={1}
                            {...field}
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                          />
                        </FormControl>
                        <FormDescription>
                          Maximum number of instances allowed
                        </FormDescription>
                      </FormItem>
                    )}
                  />
                </div>
              )}
              </div>
            </form>
          </Form>
        </div>

        <DialogFooter className="flex justify-between border-t pt-4">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={form.handleSubmit(onSubmit)}
            disabled={!selectedComponent}
            type="submit"
          >
            Add Component
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
