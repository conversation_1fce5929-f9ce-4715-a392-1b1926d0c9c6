/**
 * Utility script to fix inconsistent validation objects in existing fields
 * This addresses the issue where some fields have validations: {} and others have validations: {required: false}
 */

import { collectionFieldsApi, componentFieldsApi } from '@/lib/api';

interface FieldData {
  id: number;
  additionalInformation: string;
}

interface ParsedAdditionalInfo {
  name?: string;
  apiId?: string;
  description?: string;
  required?: boolean;
  unique?: boolean;
  type?: string;
  attributes?: any;
  validations?: any;
}

/**
 * Fix validation objects in collection fields
 */
export async function fixCollectionFieldValidations(): Promise<void> {
  console.log('🔧 Starting collection field validation fix...');
  
  try {
    // Get all collection fields
    const response = await collectionFieldsApi.getAll();
    const fields: FieldData[] = response.data || [];
    
    console.log(`📊 Found ${fields.length} collection fields to check`);
    
    let fixedCount = 0;
    let errorCount = 0;
    
    for (const field of fields) {
      try {
        if (!field.additionalInformation) {
          console.log(`⏭️ Skipping field ${field.id} - no additionalInformation`);
          continue;
        }
        
        // Parse the additional information
        const additionalInfo: ParsedAdditionalInfo = JSON.parse(field.additionalInformation);
        
        // Check if validations object needs fixing
        const needsFix = !additionalInfo.validations || 
                        (typeof additionalInfo.validations === 'object' && 
                         !additionalInfo.validations.hasOwnProperty('required'));
        
        if (needsFix) {
          console.log(`🔧 Fixing field ${field.id} - ${additionalInfo.name || 'Unnamed'}`);
          
          // Create the fixed validation object
          const fixedValidations = {
            required: additionalInfo.required || false,
            ...(additionalInfo.validations || {})
          };
          
          // Update the additional information
          const updatedAdditionalInfo = {
            ...additionalInfo,
            validations: fixedValidations
          };
          
          // Prepare the update payload
          const updatePayload = {
            id: field.id,
            additionalInformation: JSON.stringify(updatedAdditionalInfo)
          };
          
          // Update the field
          await collectionFieldsApi.update(field.id.toString(), updatePayload);
          
          console.log(`✅ Fixed field ${field.id}`);
          fixedCount++;
        } else {
          console.log(`✅ Field ${field.id} already has correct validation format`);
        }
        
      } catch (error) {
        console.error(`❌ Error processing field ${field.id}:`, error);
        errorCount++;
      }
    }
    
    console.log(`🎉 Collection field validation fix complete!`);
    console.log(`✅ Fixed: ${fixedCount} fields`);
    console.log(`❌ Errors: ${errorCount} fields`);
    
  } catch (error) {
    console.error('❌ Error fetching collection fields:', error);
    throw error;
  }
}

/**
 * Fix validation objects in component fields
 */
export async function fixComponentFieldValidations(): Promise<void> {
  console.log('🔧 Starting component field validation fix...');
  
  try {
    // Get all component fields
    const response = await componentFieldsApi.getAll();
    const fields: FieldData[] = response.data || [];
    
    console.log(`📊 Found ${fields.length} component fields to check`);
    
    let fixedCount = 0;
    let errorCount = 0;
    
    for (const field of fields) {
      try {
        if (!field.additionalInformation) {
          console.log(`⏭️ Skipping field ${field.id} - no additionalInformation`);
          continue;
        }
        
        // Parse the additional information
        const additionalInfo: ParsedAdditionalInfo = JSON.parse(field.additionalInformation);
        
        // Check if validations object needs fixing
        const needsFix = !additionalInfo.validations || 
                        (typeof additionalInfo.validations === 'object' && 
                         !additionalInfo.validations.hasOwnProperty('required'));
        
        if (needsFix) {
          console.log(`🔧 Fixing field ${field.id} - ${additionalInfo.name || 'Unnamed'}`);
          
          // Create the fixed validation object
          const fixedValidations = {
            required: additionalInfo.required || false,
            ...(additionalInfo.validations || {})
          };
          
          // Update the additional information
          const updatedAdditionalInfo = {
            ...additionalInfo,
            validations: fixedValidations
          };
          
          // Prepare the update payload
          const updatePayload = {
            id: field.id,
            additionalInformation: JSON.stringify(updatedAdditionalInfo)
          };
          
          // Update the field
          await componentFieldsApi.update(field.id.toString(), updatePayload);
          
          console.log(`✅ Fixed field ${field.id}`);
          fixedCount++;
        } else {
          console.log(`✅ Field ${field.id} already has correct validation format`);
        }
        
      } catch (error) {
        console.error(`❌ Error processing field ${field.id}:`, error);
        errorCount++;
      }
    }
    
    console.log(`🎉 Component field validation fix complete!`);
    console.log(`✅ Fixed: ${fixedCount} fields`);
    console.log(`❌ Errors: ${errorCount} fields`);
    
  } catch (error) {
    console.error('❌ Error fetching component fields:', error);
    throw error;
  }
}

/**
 * Fix validation objects in both collection and component fields
 */
export async function fixAllFieldValidations(): Promise<void> {
  console.log('🚀 Starting comprehensive field validation fix...');
  
  try {
    await fixCollectionFieldValidations();
    await fixComponentFieldValidations();
    
    console.log('🎉 All field validation fixes complete!');
  } catch (error) {
    console.error('❌ Error during field validation fix:', error);
    throw error;
  }
}

/**
 * Preview what fields would be fixed without actually updating them
 */
export async function previewFieldValidationFixes(): Promise<void> {
  console.log('👀 Previewing field validation fixes...');
  
  try {
    // Check collection fields
    const collectionResponse = await collectionFieldsApi.getAll();
    const collectionFields: FieldData[] = collectionResponse.data || [];
    
    let collectionFieldsToFix = 0;
    
    for (const field of collectionFields) {
      if (field.additionalInformation) {
        try {
          const additionalInfo: ParsedAdditionalInfo = JSON.parse(field.additionalInformation);
          const needsFix = !additionalInfo.validations || 
                          (typeof additionalInfo.validations === 'object' && 
                           !additionalInfo.validations.hasOwnProperty('required'));
          
          if (needsFix) {
            console.log(`📝 Collection field ${field.id} (${additionalInfo.name || 'Unnamed'}) needs fixing`);
            collectionFieldsToFix++;
          }
        } catch (error) {
          console.error(`❌ Error parsing field ${field.id}:`, error);
        }
      }
    }
    
    // Check component fields
    const componentResponse = await componentFieldsApi.getAll();
    const componentFields: FieldData[] = componentResponse.data || [];
    
    let componentFieldsToFix = 0;
    
    for (const field of componentFields) {
      if (field.additionalInformation) {
        try {
          const additionalInfo: ParsedAdditionalInfo = JSON.parse(field.additionalInformation);
          const needsFix = !additionalInfo.validations || 
                          (typeof additionalInfo.validations === 'object' && 
                           !additionalInfo.validations.hasOwnProperty('required'));
          
          if (needsFix) {
            console.log(`📝 Component field ${field.id} (${additionalInfo.name || 'Unnamed'}) needs fixing`);
            componentFieldsToFix++;
          }
        } catch (error) {
          console.error(`❌ Error parsing field ${field.id}:`, error);
        }
      }
    }
    
    console.log(`📊 Preview Summary:`);
    console.log(`   Collection fields to fix: ${collectionFieldsToFix}`);
    console.log(`   Component fields to fix: ${componentFieldsToFix}`);
    console.log(`   Total fields to fix: ${collectionFieldsToFix + componentFieldsToFix}`);
    
  } catch (error) {
    console.error('❌ Error during preview:', error);
    throw error;
  }
}
