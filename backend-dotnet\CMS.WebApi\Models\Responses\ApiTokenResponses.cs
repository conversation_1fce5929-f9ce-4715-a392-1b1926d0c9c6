namespace CMS.WebApi.Models.Responses;

public class ApiTokenResponse
{
    public long Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public DateTime ExpiresAt { get; set; }
    public DateTime? LastUsedAt { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class CreateApiTokenResponse : ApiTokenResponse
{
    /// <summary>
    /// The actual token value - only returned on creation
    /// </summary>
    public string TokenValue { get; set; } = string.Empty;
}

public class SimplifiedCollectionResponse
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string ApiId { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
}
