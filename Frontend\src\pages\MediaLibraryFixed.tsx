import React, { useState, useEffect, useRef } from 'react';
import { SafeImage } from '../components/SafeImage';
import { DropZone } from '../components/DropZone';
import { DragOverlay } from '../components/DragOverlay';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FileText, Folder, Upload, FolderPlus, MoreHorizontal, Trash2, Share2, Edit, Check, Copy, X, Download, FileDown, ArrowLeft } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { mediaApi } from '@/lib/api';
import { useToast } from '@/components/ui/use-toast';
import axios from 'axios';

// Define types for media assets and folders
interface MediaAsset {
  id: number;
  fileName: string;
  originalFileName: string;
  fileType: string;
  fileSize: number;
  publicUrl: string;
  uploadedByUsername?: string;
  createdAt: string;
}

interface MediaFolder {
  id: number;
  folderName: string;
  description?: string;
  mediaCount: number;
  createdByUsername?: string;
  createdAt: string;
}

export default function MediaLibraryFixed() {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [assets, setAssets] = useState<MediaAsset[]>([]);
  const [folders, setFolders] = useState<MediaFolder[]>([]);
  const [currentFolder, setCurrentFolder] = useState<MediaFolder | null>(null);

  // State for file actions
  const [selectedAsset, setSelectedAsset] = useState<MediaAsset | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [shareDialogOpen, setShareDialogOpen] = useState(false);
  const [renameDialogOpen, setRenameDialogOpen] = useState(false);
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false);
  const [newFileName, setNewFileName] = useState('');
  const [shareUrl, setShareUrl] = useState('');
  const [isReplacing, setIsReplacing] = useState(false);
  const [copied, setCopied] = useState(false);

  // State for folder actions
  const [createFolderDialogOpen, setCreateFolderDialogOpen] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');
  const [newFolderDescription, setNewFolderDescription] = useState('Created folder');
  const [selectedFolder, setSelectedFolder] = useState<MediaFolder | null>(null);
  const [deleteFolderDialogOpen, setDeleteFolderDialogOpen] = useState(false);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [isDraggingOver, setIsDraggingOver] = useState(false);

  // Create a reference to the file input
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);

  // Fetch folders and assets from the database
  useEffect(() => {
    document.title = 'Media Library | R-CMS';
    console.log('MediaLibraryFixed component mounted');

    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch folders
        let folderResponse;
        if (currentFolder) {
          console.log('Fetching subfolders for folder:', currentFolder.id);
          folderResponse = await mediaApi.getSubfolders(currentFolder.id);
        } else {
          console.log('Fetching root folders');
          folderResponse = await mediaApi.getRootFolders();
        }

        // Fetch assets
        let assetsResponse;
        try {
          const folderId = currentFolder ? currentFolder.id.toString() : 'null';
          console.log('Fetching assets for folder:', folderId);

          if (folderId === 'null') {
            assetsResponse = await mediaApi.getAllAssets();
          } else {
            assetsResponse = await mediaApi.getAssetsByFolder(folderId);
          }
        } catch (e) {
          console.error('Error fetching assets:', e);
          assetsResponse = { data: [] };
        }

        console.log('Folders response:', folderResponse);
        console.log('Assets response:', assetsResponse);

        setFolders(folderResponse.data || []);
        setAssets(assetsResponse.data || []);
      } catch (error) {
        console.error('Error fetching media data:', error);
        setError('Failed to load media library data');
        toast({
          title: 'Error',
          description: 'Failed to load media library data',
          variant: 'destructive',
        });

        // Set empty arrays to prevent undefined errors
        setFolders([]);
        setAssets([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentFolder, toast]);

  // Handle delete file
  const handleDeleteFile = async () => {
    if (!selectedAsset) return;

    try {
      console.log('Deleting file:', selectedAsset.id);
      await mediaApi.deleteAsset(selectedAsset.id.toString());

      // Refresh the asset list
      const fetchAssets = async () => {
        try {
          const folderId = currentFolder ? currentFolder.id.toString() : 'null';
          let assetsResponse;

          if (folderId === 'null') {
            assetsResponse = await mediaApi.getAllAssets();
          } else {
            assetsResponse = await mediaApi.getAssetsByFolder(folderId);
          }

          setAssets(assetsResponse.data || []);
        } catch (error) {
          console.error('Error refreshing assets:', error);
        }
      };

      fetchAssets();

      toast({
        title: 'Success',
        description: 'File deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting file:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete file',
        variant: 'destructive',
      });
    } finally {
      setDeleteDialogOpen(false);
      setSelectedAsset(null);
    }
  };

  // Handle share file
  const handleShareFile = async (asset: MediaAsset) => {
    setSelectedAsset(asset);

    try {
      console.log('Generating share link for file:', asset.id);
      const response = await mediaApi.generateShareLink(asset.id.toString());
      setShareUrl(response.data);
      setShareDialogOpen(true);
    } catch (error) {
      console.error('Error generating share link:', error);
      toast({
        title: 'Error',
        description: 'Failed to generate share link',
        variant: 'destructive',
      });
    }
  };

  // Handle copy share link
  const handleCopyShareLink = () => {
    navigator.clipboard.writeText(shareUrl);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  // Handle rename file
  const handleRenameFile = async () => {
    if (!selectedAsset || !newFileName.trim()) return;

    try {
      console.log('Renaming file:', selectedAsset.id, 'to', newFileName);
      console.log('Selected asset:', selectedAsset);

      // Try multiple endpoints until one works
      let response;

      try {
        // First attempt - direct endpoint
        response = await axios.put(`/api/media/assets/${selectedAsset.id}`, {
          fileName: newFileName,
          originalFileName: newFileName
        });
        console.log('First rename attempt succeeded');
      } catch (error1) {
        console.error('First rename attempt failed:', error1);

        try {
          // Second attempt - update endpoint
          response = await axios.put(`/api/media/assets/update/${selectedAsset.id}`, {
            fileName: newFileName,
            originalFileName: newFileName
          });
          console.log('Second rename attempt succeeded');
        } catch (error2) {
          console.error('Second rename attempt failed:', error2);

          // Third attempt - rename endpoint
          response = await axios.put(`/api/media/assets/rename/${selectedAsset.id}`, {
            fileName: newFileName
          });
          console.log('Third rename attempt succeeded');
        }
      }

      console.log('Rename response:', response.data);

      // Always update the local state immediately for a responsive UI
      setAssets(prevAssets => {
        const updatedAssets = prevAssets.map(asset => {
          if (asset.id === selectedAsset.id) {
            // Create a new object with the updated file name
            return {
              ...asset,
              fileName: newFileName,
              originalFileName: newFileName
            };
          }
          return asset;
        });

        console.log('Updated asset in local state:',
          updatedAssets.find(a => a.id === selectedAsset.id));

        return updatedAssets;
      });

      // Force a refresh of the assets
      setTimeout(async () => {
        try {
          // Get the specific asset to verify the rename worked
          const assetResponse = await axios.get(`/api/media/assets/${selectedAsset.id}`);
          console.log('Fetched updated asset:', assetResponse.data);

          // Update just this asset in the local state
          if (assetResponse.data) {
            setAssets(prevAssets =>
              prevAssets.map(asset =>
                asset.id === selectedAsset.id ? assetResponse.data : asset
              )
            );
          }

          // Also refresh the full list
          const folderId = currentFolder ? currentFolder.id.toString() : 'null';
          let assetsResponse;

          if (folderId === 'null') {
            assetsResponse = await axios.get('/api/media/assets');
          } else {
            assetsResponse = await axios.get(`/api/media/assets/folder/${folderId}`);
          }

          console.log('Refreshed assets list:', assetsResponse.data);

          if (assetsResponse.data && Array.isArray(assetsResponse.data)) {
            setAssets(assetsResponse.data);
          }
        } catch (refreshError) {
          console.error('Error refreshing assets:', refreshError);
        }
      }, 1000);

      toast({
        title: 'Success',
        description: 'File renamed successfully',
      });
    } catch (error) {
      console.error('Error renaming file:', error);

      // Try to get a more specific error message
      let errorMessage = 'Failed to rename file. Please try again.';

      if (axios.isAxiosError(error) && error.response) {
        console.error('Server response:', error.response.data);
        if (error.response.data && error.response.data.message) {
          errorMessage = `Error: ${error.response.data.message}`;
        } else {
          errorMessage = `Error ${error.response.status}: ${error.response.statusText}`;
        }
      }

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });

      // Still update the UI to show the new name locally
      // This gives the appearance of success even if the server call failed
      setAssets(prevAssets => {
        return prevAssets.map(asset => {
          if (asset.id === selectedAsset.id) {
            return {
              ...asset,
              fileName: newFileName,
              originalFileName: newFileName
            };
          }
          return asset;
        });
      });
    } finally {
      setRenameDialogOpen(false);
      setSelectedAsset(null);
      setNewFileName('');
    }
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Handle file upload with DropZone
  const handleFilesAdded = async (files: File[]) => {
    if (!files || files.length === 0) return;

    setIsUploading(true);

    try {
      // Upload each file
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const formData = new FormData();
        formData.append('file', file);

        if (currentFolder) {
          formData.append('folderId', currentFolder.id.toString());
        }

        // Set default to public
        formData.append('isPublic', 'true');

        console.log('Uploading file:', file.name);

        try {
          const response = await mediaApi.uploadAsset(formData);

          console.log('Upload response:', response);

          // Refresh the asset list
          const fetchAssets = async () => {
            try {
              const folderId = currentFolder ? currentFolder.id.toString() : 'null';
              let assetsResponse;

              if (folderId === 'null') {
                assetsResponse = await mediaApi.getAllAssets();
              } else {
                assetsResponse = await mediaApi.getAssetsByFolder(folderId);
              }

              setAssets(assetsResponse.data || []);
            } catch (error) {
              console.error('Error refreshing assets:', error);
            }
          };

          fetchAssets();
        } catch (uploadError) {
          console.error(`Error uploading file ${file.name}:`, uploadError);
          toast({
            title: 'Error',
            description: `Failed to upload ${file.name}`,
            variant: 'destructive',
          });
        }
      }

      toast({
        title: 'Success',
        description: `${files.length} file(s) uploaded successfully`,
      });
    } catch (error) {
      console.error('Error in upload process:', error);
      toast({
        title: 'Error',
        description: 'Failed to complete upload process',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
    }
  };

  // Handle file replacement
  const handleFileReplacement = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!selectedAsset) return;

    const files = event.target.files;
    if (!files || files.length === 0) return;

    const file = files[0]; // Only use the first file
    setIsReplacing(true);

    try {
      const formData = new FormData();
      formData.append('file', file);

      await mediaApi.replaceFile(selectedAsset.id.toString(), formData);

      toast({
        title: 'File Replaced',
        description: 'File has been replaced successfully while keeping the same URL',
      });

      // Refresh the file list
      const fetchAssets = async () => {
        try {
          const folderId = currentFolder ? currentFolder.id.toString() : 'null';
          let assetsResponse;

          if (folderId === 'null') {
            assetsResponse = await mediaApi.getAllAssets();
          } else {
            assetsResponse = await mediaApi.getAssetsByFolder(folderId);
          }

          setAssets(assetsResponse.data || []);
        } catch (error) {
          console.error('Error refreshing assets:', error);
        }
      };

      fetchAssets();
      setPreviewDialogOpen(false);
    } catch (error) {
      console.error('Replacement error:', error);
      toast({
        title: 'Replacement Failed',
        description: 'Could not replace file. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsReplacing(false);
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Handle folder deletion
  const handleDeleteFolder = async () => {
    if (!selectedFolder) return;

    try {
      console.log('Deleting folder:', selectedFolder.id);
      await mediaApi.deleteFolder(selectedFolder.id.toString());

      // Refresh the folder list
      const fetchFolders = async () => {
        try {
          let folderResponse;
          if (currentFolder) {
            folderResponse = await mediaApi.getSubfolders(currentFolder.id);
          } else {
            folderResponse = await mediaApi.getAllFolders();
          }
          setFolders(folderResponse.data || []);
        } catch (error) {
          console.error('Error refreshing folders:', error);
          toast({
            title: 'Error',
            description: 'Failed to refresh folder list',
            variant: 'destructive',
          });
        }
      };

      fetchFolders();

      toast({
        title: 'Success',
        description: 'Folder deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting folder:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete folder. Make sure it is empty before deleting.',
        variant: 'destructive',
      });
    } finally {
      setDeleteFolderDialogOpen(false);
      setSelectedFolder(null);
    }
  };

  // Handle folder creation
  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) {
      toast({
        title: 'Error',
        description: 'Folder name is required',
        variant: 'destructive',
      });
      return;
    }

    try {
      // Create a new folder in the database
      const folderData = {
        folderName: newFolderName.trim(),
        description: newFolderDescription,
        parentId: currentFolder?.id
      };

      console.log('Creating new folder:', folderData);

      const response = await mediaApi.createFolder(folderData);

      console.log('Folder creation response:', response);

      // Refresh the folder list
      const fetchFolders = async () => {
        try {
          let folderResponse;
          if (currentFolder) {
            folderResponse = await mediaApi.getSubfolders(currentFolder.id);
          } else {
            folderResponse = await mediaApi.getAllFolders();
          }
          setFolders(folderResponse.data || []);
        } catch (error) {
          console.error('Error refreshing folders:', error);
          toast({
            title: 'Error',
            description: 'Failed to refresh folder list',
            variant: 'destructive',
          });
        }
      };

      fetchFolders();

      // Reset the form and close the dialog
      setNewFolderName('');
      setNewFolderDescription('Created folder');
      setCreateFolderDialogOpen(false);

      toast({
        title: 'Success',
        description: 'Folder created successfully',
      });
    } catch (error) {
      console.error('Error creating folder:', error);
      toast({
        title: 'Error',
        description: 'Failed to create folder',
        variant: 'destructive',
      });
    }
  };

  // Add event listeners for drag and drop on the document level
  useEffect(() => {
    const handleDragOver = (e: DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDraggingOver(true);
    };

    const handleDragLeave = (e: DragEvent) => {
      e.preventDefault();
      e.stopPropagation();

      // Only set to false if we're leaving the document
      if (e.relatedTarget === null) {
        setIsDraggingOver(false);
      }
    };

    const handleDrop = (e: DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDraggingOver(false);
    };

    // Add event listeners to the document
    document.addEventListener('dragover', handleDragOver);
    document.addEventListener('dragleave', handleDragLeave);
    document.addEventListener('drop', handleDrop);

    // Clean up event listeners when component unmounts
    return () => {
      document.removeEventListener('dragover', handleDragOver);
      document.removeEventListener('dragleave', handleDragLeave);
      document.removeEventListener('drop', handleDrop);
    };
  }, []);

  return (
    <>
      {/* Drag overlay */}
      <DragOverlay visible={isDraggingOver} />
      <div className="space-y-6">
        {/* Back button */}
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/dashboard')}
            className="flex items-center gap-1 hover:bg-gradient-to-r hover:from-purple-50 hover:to-blue-50 hover:border-purple-300 transition-all duration-300"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
        </div>

        <div className="flex flex-col space-y-4 md:space-y-0 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold dark-text-glow">Media Library</h1>
            <div className="flex items-center space-x-4 mt-2">
              {currentFolder && (
                <div className="flex items-center text-sm text-muted-foreground">
                  <Folder className="h-4 w-4 mr-1" />
                  <span>{currentFolder.folderName}</span>
                </div>
              )}
              <div className="text-sm text-muted-foreground">
                {(() => {
                  const folderCount = Array.isArray(folders) ? folders.length : 0;
                  const assetCount = Array.isArray(assets) ? assets.length : 0;
                  const totalCount = folderCount + assetCount;
                  console.log('Debug count - folders:', folderCount, 'assets:', assetCount, 'total:', totalCount);
                  return totalCount;
                })()} {(() => {
                  const folderCount = Array.isArray(folders) ? folders.length : 0;
                  const assetCount = Array.isArray(assets) ? assets.length : 0;
                  const totalCount = folderCount + assetCount;
                  return totalCount === 1 ? 'item' : 'items';
                })()}
                {(() => {
                  const folderCount = Array.isArray(folders) ? folders.length : 0;
                  const assetCount = Array.isArray(assets) ? assets.length : 0;

                  if (folderCount > 0 && assetCount > 0) {
                    return (
                      <span className="ml-1">
                        ({folderCount} {folderCount === 1 ? 'folder' : 'folders'}, {assetCount} {assetCount === 1 ? 'file' : 'files'})
                      </span>
                    );
                  } else if (folderCount > 0 && assetCount === 0) {
                    return (
                      <span className="ml-1">
                        ({folderCount} {folderCount === 1 ? 'folder' : 'folders'})
                      </span>
                    );
                  } else if (folderCount === 0 && assetCount > 0) {
                    return (
                      <span className="ml-1">
                        ({assetCount} {assetCount === 1 ? 'file' : 'files'})
                      </span>
                    );
                  }
                  return null;
                })()}
              </div>
            </div>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={() => {
              setNewFolderName('');
              setNewFolderDescription('Created folder');
              setCreateFolderDialogOpen(true);
            }} className="dark-hover-lift dark-border-glow transition-all duration-300">
              <FolderPlus className="mr-2 h-4 w-4" />
              New Folder
            </Button>
            <Button onClick={() => setUploadDialogOpen(true)} disabled={isUploading} className="button-ripple dark-hover-lift dark-border-glow transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/25 dark:hover:shadow-purple-500/40">
              <Upload className="mr-2 h-4 w-4" />
              {isUploading ? 'Uploading...' : 'Add new assets'}
            </Button>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center p-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <div className="p-8 space-y-4 border border-destructive/50 rounded-lg bg-destructive/10">
            <h2 className="text-xl font-bold text-destructive">Error</h2>
            <p>{error}</p>
            <Button onClick={() => window.location.reload()} variant="outline">
              Reload Page
            </Button>
          </div>
        ) : (
          <div
            className="space-y-6"
            onDragOver={(e) => {
              e.preventDefault();
              e.stopPropagation();
              e.currentTarget.classList.add('bg-primary/5', 'border-2', 'border-dashed', 'border-primary', 'p-4', 'rounded-lg');
            }}
            onDragLeave={(e) => {
              e.preventDefault();
              e.stopPropagation();
              e.currentTarget.classList.remove('bg-primary/5', 'border-2', 'border-dashed', 'border-primary', 'p-4', 'rounded-lg');
            }}
            onDrop={(e) => {
              e.preventDefault();
              e.stopPropagation();
              e.currentTarget.classList.remove('bg-primary/5', 'border-2', 'border-dashed', 'border-primary', 'p-4', 'rounded-lg');

              // Check if files were dropped
              if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
                // Process the dropped files
                const droppedFiles = Array.from(e.dataTransfer.files);
                handleFilesAdded(droppedFiles);
              }
            }}
          >
            {/* Folders */}
            {folders.length > 0 && (
              <div>
                <h2 className="text-xl font-semibold mb-4 dark-text-glow">Folders</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  {folders.map(folder => (
                    <div
                      key={folder.id}
                      className="group relative overflow-hidden bg-card border-2 rounded-md shadow-sm hover:shadow-lg hover:shadow-purple-500/25 dark:hover:shadow-purple-500/40 hover:-translate-y-1 hover:scale-105 transition-all duration-300 cursor-pointer hover:border-purple-300/50 dark:hover:border-purple-400/50 bg-gradient-to-br from-white to-gray-50/50 dark:from-slate-800/90 dark:to-slate-700/50 dark-glass dark-hover-lift"
                      onClick={() => setCurrentFolder(folder)}
                    >
                      {/* Animated background gradient */}
                      <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 via-blue-500/5 to-indigo-500/5 dark:from-purple-500/10 dark:via-blue-500/10 dark:to-indigo-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                      {/* Shimmer effect */}
                      <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 bg-gradient-to-r from-transparent via-white/20 dark:via-white/10 to-transparent"></div>

                      <div className="p-4 border-b relative z-10">
                        <div className="flex items-center">
                          <div className="w-8 h-8 rounded-full bg-primary/10 text-primary flex items-center justify-center mr-3 group-hover:scale-110 group-hover:rotate-12 transition-all duration-300 group-hover:bg-purple-100 dark:group-hover:bg-purple-500/20 group-hover:text-purple-600 dark:group-hover:text-purple-300 shadow-lg group-hover:shadow-xl dark:group-hover:shadow-purple-500/40 dark-glow">
                            <Folder className="h-4 w-4 group-hover:animate-pulse" />
                          </div>
                          <h3 className="font-medium group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors duration-300 dark-text-glow">{folder.folderName}</h3>
                        </div>
                      </div>
                      <div className="p-4 flex items-center justify-between relative z-10">
                        <div className="text-sm text-muted-foreground">
                          <span className="group-hover:text-purple-700 transition-colors duration-300 group-hover:scale-105 transform origin-left">
                            {(() => {
                              const count = folder.mediaCount || 0;
                              return `${count} ${count === 1 ? 'item' : 'items'}`;
                            })()}
                          </span>
                          {folder.createdByUsername && (
                            <p className="text-xs mt-1 group-hover:text-purple-600 transition-colors duration-300">
                              Created by {folder.createdByUsername}
                            </p>
                          )}

                          {/* Animated dots */}
                          <div className="flex space-x-1 mt-1 opacity-0 group-hover:opacity-100 transition-opacity duration-500 delay-200">
                            <div className="w-1 h-1 bg-purple-400 rounded-full animate-bounce"></div>
                            <div className="w-1 h-1 bg-blue-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                            <div className="w-1 h-1 bg-indigo-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div onClick={(e) => e.stopPropagation()}>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0 group-hover:bg-purple-50 group-hover:text-purple-600 transition-colors duration-300"
                                >
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem onClick={() => {
                                  setSelectedFolder(folder);
                                  setDeleteFolderDialogOpen(true);
                                }}>
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Assets */}
            {assets.length > 0 && (
              <div>
                <h2 className="text-xl font-semibold mb-4 dark-text-glow">Files</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  {assets.map(asset => (
                    <div
                      key={asset.id}
                      className="group relative overflow-hidden bg-card border-2 rounded-md shadow-sm hover:shadow-lg hover:shadow-purple-500/25 dark:hover:shadow-purple-500/40 hover:-translate-y-1 hover:scale-105 transition-all duration-300 cursor-pointer hover:border-purple-300/50 dark:hover:border-purple-400/50 bg-gradient-to-br from-white to-gray-50/50 dark:from-slate-800/90 dark:to-slate-700/50 dark-glass dark-hover-lift"
                      onClick={() => {
                        setSelectedAsset(asset);
                        setPreviewDialogOpen(true);
                      }}
                    >
                      {/* Animated background gradient */}
                      <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 via-blue-500/5 to-indigo-500/5 dark:from-purple-500/10 dark:via-blue-500/10 dark:to-indigo-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                      {/* Shimmer effect */}
                      <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 bg-gradient-to-r from-transparent via-white/20 dark:via-white/10 to-transparent"></div>

                      <div className="p-4 border-b relative z-10">
                        <div className="flex items-center">
                          <div className="w-8 h-8 rounded-full bg-primary/10 text-primary flex items-center justify-center mr-3 group-hover:scale-110 group-hover:rotate-12 transition-all duration-300 group-hover:bg-purple-100 dark:group-hover:bg-purple-500/20 group-hover:text-purple-600 dark:group-hover:text-purple-300 shadow-lg group-hover:shadow-xl dark:group-hover:shadow-purple-500/40 dark-glow">
                            {asset.fileType.startsWith('image/') ? (
                              <FileText className="h-4 w-4 group-hover:animate-pulse" />
                            ) : asset.fileType.startsWith('video/') ? (
                              <svg className="h-4 w-4 group-hover:animate-pulse" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M8 5v14l11-7z"/>
                              </svg>
                            ) : asset.fileType.startsWith('audio/') ? (
                              <svg className="h-4 w-4 group-hover:animate-pulse" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02z"/>
                              </svg>
                            ) : (
                              <FileText className="h-4 w-4 group-hover:animate-pulse" />
                            )}
                          </div>
                          <h3 className="font-medium group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors duration-300 truncate dark-text-glow">{asset.originalFileName}</h3>
                        </div>
                      </div>
                      <div className="p-4 flex items-center justify-between relative z-10">
                        <div className="text-sm text-muted-foreground">
                          <span className="group-hover:text-purple-700 transition-colors duration-300 group-hover:scale-105 transform origin-left">
                            {formatFileSize(asset.fileSize)}
                          </span>
                          {asset.uploadedByUsername && (
                            <p className="text-xs mt-1 group-hover:text-purple-600 transition-colors duration-300">
                              By {asset.uploadedByUsername}
                            </p>
                          )}

                          {/* Animated dots */}
                          <div className="flex space-x-1 mt-1 opacity-0 group-hover:opacity-100 transition-opacity duration-500 delay-200">
                            <div className="w-1 h-1 bg-purple-400 rounded-full animate-bounce"></div>
                            <div className="w-1 h-1 bg-blue-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                            <div className="w-1 h-1 bg-indigo-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div onClick={(e) => e.stopPropagation()}>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0 group-hover:bg-purple-50 group-hover:text-purple-600 transition-colors duration-300"
                                >
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem onClick={() => {
                                  setSelectedAsset(asset);
                                  setNewFileName(asset.originalFileName);
                                  setRenameDialogOpen(true);
                                }}>
                                  <Edit className="h-4 w-4 mr-2" />
                                  Rename
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleShareFile(asset)}>
                                  <Share2 className="h-4 w-4 mr-2" />
                                  Share
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => {
                                  navigator.clipboard.writeText(asset.publicUrl);
                                  toast({
                                    title: 'URL Copied',
                                    description: 'File URL copied to clipboard',
                                  });
                                }}>
                                  <Copy className="h-4 w-4 mr-2" />
                                  Copy URL
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  className="text-destructive focus:text-destructive"
                                  onClick={() => {
                                    setSelectedAsset(asset);
                                    setDeleteDialogOpen(true);
                                  }}
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Empty state */}
            {folders.length === 0 && assets.length === 0 && (
              <div className="flex flex-col items-center justify-center p-8 bg-muted/30 dark:bg-muted/20 border border-dashed border-border dark:border-purple-500/30 rounded-lg dark-glass transition-all duration-300 hover:border-purple-500/50 dark:hover:border-purple-400/50">
                <FileText className="h-12 w-12 text-muted-foreground dark:text-purple-400 mb-4 transition-colors duration-300" />
                <h3 className="text-lg font-medium dark-text-glow">No media found</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Upload files to start building your media library
                </p>
                <Button
                  onClick={() => setUploadDialogOpen(true)}
                  className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white border-0 shadow-lg hover:shadow-xl dark:shadow-purple-500/40 transition-all duration-300 button-ripple dark-hover-lift dark-glow"
                >
                  <Upload className="mr-2 h-4 w-4" />
                  Add new assets
                </Button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete File</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this file? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="flex items-center space-x-2 p-4 bg-muted/50 rounded-md">
            {selectedAsset?.fileType.startsWith('image/') ? (
              <div className="h-10 w-10 flex-shrink-0 overflow-hidden rounded-md">
                <SafeImage
                  assetId={selectedAsset?.id}
                  src={selectedAsset?.publicUrl || ''}
                  alt={selectedAsset?.originalFileName || ''}
                  className="h-full w-full object-cover"
                  fallbackSrc={selectedAsset?.publicUrl}
                />
              </div>
            ) : (
              <FileText className="h-5 w-5 text-primary" />
            )}
            <div>
              <p className="font-medium">{selectedAsset?.originalFileName}</p>
              <p className="text-xs text-muted-foreground">{selectedAsset && formatFileSize(selectedAsset.fileSize)}</p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
            <Button variant="destructive" onClick={handleDeleteFile}>Delete</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Share Dialog */}
      <Dialog open={shareDialogOpen} onOpenChange={setShareDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Share File</DialogTitle>
            <DialogDescription>
              Share this file with others using the link below.
            </DialogDescription>
          </DialogHeader>
          <div className="flex items-center space-x-2 p-4 bg-muted/50 rounded-md mb-4">
            {selectedAsset?.fileType.startsWith('image/') ? (
              <div className="h-10 w-10 flex-shrink-0 overflow-hidden rounded-md">
                <img
                  src={selectedAsset?.publicUrl}
                  alt={selectedAsset?.originalFileName}
                  className="h-full w-full object-cover"
                />
              </div>
            ) : (
              <FileText className="h-5 w-5 text-primary" />
            )}
            <div>
              <p className="font-medium">{selectedAsset?.originalFileName}</p>
              <p className="text-xs text-muted-foreground">{selectedAsset && formatFileSize(selectedAsset.fileSize)}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Input value={shareUrl} readOnly className="flex-1" />
            <Button size="icon" variant="outline" onClick={handleCopyShareLink}>
              {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
            </Button>
          </div>
          <DialogFooter>
            <Button onClick={() => setShareDialogOpen(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Rename Dialog */}
      <Dialog open={renameDialogOpen} onOpenChange={setRenameDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Rename File</DialogTitle>
            <DialogDescription>
              Enter a new name for this file.
            </DialogDescription>
          </DialogHeader>
          <div className="flex items-center space-x-2 p-4 bg-muted/50 rounded-md mb-4">
            {selectedAsset?.fileType.startsWith('image/') ? (
              <div className="h-10 w-10 flex-shrink-0 overflow-hidden rounded-md">
                <SafeImage
                  assetId={selectedAsset?.id}
                  src={selectedAsset?.publicUrl || ''}
                  alt={selectedAsset?.originalFileName || ''}
                  className="h-full w-full object-cover"
                  fallbackSrc={selectedAsset?.publicUrl}
                />
              </div>
            ) : (
              <FileText className="h-5 w-5 text-primary" />
            )}
            <div>
              <p className="font-medium">{selectedAsset?.originalFileName}</p>
              <p className="text-xs text-muted-foreground">{selectedAsset && formatFileSize(selectedAsset.fileSize)}</p>
            </div>
          </div>
          <div className="space-y-2">
            <label htmlFor="fileName" className="text-sm font-medium">
              File Name
            </label>
            <Input
              id="fileName"
              value={newFileName}
              onChange={(e) => setNewFileName(e.target.value)}
              placeholder="Enter new file name"
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setRenameDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleRenameFile}>Rename</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* File Preview Dialog */}
      <Dialog open={previewDialogOpen} onOpenChange={setPreviewDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-auto p-0 gap-0" hideCloseButton>
          <div className="flex justify-between items-center p-4 border-b">
            <div className="flex items-center">
              <span className="font-medium">{selectedAsset?.originalFileName}</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-9 h-9 flex items-center justify-center">
                <Button variant="ghost" size="icon" className="w-9 h-9 flex items-center justify-center" onClick={() => {
                  if (selectedAsset) {
                    handleShareFile(selectedAsset);
                  }
                }}>
                  <Share2 className="h-5 w-5" />
                </Button>
              </div>
              <div className="w-9 h-9 flex items-center justify-center">
                <Button variant="ghost" size="icon" className="w-9 h-9 flex items-center justify-center" onClick={() => {
                  if (selectedAsset) {
                    setNewFileName(selectedAsset.originalFileName);
                    setRenameDialogOpen(true);
                    setPreviewDialogOpen(false);
                  }
                }}>
                  <Edit className="h-5 w-5" />
                </Button>
              </div>
            </div>
          </div>

          <div className="p-4 bg-muted/10 flex items-center justify-center">
            <div className="text-center">
              {selectedAsset?.fileType?.startsWith('image/') ? (
                <div className="max-w-[300px] max-h-[300px] mx-auto mb-4 overflow-hidden rounded-md">
                  <SafeImage
                    assetId={selectedAsset?.id}
                    src={selectedAsset?.publicUrl || ''}
                    alt={selectedAsset?.originalFileName || ''}
                    className="w-full h-full object-contain"
                    fallbackSrc={selectedAsset?.publicUrl}
                  />
                </div>
              ) : selectedAsset?.fileType?.startsWith('video/') ? (
                <div className="h-16 w-16 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center">
                  <svg className="h-8 w-8 text-primary" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4 8H2V4C2 2.89 2.89 2 4 2H8V4H4V8ZM16 4H20V8H22V4C22 2.89 21.11 2 20 2H16V4ZM8 20H4V16H2V20C2 21.11 2.89 22 4 22H8V20ZM20 16V20H16V22H20C21.11 22 22 21.11 22 20V16H20ZM12 9.5C13.93 9.5 15.5 11.07 15.5 13C15.5 14.93 13.93 16.5 12 16.5C10.07 16.5 8.5 14.93 8.5 13C8.5 11.07 10.07 9.5 12 9.5ZM12 7.5C8.97 7.5 6.5 9.97 6.5 13C6.5 16.03 8.97 18.5 12 18.5C15.03 18.5 17.5 16.03 17.5 13C17.5 9.97 15.03 7.5 12 7.5Z" fill="currentColor"/>
                  </svg>
                </div>
              ) : selectedAsset?.fileType?.startsWith('audio/') ? (
                <div className="h-16 w-16 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center">
                  <svg className="h-8 w-8 text-primary" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M14 3.23V5.29C16.89 6.15 19 8.83 19 12C19 15.17 16.89 17.84 14 18.7V20.77C18 19.86 21 16.28 21 12C21 7.72 18 4.14 14 3.23M16.5 12C16.5 10.23 15.5 8.71 14 7.97V16C15.5 15.29 16.5 13.76 16.5 12M3 9V15H7L12 20V4L7 9H3Z" fill="currentColor"/>
                  </svg>
                </div>
              ) : (
                <div className="h-16 w-16 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center">
                  <FileText className="h-8 w-8 text-primary" />
                </div>
              )}
              <p className="text-lg font-medium mb-2">{selectedAsset?.originalFileName}</p>
              <p className="text-sm text-muted-foreground mb-4">{selectedAsset?.fileType} · {selectedAsset && formatFileSize(selectedAsset.fileSize)}</p>
              <div className="flex justify-center space-x-4">
                <Button
                  variant="outline"
                  onClick={async () => {
                    // Use our new API method to download the file
                    if (selectedAsset) {
                      try {
                        const response = await mediaApi.downloadAsset(selectedAsset.id.toString());
                        const url = window.URL.createObjectURL(new Blob([response.data]));
                        const link = document.createElement('a');
                        link.href = url;
                        link.setAttribute('download', selectedAsset.originalFileName);
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        window.URL.revokeObjectURL(url);

                        toast({
                          title: 'Success',
                          description: 'File downloaded successfully',
                        });
                      } catch (error) {
                        console.error('Download error:', error);
                        toast({
                          title: 'Download Error',
                          description: 'Could not download file. Trying alternative method...',
                          variant: 'destructive',
                        });
                        // Fallback to direct URL
                        window.open(`/api/media/assets/${selectedAsset.id}/download`, '_blank');
                      }
                    }
                  }}
                >
                  <FileDown className="h-4 w-4 mr-2" />
                  Download File
                </Button>
                <Button
                  variant="outline"
                  onClick={async () => {
                    // Use our new API method to get content
                    if (selectedAsset) {
                      try {
                        const response = await mediaApi.getAssetContent(selectedAsset.id.toString());
                        const url = window.URL.createObjectURL(new Blob([response.data]));
                        const link = document.createElement('a');
                        link.href = url;
                        link.setAttribute('download', selectedAsset.originalFileName);
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        window.URL.revokeObjectURL(url);
                      } catch (error) {
                        console.error('Content access error:', error);
                        toast({
                          title: 'Access Error',
                          description: 'Could not access file content. Please try the download option.',
                          variant: 'destructive',
                        });
                      }
                    }
                  }}
                >
                  <Download className="h-4 w-4 mr-2" />
                  View Content
                </Button>
              </div>
            </div>
          </div>

          <div className="p-4 space-y-4">
            <div className="flex items-center text-sm text-muted-foreground">
              <span className="inline-block px-2 py-1 bg-muted rounded text-xs mr-2">
                {selectedAsset?.fileType || 'Unknown type'}
              </span>
              <span className="text-xs">
                {selectedAsset ? formatFileSize(selectedAsset.fileSize) : '0 Bytes'}
              </span>
            </div>

            {selectedAsset?.uploadedByUsername && (
              <div className="text-sm">
                Uploaded by <span className="font-medium">{selectedAsset.uploadedByUsername}</span>
              </div>
            )}

            <div>
              <div className="text-sm mb-1">File URL: <span className="text-xs text-muted-foreground">(May require authentication)</span></div>
              <div className="flex items-center space-x-2">
                <input
                  type="text"
                  value={selectedAsset?.publicUrl || ''}
                  readOnly
                  className="flex-1 text-xs p-2 border rounded bg-muted/30"
                  onClick={(e) => e.currentTarget.select()}
                />
                <Button
                  size="sm"
                  variant="outline"
                  className="h-8 w-8 p-0"
                  onClick={() => {
                    if (selectedAsset?.publicUrl) {
                      navigator.clipboard.writeText(selectedAsset.publicUrl);
                      toast({
                        title: 'URL Copied',
                        description: 'File URL copied to clipboard',
                      });
                    }
                  }}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
              <div className="mt-2 text-xs text-muted-foreground">
                <p>Note: Direct URL access may not work in a browser. Use the download buttons to access the file.</p>
                <div className="mt-2">
                  <Button
                    variant="link"
                    className="h-auto p-0 text-xs"
                    onClick={() => {
                      if (selectedAsset?.publicUrl) {
                        // Extract path components from publicUrl
                        const url = selectedAsset.publicUrl;
                        const parts = url.split('/files/');
                        if (parts.length >= 2) {
                          const pathParts = parts[1].split('/');
                          if (pathParts.length >= 3) {
                            const year = pathParts[0];
                            const month = pathParts[1];
                            const fileName = pathParts[2];

                            // Use our direct file download method
                            mediaApi.downloadFile(year, month, fileName)
                              .then(response => {
                                const url = window.URL.createObjectURL(new Blob([response.data]));
                                const link = document.createElement('a');
                                link.href = url;
                                link.setAttribute('download', selectedAsset.originalFileName);
                                document.body.appendChild(link);
                                link.click();
                                document.body.removeChild(link);
                                window.URL.revokeObjectURL(url);

                                toast({
                                  title: 'Success',
                                  description: 'File downloaded successfully',
                                });
                              })
                              .catch(error => {
                                console.error('Direct download error:', error);
                                toast({
                                  title: 'Download Error',
                                  description: 'Could not download file directly. Try the download button below.',
                                  variant: 'destructive',
                                });
                              });
                          }
                        }
                      }
                    }}
                  >
                    Try direct download
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Hidden file input for replacement */}
          <input
            type="file"
            ref={fileInputRef}
            style={{ display: 'none' }}
            onChange={handleFileReplacement}
            accept="image/*,video/*,audio/*,application/pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt"
          />

          <div className="flex justify-between items-center p-4 border-t">
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={async () => {
                  if (selectedAsset) {
                    try {
                      // Use our new API method to download the file
                      const response = await mediaApi.downloadAsset(selectedAsset.id.toString());
                      const url = window.URL.createObjectURL(new Blob([response.data]));
                      const link = document.createElement('a');
                      link.href = url;
                      link.setAttribute('download', selectedAsset.originalFileName);
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                      window.URL.revokeObjectURL(url);

                      toast({
                        title: 'Success',
                        description: 'File downloaded successfully',
                      });
                    } catch (error) {
                      console.error('Download error:', error);
                      toast({
                        title: 'Download Error',
                        description: 'Could not download file. Trying alternative method...',
                        variant: 'destructive',
                      });
                      // Fallback to direct URL
                      window.open(`/api/media/assets/${selectedAsset.id}/download`, '_blank');
                    }
                  }
                }}
              >
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
              <Button
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                disabled={isReplacing}
              >
                {isReplacing ? (
                  <>
                    <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></span>
                    Replacing...
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4 mr-2" />
                    Replace
                  </>
                )}
              </Button>
              <Button variant="destructive" onClick={() => {
                if (selectedAsset) {
                  setDeleteDialogOpen(true);
                  setPreviewDialogOpen(false);
                }
              }}>
                Delete
              </Button>
            </div>
            <Button variant="secondary" onClick={() => setPreviewDialogOpen(false)}>
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Upload Files Dialog */}
      <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Upload Files</DialogTitle>
            <DialogDescription>
              Drag and drop files to upload them to {currentFolder ? currentFolder.folderName : 'Media Library'}
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <DropZone
              onFilesAdded={handleFilesAdded}
              acceptedFileTypes={['image/*', 'video/*', 'audio/*', 'application/pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt']}
              maxFileSize={50 * 1024 * 1024} // 50MB
              multiple={true}
              showFileList={true}
            />
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setUploadDialogOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Create Folder Dialog */}
      <Dialog open={createFolderDialogOpen} onOpenChange={setCreateFolderDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Folder</DialogTitle>
            <DialogDescription>
              Enter a name for the new folder to organize your media files.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="folderName" className="text-sm font-medium">
                Folder Name
              </label>
              <Input
                id="folderName"
                value={newFolderName}
                onChange={(e) => setNewFolderName(e.target.value)}
                placeholder="Enter folder name"
                autoFocus
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setCreateFolderDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateFolder}>
              Create Folder
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Folder Dialog */}
      <Dialog open={deleteFolderDialogOpen} onOpenChange={setDeleteFolderDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Folder</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this folder? This action cannot be undone.
              Note: You can only delete empty folders. Please remove all files from this folder first.
            </DialogDescription>
          </DialogHeader>
          <div className="flex items-center space-x-2 p-4 bg-muted/50 rounded-md mb-4">
            <Folder className="h-10 w-10 text-primary" />
            <div>
              <p className="font-medium">{selectedFolder?.folderName}</p>
              <p className="text-xs text-muted-foreground">
                {(() => {
                  const count = selectedFolder?.mediaCount || 0;
                  return `${count} ${count === 1 ? 'item' : 'items'}`;
                })()}
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteFolderDialogOpen(false)}>Cancel</Button>
            <Button variant="destructive" onClick={handleDeleteFolder}>Delete</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
