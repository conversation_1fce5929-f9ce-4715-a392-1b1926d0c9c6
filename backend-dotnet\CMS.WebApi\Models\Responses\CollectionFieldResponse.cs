using CMS.WebApi.Models.Entities;

namespace CMS.WebApi.Models.Responses;

public class CollectionFieldResponse
{
    public int Id { get; set; }
    public string? CreatedBy { get; set; }
    public DateTime CreatedAt { get; set; }
    public string? ModifiedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public int CollectionId { get; set; }
    public FieldType? FieldType { get; set; }
    public int? DisplayPreference { get; set; }
    public int? DependentOnId { get; set; }
    public string? AdditionalInformation { get; set; }
    public List<int>? ConfigIds { get; set; }

    public static CollectionFieldResponse FromEntity(CollectionField field)
    {
        return new CollectionFieldResponse
        {
            Id = field.Id,
            CreatedBy = field.CreatedBy,
            CreatedAt = field.CreatedAt,
            ModifiedBy = field.ModifiedBy,
            ModifiedAt = field.ModifiedAt,
            CollectionId = field.CollectionId,
            FieldType = field.FieldType,
            DisplayPreference = field.DisplayPreference,
            DependentOnId = field.DependentOnId,
            AdditionalInformation = field.AdditionalInformation,
            ConfigIds = field.Configs?.Select(c => c.Id).ToList()
        };
    }
}
