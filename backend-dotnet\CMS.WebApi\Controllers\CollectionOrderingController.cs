using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.Responses;
using CMS.WebApi.Models.Requests;
using CMS.WebApi.Services.Interfaces;
using CMS.WebApi.Security;

namespace CMS.WebApi.Controllers;

[ApiController]
[Route("api/collections/{collectionId}/ordering")]
[Authorize(Policy = AuthorizationPolicies.JwtOrApiToken)]
[Tags("Collection Ordering")]
public class CollectionOrderingController : ControllerBase
{
    private readonly ICollectionOrderingService _collectionOrderingService;
    private readonly ILogger<CollectionOrderingController> _logger;

    public CollectionOrderingController(
        ICollectionOrderingService collectionOrderingService,
        ILogger<CollectionOrderingController> logger)
    {
        _collectionOrderingService = collectionOrderingService;
        _logger = logger;
    }

    /// <summary>
    /// Get ordered collection items
    /// </summary>
    /// <param name="collectionId">Collection ID</param>
    /// <returns>Ordered components and fields for the collection</returns>
    [HttpGet]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<object>> GetOrderedCollectionItems(int collectionId)
    {
        try
        {
            var result = await _collectionOrderingService.GetOrderedCollectionItemsAsync(collectionId);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Collection not found: {CollectionId}", collectionId);
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = $"Collection with ID {collectionId} not found",
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get ordered collection items for collection: {CollectionId}", collectionId);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving collection items",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Reorder collection items
    /// </summary>
    /// <param name="collectionId">Collection ID</param>
    /// <param name="request">Reorder request with component and field IDs</param>
    /// <returns>Updated ordered components and fields</returns>
    [HttpPut]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<object>> ReorderCollectionItems(
        int collectionId,
        [FromBody] ReorderItemsRequest request)
    {
        try
        {
            var result = await _collectionOrderingService.ReorderCollectionItemsAsync(
                collectionId,
                request.ComponentIds ?? new List<int>(),
                request.FieldIds ?? new List<int>());

            _logger.LogInformation("Successfully reordered items for collection: {CollectionId}", collectionId);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Collection not found: {CollectionId}", collectionId);
            return NotFound(new ErrorResponse
            {
                Status = 404,
                Error = "Not Found",
                Message = $"Collection with ID {collectionId} not found",
                Path = Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to reorder collection items for collection: {CollectionId}", collectionId);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while reordering collection items",
                Path = Request.Path
            });
        }
    }
}
