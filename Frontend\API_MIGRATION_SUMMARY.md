# Frontend API Migration Summary

## Overview
This document summarizes the changes made to migrate the frontend from Spring Boot APIs to .NET 8 Web API endpoints.

## Base URL Changes

### Before (Spring Boot):
- Development: `http://localhost:8080`
- Production: `${window.location.origin}/api`

### After (.NET Web API):
- Development: `http://localhost:5000/api` (HTTP) or `https://localhost:7000/api` (HTTPS)
- Production: `${window.location.origin}/api`

## API Endpoint Changes

### Authentication APIs
- ✅ **No changes required** - endpoints remain the same:
  - `POST /api/auth/login`
  - `POST /api/auth/register`
  - `POST /api/auth/logout`

### Collections APIs
| Spring Boot | .NET Web API | Status |
|-------------|--------------|--------|
| `/collections/getAll` | `/collections` | ✅ Updated |
| `/collections/getById/{id}` | `/collections/{id}` | ✅ Updated |
| `/collections/getByApiId/{apiId}` | `/collections/api/{apiId}` | ✅ Updated |
| `/collections/create` | `/collections` | ✅ Updated |
| `/collections/update/{id}` | `/collections/{id}` | ✅ Updated |
| `/collections/deleteById/{id}` | `/collections/{id}` | ✅ Updated |

### Components APIs
| Spring Boot | .NET Web API | Status |
|-------------|--------------|--------|
| `/components/getAll` | `/components` | ✅ Updated |
| `/components/getAllActive` | `/components/active` | ✅ Updated |
| `/components/getById/{id}` | `/components/{id}` | ✅ Updated |
| `/components/getByApiId/{apiId}` | `/components/api/{apiId}` | ✅ Updated |
| `/components/create` | `/components` | ✅ Updated |
| `/components/update/{id}` | `/components/{id}` | ✅ Updated |
| `/components/deleteById/{id}` | `/components/{id}` | ✅ Updated |

### Field Types APIs
| Spring Boot | .NET Web API | Status |
|-------------|--------------|--------|
| `/field-types/getAll` | `/field-types` | ✅ Updated |
| `/field-types/getAllActive` | `/field-types/active` | ✅ Updated |
| `/field-types/getById/{id}` | `/field-types/{id}` | ✅ Updated |
| **NEW** | `/field-types` (POST) | ✅ Added |
| **NEW** | `/field-types/{id}` (PUT) | ✅ Added |
| **NEW** | `/field-types/{id}` (DELETE) | ✅ Added |

### Field Configs APIs
| Spring Boot | .NET Web API | Status |
|-------------|--------------|--------|
| `/field-configs/getAll` | `/field-configs` | ✅ Updated |
| `/field-configs/getAllActive` | `/field-configs/active` | ✅ Updated |
| `/field-configs/getByFieldTypeId/{id}` | `/field-configs/by-field-type/{id}` | ✅ Updated |
| `/field-configs/getById/{id}` | `/field-configs/{id}` | ✅ Updated |
| `/field-configs/create` | `/field-configs` | ✅ Updated |
| `/field-configs/update/{id}` | `/field-configs/{id}` | ✅ Updated |
| **NEW** | `/field-configs/{id}` (DELETE) | ✅ Added |

### Config Types APIs (NEW)
| Endpoint | Method | Status |
|----------|--------|--------|
| `/config-types` | GET | ✅ Added |
| `/config-types/active` | GET | ✅ Added |
| `/config-types/{id}` | GET | ✅ Added |
| `/config-types` | POST | ✅ Added |
| `/config-types/{id}` | PUT | ✅ Added |
| `/config-types/{id}` | DELETE | ✅ Added |

### Content Entries APIs
| Spring Boot | .NET Web API | Status |
|-------------|--------------|--------|
| `/content-entries/getAll` | `/content-entries` | ✅ Updated |
| `/content-entries/getById/{id}` | `/content-entries/{id}` | ✅ Updated |
| `/content-entries/getByCollectionId/{id}` | `/content-entries/collection/{id}` | ✅ Updated |
| `/content-entries/create` | `/content-entries` | ✅ Updated |
| `/content-entries/update/{id}` | `/content-entries/{id}` | ✅ Updated |
| `/content-entries/deleteById/{id}` | `/content-entries/{id}` | ✅ Updated |
| **NEW** | `/content-entries/search` | ✅ Added |

### Categories APIs
| Spring Boot | .NET Web API | Status |
|-------------|--------------|--------|
| `/categories/getAll` | `/categories` | ✅ Updated |
| `/categories/getById/{id}` | `/categories/{id}` | ✅ Updated |
| `/categories/getByName/{name}` | `/categories/name/{name}` | ✅ Updated |
| `/categories/create` | `/categories` | ✅ Updated |
| `/categories/update/{id}` | `/categories/{id}` | ✅ Updated |
| `/categories/deleteById/{id}` | `/categories/{id}` | ✅ Updated |
| `/categories/getByParentId/{id}` | `/categories/parent/{id}` | ✅ Updated |
| `/categories/getHierarchical` | `/categories/hierarchical` | ✅ Updated |

### Media APIs
| Spring Boot | .NET Web API | Status |
|-------------|--------------|--------|
| `/media/assets/getAll` | `/media` | ✅ Updated |
| `/media/assets/getById/{id}` | `/media/{id}` | ✅ Updated |
| `/media/assets/getByFolderId/{id}` | `/media/folder/{id}` | ✅ Updated |
| `/media/assets/update/{id}` | `/media/{id}` | ✅ Updated |
| `/media/assets/deleteById/{id}` | `/media/{id}` | ✅ Updated |

### Media Folders APIs
| Spring Boot | .NET Web API | Status |
|-------------|--------------|--------|
| `/media/folders/getAll` | `/media-folders` | ✅ Updated |
| `/media/folders/getById/{id}` | `/media-folders/{id}` | ✅ Updated |
| `/media/folders/getSubfolders/{id}` | `/media-folders/by-parent/{id}` | ✅ Updated |
| `/media/folders/create` | `/media-folders` | ✅ Updated |
| `/media/folders/update/{id}` | `/media-folders/{id}` | ✅ Updated |
| `/media/folders/deleteById/{id}` | `/media-folders/{id}` | ✅ Updated |

### Public APIs (NEW)
| Endpoint | Method | Status |
|----------|--------|--------|
| `/public/collections/getAll` | GET | ✅ Added |
| `/public/collections/{id}` | GET | ✅ Added |
| `/public/collections/api/{apiId}` | GET | ✅ Added |
| `/public/collections/{id}/entries` | GET | ✅ Added |
| `/public/content-entries/{id}` | GET | ✅ Added |
| `/public/search` | GET | ✅ Added |

### Simplified Collections APIs (NEW)
| Endpoint | Method | Status |
|----------|--------|--------|
| `/simplified-collections/getAll` | GET | ✅ Added |
| `/simplified-collections/{id}` | GET | ✅ Added |
| `/simplified-collections/api/{apiId}` | GET | ✅ Added |
| `/simplified-collections/active` | GET | ✅ Added |

### API Tokens APIs (ENHANCED)
| Spring Boot | .NET Web API | Status |
|-------------|--------------|--------|
| `/api-tokens` | `/api-tokens` | ✅ Updated |
| **NEW** | `/api-tokens/{id}` (GET) | ✅ Added |
| `/api-tokens` (POST) | `/api-tokens` (POST) | ✅ Enhanced |
| **NEW** | `/api-tokens/{id}` (PUT) | ✅ Added |
| `/api-tokens/{id}` (DELETE) | `/api-tokens/{id}` (DELETE) | ✅ Updated |
| **NEW** | `/api-tokens/{id}/revoke` (POST) | ✅ Added |

## Key Changes Summary

### 1. **Endpoint Structure Standardization**
- Removed `/getAll`, `/getById`, `/create`, `/update`, `/deleteById` suffixes
- Adopted RESTful conventions (GET `/resource`, POST `/resource`, PUT `/resource/{id}`, DELETE `/resource/{id}`)

### 2. **New APIs Added**
- Config Types management
- Enhanced Field Types management (CRUD)
- Enhanced Field Configs management
- Public APIs for headless CMS
- Simplified Collections APIs
- Enhanced API Tokens management

### 3. **Base URL Changes**
- Development port changed from `8080` to `5000` (HTTP) or `7000` (HTTPS)
- All endpoints now use `/api` prefix consistently

### 4. **Enhanced Functionality**
- Search capabilities added to content entries and public APIs
- Active/inactive filtering for various resources
- Better API token management with revocation
- Hierarchical media folder support

## Testing Checklist

- ✅ Base URL configuration updated
- ✅ All API endpoint paths updated
- ✅ New API methods added
- ✅ Authentication flow maintained
- ✅ Error handling preserved
- ✅ Request/response interceptors maintained

## Next Steps

1. **Test the frontend** with the .NET Web API backend
2. **Update any hardcoded API calls** in components if found
3. **Test all CRUD operations** to ensure compatibility
4. **Verify authentication flow** works correctly
5. **Test media upload/download** functionality
6. **Validate public APIs** for headless CMS usage

## Notes

- All existing functionality should work seamlessly
- New APIs provide enhanced capabilities
- RESTful conventions improve API consistency
- Better error handling and logging in .NET backend
