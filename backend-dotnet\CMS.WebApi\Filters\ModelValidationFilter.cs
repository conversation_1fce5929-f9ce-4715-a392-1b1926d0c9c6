using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using CMS.WebApi.Models.Responses;

namespace CMS.WebApi.Filters;

/// <summary>
/// Action filter to handle model validation errors and return consistent error responses
/// </summary>
public class ModelValidationFilter : ActionFilterAttribute
{
    public override void OnActionExecuting(ActionExecutingContext context)
    {
        if (!context.ModelState.IsValid)
        {
            var validationErrors = context.ModelState
                .Where(x => x.Value?.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray() ?? Array.Empty<string>()
                );

            var errorResponse = new ErrorResponse
            {
                Status = 400,
                Error = "Validation Failed",
                Message = "One or more validation errors occurred.",
                Path = context.HttpContext.Request.Path,
                Timestamp = DateTime.UtcNow,
                TraceId = context.HttpContext.TraceIdentifier,
                ValidationErrors = validationErrors
            };

            context.Result = new BadRequestObjectResult(errorResponse);
        }

        base.OnActionExecuting(context);
    }
}
