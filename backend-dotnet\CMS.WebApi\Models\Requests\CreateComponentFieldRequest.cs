using System.ComponentModel.DataAnnotations;

namespace CMS.WebApi.Models.Requests;

public class CreateComponentFieldRequest
{
    [Required]
    public int ComponentId { get; set; }

    [Required]
    public int FieldTypeId { get; set; }

    public int? DisplayPreference { get; set; }

    public int? DependentOnId { get; set; }

    public string? AdditionalInformation { get; set; }

    /// <summary>
    /// Field configurations from Properties, Validations, and Attributes tabs
    /// </summary>
    public List<CreateFieldConfigRequest> Configurations { get; set; } = new List<CreateFieldConfigRequest>();
}

public class CreateFieldConfigRequest
{
    [Required]
    public int FieldConfigId { get; set; }

    [StringLength(255)]
    public string? ConfigValue { get; set; }

    public bool IsActive { get; set; } = true;
}

public class UpdateComponentFieldRequest
{
    public int? Id { get; set; }

    public int ComponentId { get; set; }

    public int FieldTypeId { get; set; }

    public int? DisplayPreference { get; set; }

    public int? DependentOnId { get; set; }

    public string? AdditionalInformation { get; set; }

    // Support for Java backend format
    public ComponentReference? Component { get; set; }
    public FieldTypeReference? FieldType { get; set; }

    // Support both "configs" and "configurations" property names
    public List<CreateFieldConfigRequest> Configs { get; set; } = new();

    public List<CreateFieldConfigRequest> Configurations
    {
        get => Configs;
        set => Configs = value ?? new();
    }
}
