using CMS.WebApi.Data;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace CMS.WebApi.Services.Implementations;

public class ConfigTypeService : IConfigTypeService
{
    private readonly CmsDbContext _context;
    private readonly ILogger<ConfigTypeService> _logger;

    public ConfigTypeService(CmsDbContext context, ILogger<ConfigTypeService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<IEnumerable<ConfigType>> GetAllConfigTypesAsync()
    {
        return await _context.ConfigTypes
            .OrderBy(ct => ct.ConfigTypeName)
            .ToListAsync();
    }

    public async Task<IEnumerable<ConfigType>> GetActiveConfigTypesAsync()
    {
        return await _context.ConfigTypes
            .Where(ct => ct.IsActive)
            .OrderBy(ct => ct.ConfigTypeName)
            .ToListAsync();
    }

    public async Task<ConfigType?> GetConfigTypeByIdAsync(int id)
    {
        return await _context.ConfigTypes.FindAsync(id);
    }

    public async Task<ConfigType?> GetConfigTypeByNameAsync(string configTypeName)
    {
        return await _context.ConfigTypes
            .FirstOrDefaultAsync(ct => ct.ConfigTypeName == configTypeName);
    }

    public async Task<ConfigType> CreateConfigTypeAsync(ConfigType configType)
    {
        configType.CreatedAt = DateTime.UtcNow;
        configType.IsActive = configType.IsActive; // Keep the provided value or default

        _context.ConfigTypes.Add(configType);
        await _context.SaveChangesAsync();
        return configType;
    }

    public async Task<ConfigType> UpdateConfigTypeAsync(int id, ConfigType configType)
    {
        var existingConfigType = await _context.ConfigTypes.FindAsync(id);
        if (existingConfigType == null)
            throw new ArgumentException($"Config type with ID {id} not found");

        existingConfigType.ConfigTypeName = configType.ConfigTypeName;
        existingConfigType.ConfigTypeDesc = configType.ConfigTypeDesc;
        existingConfigType.DisplayName = configType.DisplayName;
        existingConfigType.AdditionalInfo = configType.AdditionalInfo;
        existingConfigType.DisclaimerText = configType.DisclaimerText;
        existingConfigType.PlaceholderText = configType.PlaceholderText;
        existingConfigType.IsActive = configType.IsActive;
        existingConfigType.ModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return existingConfigType;
    }

    public async Task DeleteConfigTypeAsync(int id)
    {
        var configType = await _context.ConfigTypes.FindAsync(id);
        if (configType != null)
        {
            _context.ConfigTypes.Remove(configType);
            await _context.SaveChangesAsync();
        }
    }

    public async Task<bool> ConfigTypeExistsAsync(string configTypeName)
    {
        return await _context.ConfigTypes
            .AnyAsync(ct => ct.ConfigTypeName == configTypeName);
    }
}
