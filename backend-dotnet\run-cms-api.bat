@echo off
echo ========================================
echo    CMS Web API - .NET 8 Runner
echo ========================================
echo.

:: Check if .NET is installed
echo [1/5] Checking .NET installation...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: .NET 8 SDK is not installed or not in PATH!
    echo.
    echo Please install .NET 8 SDK from:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    echo.
    echo After installation:
    echo 1. Restart your command prompt
    echo 2. Run this batch file again
    echo.
    pause
    exit /b 1
)

:: Display .NET version
for /f "tokens=*" %%i in ('dotnet --version') do set DOTNET_VERSION=%%i
echo .NET Version: %DOTNET_VERSION%
echo.

:: Navigate to project directory
echo [2/5] Navigating to project directory...
cd /d "%~dp0CMS.WebApi"
if %errorlevel% neq 0 (
    echo ERROR: Could not find CMS.WebApi directory!
    pause
    exit /b 1
)
echo Current directory: %CD%
echo.

:: Restore NuGet packages
echo [3/5] Restoring NuGet packages...
dotnet restore
if %errorlevel% neq 0 (
    echo ERROR: Failed to restore packages!
    pause
    exit /b 1
)
echo Packages restored successfully.
echo.

:: Build the project
echo [4/5] Building the project...
dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo ERROR: Build failed! Please check the error messages above.
    pause
    exit /b 1
)
echo Build completed successfully.
echo.

:: Run the application
echo [5/5] Starting the CMS Web API...
echo.
echo ========================================
echo    Application Starting...
echo ========================================
echo.
echo The API will be available at:
echo - Swagger UI: http://localhost:5000/swagger
echo - Health Check: http://localhost:5000/api/health
echo - API Base: http://localhost:5000/api/
echo.
echo Press Ctrl+C to stop the application
echo ========================================
echo.

:: Start the application with detailed errors
set ASPNETCORE_ENVIRONMENT=Development
dotnet run --configuration Debug

echo.
echo Application stopped.
pause
