import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, Search, ChevronLeft, ChevronRight, Database, Edit, Trash2, MoreHorizontal } from 'lucide-react';
import { collectionsApi, collectionFieldsApi } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import BasicCollectionDialog from '@/components/content-type/BasicCollectionDialog';
import { PageSpinner } from '@/components/ui/spinner';

interface Collection {
  id: string;
  name: string;
  collectionName: string;
  apiId: string;
  collectionApiId: string;
  fields: any[];
  categoryId?: number;
  categoryName?: string;
}

export default function AllCollections() {
  const navigate = useNavigate();
  const { toast } = useToast();

  const [collections, setCollections] = useState<Collection[]>([]);
  const [filteredCollections, setFilteredCollections] = useState<Collection[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [createCollectionDialogOpen, setCreateCollectionDialogOpen] = useState(false);

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const itemsPerPage = 12;

  useEffect(() => {
    fetchAllCollections();
  }, []);

  useEffect(() => {
    // Filter collections based on search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      const filtered = collections.filter(collection =>
        (collection.name || collection.collectionName || '').toLowerCase().includes(query) ||
        (collection.apiId || '').toLowerCase().includes(query) ||
        (collection.categoryName || '').toLowerCase().includes(query)
      );
      setFilteredCollections(filtered);
    } else {
      setFilteredCollections(collections);
    }
    setCurrentPage(1);
  }, [searchQuery, collections]);

  useEffect(() => {
    // Update total pages when filtered collections change
    setTotalPages(Math.ceil(filteredCollections.length / itemsPerPage));
  }, [filteredCollections, itemsPerPage]);

  const fetchAllCollections = async () => {
    setLoading(true);
    try {
      const response = await collectionsApi.getAll();
      console.log('All collections response:', response.data);

      const collectionsData = Array.isArray(response.data) ? response.data : [];

      // Fetch field counts for each collection
      const collectionsWithFields = await Promise.all(
        collectionsData.map(async (collection: any) => {
          try {
            const fieldsResponse = await collectionFieldsApi.getByCollectionId(collection.id.toString());
            return {
              ...collection,
              fields: Array.isArray(fieldsResponse.data) ? fieldsResponse.data : []
            };
          } catch (error) {
            console.error(`Error fetching fields for collection ${collection.id}:`, error);
            return {
              ...collection,
              fields: []
            };
          }
        })
      );

      setCollections(collectionsWithFields);
      setFilteredCollections(collectionsWithFields);
    } catch (error) {
      console.error('Error fetching collections:', error);
      toast({
        title: 'Error',
        description: 'Failed to load collections',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEditCollection = (collectionId: string) => {
    navigate(`/content-types/edit/${collectionId}`);
  };

  const handleCreateCollection = () => {
    setCreateCollectionDialogOpen(true);
  };

  const handleCloseCollectionDialog = () => {
    setCreateCollectionDialogOpen(false);
  };

  const handleSaveCollection = async (collectionData: any) => {
    try {
      await fetchAllCollections(); // Refresh the list
      toast({
        title: 'Success',
        description: 'Collection created successfully',
      });
    } catch (error) {
      console.error('Error after creating collection:', error);
    }
  };

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const getCurrentPageItems = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredCollections.slice(startIndex, endIndex);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 md:space-y-0 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-2">
          <h1 className="text-3xl font-bold dark-text-glow">All Collections</h1>
          <Badge variant="outline" className="ml-2 dark:border-purple-500/30 dark-glass">
            {filteredCollections.length} {filteredCollections.length === 1 ? 'collection' : 'collections'}
          </Badge>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={handleCreateCollection} className="button-ripple dark-hover-lift dark-border-glow transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/25 dark:hover:shadow-purple-500/40">
            <Plus className="mr-2 h-4 w-4" />
            Create Collection
          </Button>
        </div>
      </div>

      {/* Search */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search collections..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8 dark-glass dark:border-purple-500/30 transition-all duration-300"
          />
        </div>
      </div>

      {/* Content */}
      {loading ? (
        <PageSpinner text="Loading collections..." />
      ) : filteredCollections.length === 0 ? (
        <div className="flex flex-col items-center justify-center p-8 bg-gray-50 dark:bg-muted/20 border border-dashed border-border dark:border-purple-500/30 rounded-lg dark-glass transition-all duration-300 hover:border-purple-500/50 dark:hover:border-purple-400/50">
          <Database className="h-12 w-12 text-muted-foreground dark:text-purple-400 mb-4 transition-colors duration-300" />
          <h3 className="text-lg font-medium dark-text-glow">
            {searchQuery ? 'No collections found' : 'No collections yet'}
          </h3>
          <p className="text-sm text-muted-foreground mb-4">
            {searchQuery
              ? `No collections match your search query "${searchQuery}"`
              : 'Create your first collection to start building your API'
            }
          </p>
          {searchQuery ? (
            <Button variant="outline" onClick={() => setSearchQuery('')} className="dark-hover-lift dark-border-glow transition-all duration-300">
              Clear search
            </Button>
          ) : (
            <Button onClick={handleCreateCollection} className="button-ripple dark-hover-lift dark-border-glow transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/25 dark:hover:shadow-purple-500/40">
              <Plus className="mr-2 h-4 w-4" />
              Create Collection
            </Button>
          )}
        </div>
      ) : (
        <>
          {/* Collections Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {getCurrentPageItems().map((collection) => (
              <Card
                key={collection.id}
                className="group relative overflow-hidden hover:shadow-lg hover:shadow-purple-500/25 dark:hover:shadow-purple-500/40 transition-all duration-300 hover:-translate-y-1 hover:scale-105 cursor-pointer border-2 hover:border-purple-300/50 dark:hover:border-purple-400/50 bg-gradient-to-br from-white to-gray-50/50 dark:from-card dark:to-card/80 dark-glass dark-hover-lift"
                onClick={() => handleEditCollection(collection.id)}
              >
                {/* Animated background gradient */}
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-pink-500/5 dark:from-blue-500/10 dark:via-purple-500/10 dark:to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                {/* Shimmer effect */}
                <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 bg-gradient-to-r from-transparent via-white/20 dark:via-purple-400/20 to-transparent"></div>

                <CardHeader className="pb-2 relative z-10">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 rounded-full bg-primary dark:bg-purple-500/80 text-primary-foreground flex items-center justify-center text-sm font-medium dark-glow transition-all duration-300 group-hover:scale-110 group-hover:rotate-12 group-hover:shadow-xl shadow-lg">
                        {(collection.name || collection.collectionName || 'U')[0].toUpperCase()}
                      </div>
                      <div className="flex-1 min-w-0">
                        <CardTitle className="text-sm font-medium truncate group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors duration-300 dark-text-glow">
                          {collection.name || collection.collectionName || 'Unnamed Collection'}
                        </CardTitle>
                      </div>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity dark-hover-lift hover:bg-purple-500/20">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          handleEditCollection(collection.id);
                        }}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardHeader>
                <CardContent className="relative z-10">
                  <div className="space-y-3">
                    {/* API ID - Enhanced Display */}
                    <div className="flex items-center gap-2 p-3 bg-muted/50 dark:bg-muted/40 rounded-md border border-border dark:border-purple-500/40 dark-glass transition-all duration-300 group-hover:border-purple-500/60 group-hover:bg-muted/60 dark:group-hover:bg-muted/50 group-hover:shadow-md">
                      <Database className="h-4 w-4 text-blue-600 dark:text-purple-400 transition-colors duration-300 group-hover:scale-110" />
                      <span className="text-sm font-mono text-foreground font-semibold dark-text-glow tracking-wide">
                        {collection.collectionApiId || collection.apiId || 'No API ID'}
                      </span>
                    </div>

                    {collection.categoryName && (
                      <p className="text-xs text-muted-foreground truncate">
                        Category: {collection.categoryName}
                      </p>
                    )}
                    <div className="flex items-center justify-between">
                      <Badge variant="outline" className="text-xs dark:border-purple-500/30 dark-glass">
                        {collection.fields?.length || 0} fields
                      </Badge>
                    </div>
                  </div>
                </CardContent>

                {/* Corner accent */}
                <div className="absolute top-0 right-0 w-0 h-0 border-l-[20px] border-l-transparent border-t-[20px] border-t-purple-500/20 dark:border-t-purple-400/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </Card>
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredCollections.length)} of {filteredCollections.length} collections
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">
                  Page {currentPage} of {totalPages}
                </span>
                <div className="flex items-center space-x-1">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="dark-hover-lift dark-border-glow transition-all duration-300"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="dark-hover-lift dark-border-glow transition-all duration-300"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </>
      )}

      {/* Create collection dialog */}
      <BasicCollectionDialog
        isOpen={createCollectionDialogOpen}
        onClose={handleCloseCollectionDialog}
        onSave={handleSaveCollection}
      />
    </div>
  );
}
