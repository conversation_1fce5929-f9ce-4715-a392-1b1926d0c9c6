using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Options;
using System.Security.Claims;
using System.Text.Encodings.Web;
using CMS.WebApi.Services.Interfaces;

namespace CMS.WebApi.Security;

public class JwtAuthenticationHandler : AuthenticationHandler<JwtBearerOptions>
{
    public JwtAuthenticationHandler(
        IOptionsMonitor<JwtBearerOptions> options,
        ILoggerFactory logger,
        UrlEncoder encoder)
        : base(options, logger, encoder)
    {
    }

    protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        try
        {
            // Resolve scoped services from the request scope
            var jwtTokenService = Context.RequestServices.GetRequiredService<IJwtTokenService>();
            var userService = Context.RequestServices.GetRequiredService<IUserService>();

            // Get JWT token from Authorization header
            var authHeader = Request.Headers["Authorization"].FirstOrDefault();
            if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
            {
                return AuthenticateResult.NoResult();
            }

            var token = authHeader.Substring(7);

            // Validate the token
            var principal = jwtTokenService.ValidateToken(token);
            if (principal == null)
            {
                Logger.LogWarning("Invalid JWT token");
                return AuthenticateResult.Fail("Invalid token");
            }

            // Check if token is expired
            if (jwtTokenService.IsTokenExpired(token))
            {
                Logger.LogWarning("JWT token is expired");
                return AuthenticateResult.Fail("Token expired");
            }

            // Get username from token
            var username = jwtTokenService.GetUsernameFromToken(token);
            if (string.IsNullOrEmpty(username))
            {
                Logger.LogWarning("Username not found in token");
                return AuthenticateResult.Fail("Username not found in token");
            }

            // Load user details
            var user = await userService.GetUserByUsernameAsync(username);
            if (user == null)
            {
                Logger.LogWarning("User not found: {Username}", username);
                return AuthenticateResult.Fail("User not found");
            }

            // Check if user is active
            if (!user.IsActive)
            {
                Logger.LogWarning("User is inactive: {Username}", username);
                return AuthenticateResult.Fail("User is inactive");
            }

            Logger.LogDebug("Authentication successful for user: {Username}", username);

            var ticket = new AuthenticationTicket(principal, JwtBearerDefaults.AuthenticationScheme);
            return AuthenticateResult.Success(ticket);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Authentication failed");
            return AuthenticateResult.Fail("Authentication failed");
        }
    }
}
