using System.ComponentModel.DataAnnotations;

namespace CMS.WebApi.Models.Requests;

public class CreateComponentComponentRequest
{
    [Required]
    public int ParentComponentId { get; set; }

    [Required]
    public int ChildComponentId { get; set; }

    public int? DisplayPreference { get; set; }

    public bool IsRepeatable { get; set; } = false;

    public bool IsActive { get; set; } = true;

    public string? AdditionalInformation { get; set; }
}
