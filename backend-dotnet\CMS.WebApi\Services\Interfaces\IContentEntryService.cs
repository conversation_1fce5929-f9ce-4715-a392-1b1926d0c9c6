using CMS.WebApi.Models.Entities;

namespace CMS.WebApi.Services.Interfaces;

public interface IContentEntryService
{
    Task<IEnumerable<ContentEntry>> GetAllContentEntriesAsync();
    Task<ContentEntry?> GetContentEntryByIdAsync(int id);
    Task<IEnumerable<ContentEntry>> GetContentEntriesByCollectionAsync(int collectionId);
    Task<ContentEntry> CreateContentEntryAsync(ContentEntry contentEntry);
    Task<ContentEntry> UpdateContentEntryAsync(int id, ContentEntry contentEntry);
    Task DeleteContentEntryAsync(int id);
    Task<IEnumerable<ContentEntry>> SearchContentEntriesAsync(int collectionId, string searchText);
    Task<IEnumerable<ContentEntry>> SearchContentEntriesAsync(string query, int? collectionId = null);
    Task<(IEnumerable<ContentEntry> Items, int TotalCount)> GetPagedContentEntriesAsync(
        int collectionId, int pageNumber, int pageSize, string? searchText = null);
}
