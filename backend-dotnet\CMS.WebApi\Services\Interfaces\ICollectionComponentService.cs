using CMS.WebApi.Models.Entities;

namespace CMS.WebApi.Services.Interfaces;

public interface ICollectionComponentService
{
    /// <summary>
    /// Get collection component by ID
    /// </summary>
    /// <param name="id">Collection component ID</param>
    /// <returns>Collection component</returns>
    Task<CollectionComponent?> GetCollectionComponentByIdAsync(int id);

    /// <summary>
    /// Get all collection components
    /// </summary>
    /// <returns>List of all collection components</returns>
    Task<IEnumerable<CollectionComponent>> GetAllCollectionComponentsAsync();

    /// <summary>
    /// Get collection components by collection ID
    /// </summary>
    /// <param name="collectionId">Collection ID</param>
    /// <returns>List of collection components for the collection</returns>
    Task<IEnumerable<CollectionComponent>> GetCollectionComponentsByCollectionIdAsync(int collectionId);

    /// <summary>
    /// Create a new collection component
    /// </summary>
    /// <param name="collectionComponent">Collection component to create</param>
    /// <returns>Created collection component</returns>
    Task<CollectionComponent> CreateCollectionComponentAsync(CollectionComponent collectionComponent);

    /// <summary>
    /// Update an existing collection component
    /// </summary>
    /// <param name="id">Collection component ID</param>
    /// <param name="collectionComponent">Updated collection component data</param>
    /// <returns>Updated collection component</returns>
    Task<CollectionComponent> UpdateCollectionComponentAsync(int id, CollectionComponent collectionComponent);

    /// <summary>
    /// Update display preference for a collection component
    /// </summary>
    /// <param name="id">Collection component ID</param>
    /// <param name="displayPreference">New display preference</param>
    /// <returns>Updated collection component</returns>
    Task<CollectionComponent> UpdateDisplayPreferenceAsync(int id, int displayPreference);

    /// <summary>
    /// Delete a collection component
    /// </summary>
    /// <param name="id">Collection component ID</param>
    /// <returns>Task</returns>
    Task DeleteCollectionComponentAsync(int id);

    /// <summary>
    /// Get the next available ID for a collection component
    /// </summary>
    /// <returns>Next available ID</returns>
    Task<int> GetNextAvailableIdAsync();

    /// <summary>
    /// Reorder collection components for a collection
    /// </summary>
    /// <param name="collectionId">Collection ID</param>
    /// <param name="componentIds">List of component IDs in the desired order</param>
    /// <returns>Task</returns>
    Task ReorderCollectionComponentsAsync(int collectionId, List<int> componentIds);
}
