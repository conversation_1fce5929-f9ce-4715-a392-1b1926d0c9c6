using Microsoft.AspNetCore.Mvc;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Models.Responses;
using CMS.WebApi.Services.Interfaces;

namespace CMS.WebApi.Controllers;

[ApiController]
[Route("api/public")]
[Tags("Public API")]
public class PublicController : ControllerBase
{
    private readonly ICollectionService _collectionService;
    private readonly IContentEntryService _contentEntryService;
    private readonly ILogger<PublicController> _logger;

    public PublicController(
        ICollectionService collectionService,
        IContentEntryService contentEntryService,
        ILogger<PublicController> logger)
    {
        _collectionService = collectionService;
        _contentEntryService = contentEntryService;
        _logger = logger;
    }

    /// <summary>
    /// Get all collections (public endpoint)
    /// </summary>
    /// <returns>List of all collections</returns>
    [HttpGet("collections")]
    [ProducesResponseType(typeof(IEnumerable<CollectionListing>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<IEnumerable<CollectionListing>>> GetAllCollections()
    {
        try
        {
            var collections = await _collectionService.GetAllCollectionsAsync();
            
            if (!collections.Any())
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = "No collections found",
                    Path = Request.Path
                });
            }

            _logger.LogInformation("Retrieved {Count} collections via public API", collections.Count());
            return Ok(collections);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve collections via public API");
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving collections",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get collection by ID (public endpoint)
    /// </summary>
    /// <param name="id">Collection ID</param>
    /// <returns>Collection details</returns>
    [HttpGet("collections/{id}")]
    [ProducesResponseType(typeof(CollectionListing), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<CollectionListing>> GetCollectionById(int id)
    {
        try
        {
            var collection = await _collectionService.GetCollectionByIdAsync(id);
            
            if (collection == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Collection with ID {id} not found",
                    Path = Request.Path
                });
            }

            return Ok(collection);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve collection with ID: {CollectionId} via public API", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving the collection",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get collection by API ID (public endpoint)
    /// </summary>
    /// <param name="apiId">Collection API ID</param>
    /// <returns>Collection details</returns>
    [HttpGet("collections/api/{apiId}")]
    [ProducesResponseType(typeof(CollectionListing), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<CollectionListing>> GetCollectionByApiId(string apiId)
    {
        try
        {
            var collection = await _collectionService.GetCollectionByApiIdAsync(apiId);
            
            if (collection == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Collection with API ID '{apiId}' not found",
                    Path = Request.Path
                });
            }

            return Ok(collection);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve collection with API ID: {ApiId} via public API", apiId);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving the collection",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get content entries for a collection (public endpoint)
    /// </summary>
    /// <param name="collectionId">Collection ID</param>
    /// <returns>List of content entries</returns>
    [HttpGet("collections/{collectionId}/entries")]
    [ProducesResponseType(typeof(IEnumerable<ContentEntry>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<IEnumerable<ContentEntry>>> GetContentEntriesByCollection(int collectionId)
    {
        try
        {
            // First check if collection exists
            var collection = await _collectionService.GetCollectionByIdAsync(collectionId);
            if (collection == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Collection with ID {collectionId} not found",
                    Path = Request.Path
                });
            }

            var contentEntries = await _contentEntryService.GetContentEntriesByCollectionAsync(collectionId);
            
            _logger.LogInformation("Retrieved {Count} content entries for collection {CollectionId} via public API", 
                contentEntries.Count(), collectionId);
            return Ok(contentEntries);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve content entries for collection: {CollectionId} via public API", collectionId);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving content entries",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Get content entry by ID (public endpoint)
    /// </summary>
    /// <param name="id">Content entry ID</param>
    /// <returns>Content entry details</returns>
    [HttpGet("content-entries/{id}")]
    [ProducesResponseType(typeof(ContentEntry), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ContentEntry>> GetContentEntryById(int id)
    {
        try
        {
            var contentEntry = await _contentEntryService.GetContentEntryByIdAsync(id);
            
            if (contentEntry == null)
            {
                return NotFound(new ErrorResponse
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = $"Content entry with ID {id} not found",
                    Path = Request.Path
                });
            }

            return Ok(contentEntry);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve content entry with ID: {ContentEntryId} via public API", id);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while retrieving the content entry",
                Path = Request.Path
            });
        }
    }

    /// <summary>
    /// Search content entries (public endpoint)
    /// </summary>
    /// <param name="query">Search query</param>
    /// <param name="collectionId">Optional collection ID to filter by</param>
    /// <returns>List of matching content entries</returns>
    [HttpGet("search")]
    [ProducesResponseType(typeof(IEnumerable<ContentEntry>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<ContentEntry>>> SearchContentEntries(
        [FromQuery] string query,
        [FromQuery] int? collectionId = null)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(query))
            {
                return BadRequest(new ErrorResponse
                {
                    Status = 400,
                    Error = "Bad Request",
                    Message = "Search query is required",
                    Path = Request.Path
                });
            }

            var contentEntries = await _contentEntryService.SearchContentEntriesAsync(query, collectionId);
            
            _logger.LogInformation("Search returned {Count} content entries for query: {Query}", 
                contentEntries.Count(), query);
            return Ok(contentEntries);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to search content entries with query: {Query}", query);
            return StatusCode(500, new ErrorResponse
            {
                Status = 500,
                Error = "Internal Server Error",
                Message = "An error occurred while searching content entries",
                Path = Request.Path
            });
        }
    }
}
