using Microsoft.EntityFrameworkCore;
using CMS.WebApi.Data;
using CMS.WebApi.Models.Entities;
using CMS.WebApi.Services.Interfaces;

namespace CMS.WebApi.Services.Implementations;

public class ClientService : IClientService
{
    private readonly CmsDbContext _context;
    private readonly ILogger<ClientService> _logger;

    public ClientService(CmsDbContext context, ILogger<ClientService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<IEnumerable<Client>> GetAllClientsAsync()
    {
        return await _context.Clients.ToListAsync();
    }

    public async Task<Client?> GetClientByIdAsync(int id)
    {
        return await _context.Clients.FindAsync(id);
    }

    public async Task<Client?> GetClientByNameAsync(string name)
    {
        return await _context.Clients.FirstOrDefaultAsync(c => c.Name == name);
    }

    public async Task<Client> CreateClientAsync(Client client)
    {
        client.CreatedAt = DateTime.UtcNow;
        _context.Clients.Add(client);
        await _context.SaveChangesAsync();
        return client;
    }

    public async Task<Client> UpdateClientAsync(int id, Client client)
    {
        var existingClient = await _context.Clients.FindAsync(id);
        if (existingClient == null)
            throw new ArgumentException($"Client with ID {id} not found");

        existingClient.Name = client.Name;
        existingClient.ModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return existingClient;
    }

    public async Task DeleteClientAsync(int id)
    {
        var client = await _context.Clients.FindAsync(id);
        if (client != null)
        {
            _context.Clients.Remove(client);
            await _context.SaveChangesAsync();
        }
    }

    public async Task<bool> ClientExistsAsync(string name)
    {
        return await _context.Clients.AnyAsync(c => c.Name == name);
    }
}
